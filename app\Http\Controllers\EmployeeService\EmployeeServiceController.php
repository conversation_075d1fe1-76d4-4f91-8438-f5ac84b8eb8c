<?php

namespace App\Http\Controllers\EmployeeService;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\EmployeeService;
use Illuminate\Http\Request;

class EmployeeServiceController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('employeeservice','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $employeeservice = EmployeeService::where('salon_service_id', 'LIKE', "%$keyword%")
                ->orWhere('employee_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $employeeservice = EmployeeService::paginate($perPage);
            }

            return view('employeeService.employee-service.index', compact('employeeservice'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('employeeservice','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('employeeService.employee-service.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('employeeservice','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            EmployeeService::create($requestData);
            return redirect('employeeService/employee-service')->with('flash_message', 'EmployeeService added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('employeeservice','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $employeeservice = EmployeeService::findOrFail($id);
            return view('employeeService.employee-service.show', compact('employeeservice'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('employeeservice','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $employeeservice = EmployeeService::findOrFail($id);
            return view('employeeService.employee-service.edit', compact('employeeservice'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('employeeservice','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $employeeservice = EmployeeService::findOrFail($id);
             $employeeservice->update($requestData);

             return redirect('employeeService/employee-service')->with('flash_message', 'EmployeeService updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('employeeservice','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            EmployeeService::destroy($id);

            return redirect('employeeService/employee-service')->with('flash_message', 'EmployeeService deleted!');
        }
        return response(view('403'), 403);

    }
}
