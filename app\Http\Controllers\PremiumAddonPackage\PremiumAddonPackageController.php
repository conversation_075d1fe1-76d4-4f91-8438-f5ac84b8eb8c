<?php

namespace App\Http\Controllers\PremiumAddonPackage;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\PremiumAddonPackage;
use Illuminate\Http\Request;

class PremiumAddonPackageController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('premiumaddonpackage','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $premiumaddonpackage = PremiumAddonPackage::where('title', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->orWhere('price', 'LIKE', "%$keyword%")
                ->orWhere('type', 'LIKE', "%$keyword%")
                ->orWhere('no_of_users', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $premiumaddonpackage = PremiumAddonPackage::paginate($perPage);
            }

            return view('premiumAddonPackage.premium-addon-package.index', compact('premiumaddonpackage'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('premiumaddonpackage','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('premiumAddonPackage.premium-addon-package.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('premiumaddonpackage','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            $requestData = $request->all();
            $requestData['description'] = json_encode($request->description);
            PremiumAddonPackage::create($requestData);
            return redirect()->back()->with('flash_message', 'PremiumAddonPackage added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('premiumaddonpackage','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $premiumaddonpackage = PremiumAddonPackage::findOrFail($id);
            return view('premiumAddonPackage.premium-addon-package.show', compact('premiumaddonpackage'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('premiumaddonpackage','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $premiumaddonpackage = PremiumAddonPackage::findOrFail($id);
            return view('premiumAddonPackage.premium-addon-package.edit', compact('premiumaddonpackage'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('premiumaddonpackage','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $requestData = $request->all();
            $requestData['description'] = json_encode($request->description);
            $premiumaddonpackage = PremiumAddonPackage::findOrFail($id);
            $premiumaddonpackage->update($requestData);
            return redirect()->back()->with('flash_message', 'PremiumAddonPackage updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('premiumaddonpackage','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            PremiumAddonPackage::destroy($id);

            return redirect('premiumAddonPackage/premium-addon-package')->with('flash_message', 'PremiumAddonPackage deleted!');
        }
        return response(view('403'), 403);

    }
}
