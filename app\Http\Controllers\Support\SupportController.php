<?php

namespace App\Http\Controllers\Support;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Support;
use App\SupportCategory;
use App\User;
use Auth;
use Mail;
use Illuminate\Http\Request;

class SupportController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('support','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25000000;
            if(Auth::user()->hasRole('spa_salon')) {
                if (!empty($keyword)) {
                    $support = Support::where('salon_id', 'LIKE', "%$keyword%")
                        ->orWhere('description', 'LIKE', "%$keyword%")
                        ->orWhere('status', 'LIKE', "%$keyword%")
                        ->paginate($perPage);
                } else {
                    $support = Support::where('salon_id',Auth::id())->paginate($perPage);
                    $supportCategory = SupportCategory::get();
                }
            }else{
                if (!empty($keyword)) {
                    $support = Support::where('salon_id', 'LIKE', "%$keyword%")
                        ->orWhere('description', 'LIKE', "%$keyword%")
                        ->orWhere('status', 'LIKE', "%$keyword%")
                        ->paginate($perPage);
                } else {
                    $support = Support::paginate($perPage);
                    $supportCategory = SupportCategory::get();
                }
            }
            return view('support.support.index', compact('support','supportCategory'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('support','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('support.support.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('support','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            $requestData = $request->all();
            $supports = Support::count();
            $newCount = $supports + 1;
            $categoryCount = str_pad($request->support_category_id, 3, '0', STR_PAD_LEFT);
            $formattedCounter = str_pad($newCount, 3, '0', STR_PAD_LEFT);
            $requestData['support_id'] = "SC-$categoryCount-$formattedCounter";
            $support = Support::create($requestData);
            $salonName = $support->salon->name;
            $salonEmail = $support->salon->email;
            $salonPicture = $support->salon->profile->pic;
            $support_category_id = $support->supportCategory->name;
            $description = $support->description;
            $support_id = $support->support_id;
            $admin = User::findOrFail(2);
            $adminEmail = $admin->email;
            $data = array(
                'name' => $salonName,
                'email' => $salonEmail,
                'adminName' => "Liink",
                'support_id' => $support_id,
                'support_category_id' => $support->supportCategory->name,
                'adminEmail' => $adminEmail,
                'description' => $description,
                'salon_picture' => $salonPicture,
                'welcome_message' => 'Welcome',
                'information_message' => 'Support Request',
                'detail' => env('APP_URL'),
                'login_url' => env('APP_URL'),
                'site_url' => env('APP_URL'),
            );
            Mail::send('website.email_templates.support_admin_email',['data'=>$data],function($message) use($data){
                $message->to($data['adminEmail'], $data['adminName'])->cc('<EMAIL>', 'Usman Dev')->subject('Support Request');;
            });
            return redirect('support/support')->with('flash_message', 'Support added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('support','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $support = Support::findOrFail($id);
            return view('support.support.show', compact('support'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('support','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $support = Support::findOrFail($id);
            return view('support.support.edit', compact('support'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('support','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $support = Support::findOrFail($id);
             $support->update($requestData);

             return redirect('support/support')->with('flash_message', 'Support updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('support','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Support::destroy($id);

            return redirect('support/support')->with('flash_message', 'Support deleted!');
        }
        return response(view('403'), 403);

    }
}
