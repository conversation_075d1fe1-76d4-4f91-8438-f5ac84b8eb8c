<?php

namespace App\Http\Controllers\StockOut;

use App\CustomerAppointment;
use App\CustomNotification;
use App\EmployeeService;
use App\Expense;
use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Product;
use App\ProductInventory;
use App\Profile;
use App\PurchaseOrder;
use App\SalonService;
use App\StockOut;
use App\Revenue;
use App\Supplier;
use App\User;
use App\UserSubscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Auth;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use DB;

class StockOutController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('stockout','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25000000;
            if (!empty($keyword)) {
                $stockout = StockOut::where('employee_id', 'LIKE', "%$keyword%")
                    ->orWhere('client_name', 'LIKE', "%$keyword%")
                    ->orWhere('product_id', 'LIKE', "%$keyword%")
                    ->orWhere('price_per_product', 'LIKE', "%$keyword%")
                    ->orWhere('quantity', 'LIKE', "%$keyword%")
                    ->orWhere('total_price_per_product', 'LIKE', "%$keyword%")
                    ->orWhere('date', 'LIKE', "%$keyword%")
                    ->orWhere('notes', 'LIKE', "%$keyword%")
                    ->orWhere('total_amount_without_vat', 'LIKE', "%$keyword%")
                    ->orWhere('total_amount_with_vat', 'LIKE', "%$keyword%")
                    ->orWhere('vat', 'LIKE', "%$keyword%")
                    ->orWhere('salon_id', 'LIKE', "%$keyword%")
                    ->paginate($perPage);
            } else {
                if(Auth::user()->hasRole('spa_salon')){
                    if ($request->branch_id != null){
                        $branches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('id', $request->branch_id)->get();
                        $branchIds = $branches->pluck('id');
                        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                            ->orderBy('user_id', 'ASC')
                            ->orderBy('id', 'DESC')
                            ->get()
                            ->groupBy('user_id');
                        $currentDate = now();
                        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                            $dueDate = $subs->first()->fatoraPdf->due_date;
                            return $dueDate->isPast();
                        })->keys();
                        $employees = User::whereHas(
                            'roles', function($q){
                            $q->where('name', 'employee');
                        }
                        )->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->orderBy('id','DESC')->paginate($perPage);
                        $stockout = StockOut::where('salon_id',Auth::user()->salon_id)->get();
                        $purchaseOrders = PurchaseOrder::where('salon_id', Auth::user()->salon_id)->orderBy('id','DESC')->get();
                        $totalQuantity = 0;
                        foreach ($purchaseOrders as $purchaseOrder) {
                            foreach ($purchaseOrder->stockOuts as $stockOut) {
                                if ($stockOut->status != 'refund') {
                                    $totalQuantity += $stockOut->quantity;
                                }
                            }
                        }
                        $revenue = PurchaseOrder::where('salon_id', Auth::user()->salon_id)->where('status', '!=', 'expire')->get();
                        $totalRevenue=$revenue->sum('total_amount_with_vat');
                        $totalCost = $stockout->sum('cost_price_per_product');
                        $netProfit = $totalRevenue - $totalCost;
                        if ($totalRevenue >0){
                            $netMargin = ($netProfit /$totalRevenue) * 100;
                        }else{
                            $netMargin=0;
                        }
                        $products = Product::where('is_deleted','1')->where('product_type_id','2')->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->get();
                        $totalStockIns = $products->sum(function ($product) {
                            return $product->productInventories->sum('quantity')+$product->productInventories->sum('consumed_quantity');
                        });
                        $shopSuppliers = Supplier::where('salon_id',Auth::user()->salon_id);
                        $totalSuppliers = $shopSuppliers->count();
                        $suppliers = $shopSuppliers->get();
                    }else {
                        $allBranches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
                        $branchIds = $allBranches->pluck('id');
                        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                            ->orderBy('user_id', 'ASC')
                            ->orderBy('id', 'DESC')
                            ->get()
                            ->groupBy('user_id');
                        $currentDate = now();
                        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                            $dueDate = $subs->first()->fatoraPdf->due_date;
                            return $dueDate->isPast();
                        })->keys();
                        $employees = User::whereHas(
                            'roles', function ($q) {
                            $q->where('name', 'employee');
                        }
                        )->whereIn('salon_id', $branchIds)->whereNotIn('salon_id', $expiredUserIds)->orderBy('id', 'DESC')->get();
                        $stockout = StockOut::whereIn('salon_id',$branchIds)->get();
                        $purchaseOrders = PurchaseOrder::whereIn('salon_id', $branchIds)->orderBy('id','DESC')->get();
                        $totalQuantity = 0;
                        foreach ($purchaseOrders as $purchaseOrder) {
                            foreach ($purchaseOrder->stockOuts as $stockOut) {
                                if ($stockOut->status != 'refund') {
                                    $totalQuantity += $stockOut->quantity;
                                }
                            }
                        }
                        $revenue = PurchaseOrder::whereIn('salon_id', $branchIds)->where('status', '!=', 'expire')->get();
                        $totalRevenue=$revenue->sum('total_amount_with_vat');
                        $totalCost = $stockout->sum('cost_price_per_product');
                        $netProfit = $totalRevenue - $totalCost;
                        if ($totalRevenue > 0){
                            $netMargin = ($netProfit /$totalRevenue) * 100;
                        }else{
                            $netMargin = 0;
                        }
                        $products = Product::where('is_deleted','1')->where('product_type_id','2')->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->get();
                        $totalStockIns = $products->sum(function ($product) {
                            return $product->productInventories->sum('quantity')+$product->productInventories->sum('consumed_quantity');
                        });
                        $shopSuppliers = Supplier::whereIn('salon_id',$branchIds);
                        $totalSuppliers = $shopSuppliers->count();
                        $suppliers = $shopSuppliers->get();
                    }
                }else{
                    $allBranches = User::whereHas('roles', function ($query) {
                        $query->where('name', 'spa_salon');
                    })->where('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
                    $branchIds = $allBranches->pluck('id');
                    $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                        ->orderBy('user_id', 'ASC')
                        ->orderBy('id', 'DESC')
                        ->get()
                        ->groupBy('user_id');
                    $currentDate = now();
                    $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                        $dueDate = $subs->first()->fatoraPdf->due_date;
                        return $dueDate->isPast();
                    })->keys();
                    $employees = User::whereHas(
                        'roles', function ($q) {
                        $q->where('name', 'employee');
                    }
                    )->whereIn('salon_id', $branchIds)->whereNotIn('salon_id', $expiredUserIds)->orderBy('id', 'DESC')->get();
                    $stockout = StockOut::whereIn('salon_id',$branchIds)->get();
                    $purchaseOrders = PurchaseOrder::whereIn('salon_id', $branchIds)->orderBy('id','DESC')->get();
                    $totalQuantity = 0;
                    foreach ($purchaseOrders as $purchaseOrder) {
                        foreach ($purchaseOrder->stockOuts as $stockOut) {
                            if ($stockOut->status != 'refund') {
                                $totalQuantity += $stockOut->quantity;
                            }
                        }
                    }
                    $revenue = PurchaseOrder::whereIn('salon_id', $branchIds)->where('status', '!=', 'expire')->get();
                    $totalRevenue=$revenue->sum('total_amount_with_vat');
                    $totalCost = $stockout->sum('cost_price_per_product');
                    $netProfit = $totalRevenue - $totalCost;
                    if ($totalRevenue > 0){
                        $netMargin = ($netProfit /$totalRevenue) * 100;
                    }else{
                        $netMargin = 0;
                    }
                    $products = Product::where('is_deleted','1')->where('product_type_id','2')->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->get();
                    $totalStockIns = $products->sum(function ($product) {
                        return $product->productInventories->sum('quantity')+$product->productInventories->sum('consumed_quantity');
                    });
                    $shopSuppliers = Supplier::whereIn('salon_id',$branchIds);
                    $totalSuppliers = $shopSuppliers->count();
                    $suppliers = $shopSuppliers->get();
                }
            }
            return view('stockOut.stock-out.index', compact('stockout','employees','products','purchaseOrders','netProfit','netMargin','totalStockIns','totalSuppliers','totalQuantity','suppliers'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('stockout','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('stockOut.stock-out.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('stockout','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            extract($request->all());
            if(isset($sales) && $sales == "sale_products") {
                $phoneDefault = str_replace(' ', '', $request->phone);
                $random_password = rand('********', '********');
                $email = $client_name . time() . "@gmail.com";
                $user = User::create(['name' => $client_name??"",'email'=>$email??"",'password' => bcrypt($random_password),'customer_type_id' => "1", 'phone' => $phoneDefault??"", 'show_password' => $random_password]);
                Profile::create(['user_id' => $user->id, 'address' => $client_address]);
                if($user != null) {
                    $purchaseOrder = PurchaseOrder::create(["user_id"=>$user->id??"","salon_id"=>Auth::user()->salon_id??"","notes"=>$notes??"","vat"=>$vat??"","total_amount_with_vat"=>$overall_price_with_vat??"","total_amount_without_vat"=> $overall_price_without_vat??"",'date'=>$date??"",'status'=>"sales",'total_quantity'=>$totalQuantity??""]);
                    $dataForNotification = [];
                    if (!empty($sku_id)) {
                        if (is_array($sku_id)) {
                            foreach ($sku_id as $key => $value) {
                                $requiredQuantity = $quantity[$key] ?? $quantity;
                                $productInventories = ProductInventory::where('sku_id', $value)
                                    ->where('quantity', '>', 0)
                                    ->orderBy('created_at', 'asc')
                                    ->get();
                                foreach ($productInventories as $inventory) {
                                    $inventory->consumed_quantity = is_numeric($inventory->consumed_quantity) ? $inventory->consumed_quantity : 0;
                                    if ($requiredQuantity <= 0) {
                                        break;
                                    }
                                    if ($inventory->quantity >= $requiredQuantity) {
                                        $inventory->quantity -= $requiredQuantity;
                                        $inventory->consumed_quantity += $requiredQuantity;
                                        $inventory->save();
                                        $expense = Expense::where('product_inventory_id',$inventory->id)->where('saloon_id',auth()->user()->salon_id)->first();
                                        if($expense != null){
                                            $totalCostWithVat = $inventory->per_cost_price * $requiredQuantity;
                                            $totalAmount = $expense->total_amount - $totalCostWithVat;
                                            $vatPercentage = $inventory->vat;
                                            $amount = $totalAmount / (1 + ($vatPercentage / 100));
                                            $expense->update([
                                                'amount' => $amount,
                                                'total_amount' => $totalAmount,
                                            ]);
                                        }
                                        $requiredQuantity = 0;
                                    } else {
                                        $inventory->consumed_quantity += $inventory->quantity;
                                        $requiredQuantity -= $inventory->quantity;
                                        $inventory->quantity = 0;
                                        $inventory->save();
                                    }
                                }
                                $product = ProductInventory::where('sku_id', $value)->latest()->first();
                                $productTitle = $product->products->title ?? 'Unknown Product';
                                $remainingQuantity = $product->quantity ?? 0;
                                $soldQuantity = $quantity[$key] ?? 0;
                                $unitPrice = $cost_price[$key] ?? 0;
                                $total_cost_price_per_product=$per_cost_price[$key] * $quantity[$key];
                                StockOut::create([
                                    'product_id' => $inventory->product_id,
                                    'quantity' => $quantity[$key] ?? $quantity,
                                    'price_per_product' => $cost_price[$key] ?? $cost_price,
                                    'total_price_per_product' => $total_price[$key] ?? $total_price,
                                    'cost_price_per_product'=>$total_cost_price_per_product ??"",
                                    'salon_id' => Auth::user()->salon_id??"",
                                    'purchase_order_id' => $purchaseOrder->id??"",
                                    'sku_id'=>$sku_id[$key]??$sku_id,
                                ]);
//                                $dataForNotification = [
//                                    'client_name'        => $client_name,
//                                    'product'            => $productTitle,
//                                    'sku_id'             => $value,
////                                    'quantity_sold'      => $soldQuantity,
////                                    'quantity_remaining' => $remainingQuantity,
//                                    'unit_price'         => $unitPrice,
//                                    'type'             => 'clientSaleProducts',
//                                ];
                                $dataForNotification = [
                                    'client_name'        => $client_name,
                                    'product'            => $productTitle,
                                    'sku_id'             => $value,
//                                    'quantity_sold'      => $soldQuantity,
//                                    'quantity_remaining' => $remainingQuantity,
                                    'unit_price'         => $unitPrice,
                                    'type'               =>'stockOutServices',
                                    'template'           => 'clientSaleProducts',
                                ];
                                CustomNotification::create([
                                    'notifiable_id'     => Auth::user()->salon_id??"",
                                    'notifiable_type'   => 'App\User',
                                    'type'              => 'InventoryProduct',
                                    'data'              => $dataForNotification,
                                ]);
                            }
                            Revenue::create([
                                'saloon_id' => Auth::user()->salon_id??"",
                                'purchase_order_id' => $purchaseOrder->id??"",
                                'name' => $client_name??"",
                                'email'=>$email??"",
                                'description' => $notes??"",
                                'total_amount' => $request->overall_price_with_vat??"",
                                'tax' => $vat??"",
                                'amount' => $request->overall_price_without_vat??"",
                                'date' => $date,
                                'vendor' => $purchaseOrder->salon->name??"",
                                'client_type' => 'Sales',
                            ]);
                        }
                    }
                }
            }else if(isset($sales) == "retain_products"){
                if(isset($request->sku_id) && $request->sku_id != null){
                    foreach ($request->sku_id as $key => $item){
                        $product = ProductInventory::where('sku_id',$item)->first();
                        $updatePriceWithVat = $product->per_cost_price * $request->quantity[$key];
                        $updatePriceWithOutVat = $updatePriceWithVat / (1 + ($product->vat / 100));
                        $product->consumed_quantity += $request->quantity[$key];
                        $product->quantity -= $request->quantity[$key];
                        $product->save();
                        $purchaseOrder = PurchaseOrder::create(['salon_id'=>Auth::user()->salon_id,'notes'=>'Refund','date'=>date('Y-m-d'),'status'=>'refund','total_quantity'=>$request->quantity[$key],'vat'=>$product->vat,'total_amount_with_vat'=>$updatePriceWithVat,'total_amount_without_vat'=>$updatePriceWithOutVat]);

                        $stockOut = StockOut::create([
                            'product_id' => $product->product_id??"",
                            'quantity' => $request->quantity[$key]??"",
                            'price_per_product' => $product->price??"",
                            'total_price_per_product' => $updatePriceWithVat??"",
                            'cost_price_per_product'=> $updatePriceWithOutVat ??"",
                            'salon_id' => Auth::user()->salon_id??"",
                            'purchase_order_id' => $purchaseOrder->id??"",
                            'sku_id'=>$product->sku_id??"",
                        ]);
                        if ($product->attachment != null) {
                            $attachment = Storage::disk('website')->put('revenue_attachments', $product->attachment);
                        } else {
                            $attachment = (NULL);
                        }
                        $revenue = Revenue::create([
                            'saloon_id' => $purchaseOrder->salon->id??"",
                            'purchase_order_id' => $purchaseOrder->id??"",
                            'name' => $product->getSupplier->name??"",
                            'email'=> $product->getSupplier->email??"",
                            'total_amount' => $purchaseOrder->total_amount_with_vat??"",
                            'tax' => $purchaseOrder->vat??"",
                            'amount' => $purchaseOrder->total_amount_without_vat??"",
                            'date' => date('Y-m-d'),
                            'vendor' => $purchaseOrder->salon->name??"",
                            'attachments' => $attachment,
                            'description' => ("Product Name: ") . ($product->products->title ?? "")  . ("; Product SKU ID: ") . ($item ?? "") . ("; Supplier Name: ") . ($product->getSupplier->name ?? "") .(";"),
                        ]);

//                        $data = [
//                            'stockOut' => $stockOut,
//                            'revenue' => $revenue,
//                        ];
                        if($stockOut && $revenue){
                        $data = [
                                'supplierName'    => $revenue->name,
                                'productName'     => $product->products->title,
                                'productSku'      => $stockOut->sku_id,
                                'productQuantity' => $stockOut->quantity,
                                'totalAmount'     => $revenue->total_amount,
                                'vendor'          => $revenue->vendor,
                                'type'            =>'stockOutServices',
                                'template'        => 'Retain_StockOut',
                        ];
//                        \Log::info('Retain StockOut', $data);
                        $custom = CustomNotification::create(
                            [
                                'notifiable_id'   => $stockOut->salon_id,
                                'notifiable_type' => 'App\User',
                                    'type'            => 'InventoryProduct',
                                'data'            => $data ,
                            ]
                        );
                    }
                }
                }
            }else{
                $employee_ids = $request->employee_id;
                $product_ids = $request->product_id;
                $quantities = $request->quantity;
                $cost_prices = $request->cost_price;
                $sku_ids = $request->sku_id;
                $groupedByEmployee = [];
                foreach ($employee_ids as $key => $employee_id) {
                    if (!isset($groupedByEmployee[$employee_id])) {
                        $groupedByEmployee[$employee_id] = [
                            'products' => [],
                            'quantities' => [],
                            'cost_prices' => [],
                            'sku_ids' => [],
                            'total_quantity' => 0,
                            'total_price' => 0,
                        ];
                    }
                    $quantity = $quantities[$key];
                    $cost_price = $cost_prices[$key]/$quantity;
                    $total_price = $quantity * $cost_price;
                    $groupedByEmployee[$employee_id]['products'][] = $product_ids[$key];
                    $groupedByEmployee[$employee_id]['quantities'][] = $quantity;
                    $groupedByEmployee[$employee_id]['sku_ids'][] = $sku_ids[$key];
                    $groupedByEmployee[$employee_id]['cost_prices'][] = $cost_price;
                    $groupedByEmployee[$employee_id]['total_quantity'] += $quantity;
                    $groupedByEmployee[$employee_id]['total_price'] += $total_price;
                }
                foreach ($groupedByEmployee as $employee_id => $data) {
                    $employeeDetails = [
                        'employee_id' => $employee_id,
                        'employee_name' => User::find($employee_id)->name,
                        'products' => [],
                    ];
                    $purchaseOrder = PurchaseOrder::create([
                        'employee_id' => $employee_id??"",
                        'salon_id' => Auth::user()->salon_id??"",
                        'date' => $request->date??"",
                        'notes' => $request->notes??'',
                        'total_quantity' => $data['total_quantity']??"",
                        'total_amount_without_vat' => $data['total_price']??"",
                        'total_amount_with_vat'=>$data['total_price']??"",
                        'status' => "services"??"",
                    ]);
//                    $expense = Expense::create([
//                        'name' => 'Stock-Out Service',
//                        'purchase_order_id' => $purchaseOrder->id,
//                        'saloon_id' => Auth::user()->salon_id??"",
//                        'vendor' => Auth::user()->name??"",
//                        'expense_category_id' => '5',
//                        'amount' => $data['total_price']??"",
//                        'total_amount' => $data['total_price']??"",
//                        'date' => $request->date??"",
//                        'description' => $request->notes??"",
//                    ]);
                    foreach ($data['products'] as $index => $product_id) {
                        $quantity = $data['quantities'][$index];
                        $sku_id=$data['sku_ids'][$index];
                        $cost_price = $data['cost_prices'][$index];
                        $total_price = $quantity * $cost_price;
                        $total_cost_price_per_product=$cost_price * $quantity;
                        $stockOut= StockOut::create([
                            'product_id' => $product_id??"",
                            'quantity' => $quantity??"",
                            'price_per_product' => $cost_price??"",
                            'total_price_per_product' => $total_price??"",
                            'cost_price_per_product'=> $total_cost_price_per_product??"",
                            'salon_id' => Auth::user()->salon_id??"",
                            'purchase_order_id' => $purchaseOrder->id??"",
                            'sku_id'=>$sku_id??"",
                        ]);
                        $requiredQuantity = (float)$quantity;
                        $productInventories = ProductInventory::where('product_id', $product_id)
                            ->where('quantity', '>', 0)
                            ->orderBy('created_at', 'asc')
                            ->get();
                        $inventoryLogs = [];
                        foreach ($productInventories as $inventory) {
                            $inventory->consumed_quantity = is_numeric($inventory->consumed_quantity) ? $inventory->consumed_quantity : 0;
                            if ($requiredQuantity <= 0) {
                                break;
                            }
                            if ($inventory->quantity >= $requiredQuantity) {
                                $inventoryLogs[] = [
                                    'inventory_id' => $inventory->id,
                                    'from_quantity' => $inventory->quantity,
                                    'consumed' => $requiredQuantity,
                                ];
                                $inventory->quantity -= $requiredQuantity;
                                $inventory->consumed_quantity += $requiredQuantity;
                                $inventory->save();
                                $requiredQuantity = 0;
                            } else
                            {
                                $inventoryLogs[] = [
                                    'inventory_id' => $inventory->id,
                                    'from_quantity' => $inventory->quantity,
                                    'consumed' => $inventory->quantity,
                                ];
                                $inventory->consumed_quantity += $inventory->quantity;
                                $requiredQuantity -= $inventory->quantity;
                                $inventory->quantity = 0;
                                $inventory->save();
                            }
                            $productId = Product::find($product_id);
                            $isNearToOutOfStock = $productId->productInventories->sum('quantity') <= $productId->near_to_out_of_stock;
                            if($isNearToOutOfStock) {
                                CustomNotification::create(
                                    [
                                        'notifiable_id'   => $stockOut->salon_id,
                                        'notifiable_type' => 'App\User',
                                        'type'            => 'productNearToOutOfStock',
                                        'data'            => [
                                            'message'       => 'Product is near to out of stock.',
                                            'product_name'  => $productId->title,
                                            'sku_id'        => $sku_id,
                                            'quantity'      => $inventory->quantity,
                                            'type'          => 'productNearToOutOfStock'
                                        ],
                                    ]
                                );
                            }
                        }
//                        $employeeDetails['products'][] = [
//                            'product_id'          => $product_id,
//                            'product_name'        => Product::find($product_id)->title,
//                            'sku_id'              => $sku_id,
//                            'quantity'            => $quantity,
//                            'cost_price'          => $cost_price,
//                            'total_price'         => $total_price,
//                            'inventory_breakdown' => $inventoryLogs,
//                            'type'                => 'stockOutServices',
//                        ];
                        $employeeDetails = [
                            'product_name'        => $productId->title,
                            'employee_name'       => User::find($employee_id)->name,
                            'sku_id' => $sku_id,
                            'quantity' => $quantity,
                            'cost_price' => $cost_price,
                            'total_price' => $total_price,
                            'type'                => 'stockOutServices',
                            'template'            => 'employee'
//                            'inventory_breakdown' => $inventoryLogs,
                        ];
                    }

                    \Log::info('StockOut Service', $employeeDetails);
                    CustomNotification::create([
                        'notifiable_id'   => $stockOut->salon_id,
                        'notifiable_type' => 'App\User',
                        'type'            => 'InventoryProduct',
                        'data'            => $employeeDetails,
                    ]);
                    
                }
            }
            return redirect('stockOut/stock-out')->with('flash_message', 'StockOut added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('stockout','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            if(Auth::user()->hasRole('spa_salon')){
                $purchaseOrder = PurchaseOrder::where('id',$id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
            }else{
                $purchaseOrder = PurchaseOrder::where('id',$id)->where('salon_id', Auth::user()->salon_id)->firstOrFail();
            }
            return view('stockOut.stock-out.show', compact('purchaseOrder'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('stockout','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            if(Auth::user()->hasRole('spa_salon')){
                $purchaseOrder = PurchaseOrder::where('id',$id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
            }else{
                $purchaseOrder = PurchaseOrder::where('id',$id)->where('salon_id', Auth::user()->salon_id)->firstOrFail();
            }
            return view('stockOut.stock-out.edit', compact('purchaseOrder'));
        }
        return response(view('403'), 403);
    }
    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('stockout','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            extract($request->all());
            $user= User::findorfail($request->user_id);
            $user->update(['name'=>$user_name,'phone'=>$phone_number]);
            Profile::where('user_id',$request->user_id)->update(['address'=>$address,'phone'=>$phone_number]);
            $refundStock = $request->input('refund', []);
            $purchaseOrder = null;
            $refundTotalPrice = 0;
//            if($refundStock !=null)
            $stockouts = StockOut::whereIn('id', $refundStock)->get();
            foreach ($stockouts as $stockout) {
                $productInventry = ProductInventory::where('sku_id', $stockout->sku_id)->first();
                $productInventry->quantity += $stockout->quantity;
                $productInventry->consumed_quantity -= $stockout->quantity;
                $productInventry->save();
                $purchaseOrder = PurchaseOrder::where('id', $stockout->purchase_order_id)->first();
                $purchaseOrder->total_amount_without_vat -= $stockout->total_price_per_product;
                $vatAmount = ($purchaseOrder->vat / 100) * $stockout->total_price_per_product;
                $purchaseOrder->total_amount_with_vat -= $stockout->total_price_per_product + $vatAmount;
                $purchaseOrder->total_amount_without_vat = round($purchaseOrder->total_amount_without_vat, 2);
                $purchaseOrder->total_amount_with_vat = round($purchaseOrder->total_amount_with_vat, 2);
                if (abs($purchaseOrder->total_amount_without_vat) < 0.01) {
                    $purchaseOrder->total_amount_without_vat = 0;
                }
                $purchaseOrder->save();
                $refundTotalPrice += $stockout->total_price_per_product;
                StockOut::where('id', $stockout->id)->update(['status' => "refund"]);
            }
            if(isset($purchaseOrder) && $purchaseOrder != null){
                $lessPrice = $purchaseOrder->stockOuts->where('status','refund')->pluck('total_price_per_product')->sum();
                $vatPercentage = $purchaseOrder->vat;
                $vatPrice = ($lessPrice * $vatPercentage) / 100;
                $totalPrice = $lessPrice + $vatPrice;
//                $expenseTotalAmount = $refundTotalPrice + $vatPrice;
                $expense = Expense::updateOrCreate(['purchase_order_id' => $purchaseOrder->id],[
                    'name' => 'Stock-Out Refund',
                    'saloon_id' => $purchaseOrder->salon->id??"",
                    'vendor' => $purchaseOrder->salon->name??"",
                    'expense_category_id' => '7',
                    'amount' => $lessPrice??"",
                    'tax' => $purchaseOrder->vat,
                    'total_amount' => $totalPrice??"",
                    'date' => date('Y-m-d'),
                    'description' => "refund product",
                ]);
                if (Revenue::where('purchase_order_id',$purchaseOrder->id)->exists()){
                    if($purchaseOrder->total_amount_with_vat != "0"){
                        $revenue = Revenue::where('purchase_order_id', $purchaseOrder->id??"")
                            ->update([
                                'saloon_id' => $purchaseOrder->salon_id??"",
                                'description' => $notes??"",
                                'total_amount' => $purchaseOrder->total_amount_with_vat??"",
                                'tax' => $purchaseOrder->vat??"",
                                'amount' => $purchaseOrder->total_amount_without_vat??"",
                                'date' => date('Y-m-d'),
                            ]);
                    }else if($purchaseOrder->total_amount_with_vat == "0"){
                        $revenue = Revenue::where('purchase_order_id', $purchaseOrder->id??"")->delete();
                    }
                }
            }
            return redirect('stockOut/stock-out')->with('flash_message', 'StockOut updated!');
        }
        return response(view('403'), 403);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('stockout','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            StockOut::destroy($id);
            return redirect('stockOut/stock-out')->with('flash_message', 'StockOut deleted!');
        }
        return response(view('403'), 403);
    }
    public function productCostPrice(Request $request ,$id)
    {
        $currentDate = now();
        return $product = Product::where('product_type_id','2')->whereHas('productInventories', function ($query) use ($currentDate) {
            $query->where('expiry_date', '>=', $currentDate)
                ->where('quantity', '>', 0)
                ->orderBy('expiry_date', 'asc');
        })->with(['productInventories' => function($query) use ($currentDate) {
            $query->where('expiry_date', '>=', $currentDate)
                ->where('quantity', '>', 0)
                ->orderBy('expiry_date', 'asc');
        }])->find($id);

        return $nearestExpiryInventory = $product->productInventories->first();

        return response()->json($product);
    }
//    public function productExpire()
//    {
//        $liveDate = now()->format('Y-m-d');
//        $products = ProductInventory::where('quantity','>','0')->get();
//        foreach($products as $product)
//        {
//            $expireDate =$product->expiry_date;
//            if($liveDate == $expireDate)
//            {
//                $totalAmount=$product->quantity * $product->per_cost_price;
//                $purchaseOrder=PurchaseOrder::create(["salon_id"=>$product->products->salon_id,"notes"=>"expire products","total_amount_with_vat"=>$totalAmount,"total_amount_without_vat"=> $totalAmount,'date'=>$liveDate,'status'=>"expire",'total_quantity'=>$product->quantity]);
//                $productInventory=ProductInventory::findorfail($product->id);
//                $productInventory->quantity -= $product->quantity;
//                $productInventory->consumed_quantity += $product->quantity;
//                $productInventory->save();
//                StockOut::create(['product_id' => $product->product_id, 'quantity' => $product->quantity, 'price_per_product' => $product->per_cost_price, 'total_price_per_product' => $totalAmount, 'cost_price_per_product'=>$totalAmount ??"", 'salon_id' =>$product->products->salon_id, 'purchase_order_id' => $purchaseOrder->id,'sku_id'=>$product->sku_id]);
//            }
//        }
//        return "expire products out from the inventory..";
//    }
    public function stockOutFatoraPdf($id){
        $stockOutFatora = PurchaseOrder::findOrFail($id);
        if(empty($stockOutFatora->appointment_id)) {
            $customerFatora ="";
            $services = "";
        }else{
            $customerFatora = CustomerAppointment::findOrFail($stockOutFatora->appointment_id);
            $services = SalonService::where('salon_id', $customerFatora->salon_id)->get();
        }
        $qrCode = QrCode::size(300)->generate(json_encode($stockOutFatora));
        $qrCodeFinal = ($qrCode);
        return view('website.pdf.stock_out_fatora', compact('qrCodeFinal', 'stockOutFatora','services','customerFatora'));
    }
    public function stockOutStats(Request $request)
    {
        if ($request->branch_id != null) {
            $branches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('id', $request->branch_id)->get();
            $branchIds = $branches->pluck('id');
            $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                ->orderBy('user_id', 'ASC')
                ->orderBy('id', 'DESC')
                ->get()
                ->groupBy('user_id');
            $currentDate = now();
            $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                $dueDate = $subs->first()->fatoraPdf->due_date;
                return $dueDate->isPast();
            })->keys();
            $stockout = StockOut::where('salon_id', Auth::user()->salon_id)->get();
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $purchaseOrders = PurchaseOrder::where('salon_id', Auth::user()->salon_id)
                ->where(function ($query) use ($startDate, $endDate) {
                    $query->whereBetween(DB::raw("STR_TO_DATE(date, '%m/%d/%Y')"), [$startDate, $endDate])
                        ->orWhereBetween(DB::raw("DATE_FORMAT(date, '%Y-%m-%d')"), [$startDate, $endDate]);
                })
                ->get();
            $totalQuantity = 0;
            foreach ($purchaseOrders as $purchaseOrder) {
                foreach ($purchaseOrder->stockOuts as $stockOut) {
                    if ($stockOut->status != 'refund') {
                        $totalQuantity += $stockOut->quantity;
                    }
                }
            }
            $products = Product::where('product_type_id','2')->whereIn('salon_id', $branchIds)
                ->whereNotIn('salon_id', $expiredUserIds)
                ->whereHas('productInventories', function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('created_at', [$startDate, $endDate]);
                })->get();
            $totalStockIns = $products->sum(function ($product) use ($startDate, $endDate) {
                $filteredInventories = $product->productInventories()
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->get();
                $totalQuantity = $filteredInventories->sum('quantity');
                $totalConsumedQuantity = $filteredInventories->sum('consumed_quantity');
                return $totalQuantity + $totalConsumedQuantity;
            });
            $totalSuppliers = Supplier::where('salon_id', Auth::user()->salon_id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();
        } else {
            $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
            $branchIds = $allBranches->pluck('id');
            $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                ->orderBy('user_id', 'ASC')
                ->orderBy('id', 'DESC')
                ->get()
                ->groupBy('user_id');
            $currentDate = now();
            $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                $dueDate = $subs->first()->fatoraPdf->due_date;
                return $dueDate->isPast();
            })->keys();
            $stockout = StockOut::where('salon_id', Auth::user()->salon_id)->get();
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $purchaseOrders = PurchaseOrder::where('salon_id', Auth::user()->salon_id)
                ->where(function ($query) use ($startDate, $endDate) {
                    $query->whereBetween(DB::raw("STR_TO_DATE(date, '%m/%d/%Y')"), [$startDate, $endDate])
                        ->orWhereBetween(DB::raw("DATE_FORMAT(date, '%Y-%m-%d')"), [$startDate, $endDate]);
                })
                ->get();
            $totalQuantity = 0;
            foreach ($purchaseOrders as $purchaseOrder) {
                foreach ($purchaseOrder->stockOuts as $stockOut) {
                    if ($stockOut->status != 'refund') {
                        $totalQuantity += $stockOut->quantity;
                    }
                }
            }
            $products = Product::where('product_type_id','2')->whereIn('salon_id', $branchIds)
                ->whereNotIn('salon_id', $expiredUserIds)
                ->whereHas('productInventories', function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('created_at', [$startDate, $endDate]);
                })->get();
            $totalStockIns = $products->sum(function ($product) use ($startDate, $endDate) {
                $filteredInventories = $product->productInventories()
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->get();
                $totalQuantity = $filteredInventories->sum('quantity');
                $totalConsumedQuantity = $filteredInventories->sum('consumed_quantity');
                return $totalQuantity + $totalConsumedQuantity;
            });
            $totalSuppliers = Supplier::where('salon_id', Auth::user()->salon_id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();
            return response()->json([
                'totalSuppliers' => $totalSuppliers,
                'totalStockIns' => $totalStockIns,
                'purchaseOrders' => $purchaseOrders,
                'totalQuantity'=>$totalQuantity
            ]);
        }
    }
}
