<?php

namespace App\Http\Controllers\AssignedProduct;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use Mail;
use App\AssignedProduct;
use Illuminate\Http\Request;

class AssignedProductController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('assignedproduct','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $assignedproduct = AssignedProduct::where('product_id', 'LIKE', "%$keyword%")
                ->orWhere('employee_id', 'LIKE', "%$keyword%")
                ->orWhere('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('assigned_user_id', 'LIKE', "%$keyword%")
                ->orWhere('status', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $assignedproduct = AssignedProduct::paginate($perPage);
            }

            return view('assignedProduct.assigned-product.index', compact('assignedproduct'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('assignedproduct','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('assignedProduct.assigned-product.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {   
        $model = str_slug('assignedproduct','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $assignedproduct = AssignedProduct::create($requestData);

            try{
                $employee_name = $assignedproduct->employee->name;
                $employee_email= $assignedproduct->employee->email;
                $product_brand_name = $assignedproduct->product->brand_name;
                $product_title = $assignedproduct->product->title;
                $product_quantity = $assignedproduct->product_quantity;
                $result = Mail::raw("Hello $employee_name, You Have Assigned A Products
                Brand Name : $product_brand_name
                Product Title : $product_title
                Product Quantity : $product_quantity
                ", function ($message) use ($employee_email) {
                    $message->to($employee_email)->cc('<EMAIL>', 'Aftab Ali')->subject('Salon Appointment Request.');
                });
                return back()->with('flash_message', 'AssignedCustomer added!');
            }catch(\Exception $e){
                return back()->with(['message'=>'Your Creation successfully, but unable to send email.','type'=>'error','title'=>'Fail']);
            }

            return back()->with('flash_message', 'AssignedProduct added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('assignedproduct','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $assignedproduct = AssignedProduct::findOrFail($id);
            return view('assignedProduct.assigned-product.show', compact('assignedproduct'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('assignedproduct','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $assignedproduct = AssignedProduct::findOrFail($id);
            return view('assignedProduct.assigned-product.edit', compact('assignedproduct'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('assignedproduct','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $assignedproduct = AssignedProduct::findOrFail($id);
             $assignedproduct->update($requestData);

             return redirect('assignedProduct/assigned-product')->with('flash_message', 'AssignedProduct updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('assignedproduct','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            AssignedProduct::destroy($id);

            return redirect('assignedProduct/assigned-product')->with('flash_message', 'AssignedProduct deleted!');
        }
        return response(view('403'), 403);

    }
}
