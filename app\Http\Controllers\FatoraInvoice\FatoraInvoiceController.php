<?php

namespace App\Http\Controllers\FatoraInvoice;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\FatoraInvoice;
use Illuminate\Http\Request;

class FatoraInvoiceController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('fatorainvoice','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $fatorainvoice = FatoraInvoice::where('company_name', 'LIKE', "%$keyword%")
                ->orWhere('company_address', 'LIKE', "%$keyword%")
                ->orWhere('company_vat_number', 'LIKE', "%$keyword%")
                ->orWhere('commercial_registration_number', 'LIKE', "%$keyword%")
                ->orWhere('customer_name', 'LIKE', "%$keyword%")
                ->orWhere('customer_address', 'LIKE', "%$keyword%")
                ->orWhere('customer_vat_number', 'LIKE', "%$keyword%")
                ->orWhere('total_amount', 'LIKE', "%$keyword%")
                ->orWhere('unit_price', 'LIKE', "%$keyword%")
                ->orWhere('vat_amount', 'LIKE', "%$keyword%")
                ->orWhere('quantity', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->orWhere('invoice_number', 'LIKE', "%$keyword%")
                ->orWhere('current_date', 'LIKE', "%$keyword%")
                ->orWhere('due_date', 'LIKE', "%$keyword%")
                ->orWhere('user_subscription_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $fatorainvoice = FatoraInvoice::paginate($perPage);
            }

            return view('fatoraInvoice.fatora-invoice.index', compact('fatorainvoice'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('fatorainvoice','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('fatoraInvoice.fatora-invoice.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('fatorainvoice','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            FatoraInvoice::create($requestData);
            return redirect('fatoraInvoice/fatora-invoice')->with('flash_message', 'FatoraInvoice added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('fatorainvoice','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $fatorainvoice = FatoraInvoice::findOrFail($id);
            return view('fatoraInvoice.fatora-invoice.show', compact('fatorainvoice'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('fatorainvoice','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $fatorainvoice = FatoraInvoice::findOrFail($id);
            return view('fatoraInvoice.fatora-invoice.edit', compact('fatorainvoice'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('fatorainvoice','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $fatorainvoice = FatoraInvoice::findOrFail($id);
             $fatorainvoice->update($requestData);

             return redirect('fatoraInvoice/fatora-invoice')->with('flash_message', 'FatoraInvoice updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('fatorainvoice','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            FatoraInvoice::destroy($id);

            return redirect('fatoraInvoice/fatora-invoice')->with('flash_message', 'FatoraInvoice deleted!');
        }
        return response(view('403'), 403);

    }
}
