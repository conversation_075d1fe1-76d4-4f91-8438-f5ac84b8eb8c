<?php

namespace App\Http\Controllers\CustomerFatora;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\Profile;
use App\CustomerFatora;
use App\CustomerAppointment;
use App\SalonService;
use App\User;
use App\Product;
use Illuminate\Support\Facades\Http;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Facades\File;
use Auth;
use Dompdf\Dompdf;
use PDF;
use Mail;
use Storage;
use Illuminate\Http\Request;

class CustomerFatoraController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('customerfatora','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $customerfatora = CustomerFatora::where('appointment_id', 'LIKE', "%$keyword%")
                ->orWhere('invoice_number', 'LIKE', "%$keyword%")
                ->orWhere('invoice_issue_date', 'LIKE', "%$keyword%")
                ->orWhere('date_of_supply', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $customerfatora = CustomerFatora::paginate($perPage);
            }

            return view('customerFatora.customer-fatora.index', compact('customerfatora'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('customerfatora','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('customerFatora.customer-fatora.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        set_time_limit(12220);
        $model = str_slug('customerfatora','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            extract($request->all());
            $requestData = (['appointment_id'=>$appointment_id,'invoice_number'=>$invoice_number,'invoice_issue_date'=>$invoice_issue_date,'date_of_supply'=>$date_of_supply]);
            $fatora = CustomerFatora::create($requestData);
            $profile = (['address'=>$address,'city'=>$city,'country'=>$country,'postal'=>$postal,'state'=>$state,'vat_number'=>$vat_number]);
            Profile::where('user_id',$request->user_id)->update($profile);
            $customerFatora = CustomerAppointment::findOrFail($request->appointment_id);
            if (Auth::user()->roles[0]->name == "cashier"){
                $services = SalonService::where('salon_id', Auth::user()->salon_id)->get();
                $products = Product::where('salon_id', Auth::user()->salon_id)->where('product_type_id',1)->get();
            }else{
                $services = SalonService::where('salon_id', Auth::user()->id)->get();
                $products = Product::where('salon_id', Auth::user()->id)->where('product_type_id',1)->get();
            }
            $admin = User::FindOrFail(2);
            $vat = $admin->profile->vat;
            $customerFatoraData = json_encode($customerFatora);
            $encodedCustomerFatoraData = urlencode($customerFatoraData);
            $qrCodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?data=' . $encodedCustomerFatoraData . '&size=300x300';
            $qrCodeImageData = file_get_contents($qrCodeUrl);
            $fileName = 'qr_code_image.png';
            Storage::disk('website')->put('qr-codes/' . $customerFatora->id . '/' . $fileName, $qrCodeImageData);
            try{
                $data = [
                    'name' => $customerFatora->customer->name,
                    'salon_picture' => $customerFatora->salonId->profile->pic,
                    'email' => $customerFatora->customer->email,
                    'salon_name' => $customerFatora->salonId->name,
                    'salon_email' => $customerFatora->salonId->email,
                ];
                $result = Mail::send('website.email_templates.fatora_email', ['data' => $data, 'customerFatora' => $customerFatora, 'services' => $services, 'products' => $products, 'vat'=> $vat], function ($message) use($data){
                    $message->to($data['email'], $data['name'])->subject('Fatoora Invoice');
                });
            }catch (Exception $e) {
                echo "An error occurred: " . $e->getMessage();
            }
            // return $pdf = view('website.pdf.generate_customer_fatoora', compact('qrCodeFinal','customerFatora','services','vat','products'));
            // $pdf = PDF::loadView('website.pdf.generate_customer_fatoora', compact('qrCodeFinal','customerFatora','services','vat','products'))->setPaper('a4', 'landscape');
            // $folderPath = public_path('website/customer_fatoora');
            // if (!File::exists($folderPath)) {
            //     File::makeDirectory($folderPath, 0755, true);
            // }
            // $pdfFile = '/customer_fatora'. $fatora->id .'.pdf';
            // $pdf->save($folderPath . $pdfFile);
            return view('website.pdf.customer_fatora',compact('customerFatora','services','vat','products'));

        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('customerfatora','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $customerfatora = CustomerFatora::findOrFail($id);
            return view('customerFatora.customer-fatora.show', compact('customerfatora'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('customerfatora','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $customerfatora = CustomerFatora::findOrFail($id);
            return view('customerFatora.customer-fatora.edit', compact('customerfatora'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('customerfatora','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $customerfatora = CustomerFatora::findOrFail($id);
             $customerfatora->update($requestData);

             return redirect('customerFatora/customer-fatora')->with('flash_message', 'CustomerFatora updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('customerfatora','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            CustomerFatora::destroy($id);

            return redirect('customerFatora/customer-fatora')->with('flash_message', 'CustomerFatora deleted!');
        }
        return response(view('403'), 403);

    }
}
