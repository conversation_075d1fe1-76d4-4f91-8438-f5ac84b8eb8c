<?php

namespace App\Http\Controllers;

use App\Blog;
use App\CustomerFeedback;
use App\Discount;
use App\EmployeeExpiryNotification;
use App\Http\Middleware\Authenticate;
use App\CustomNotification;
use App\OffDate;
use App\PurchaseOrder;
use App\StockOut;
use App\Supplier;
use App\ProductBrand;
use App\Support;
use App\Testimonial;
use Illuminate\Broadcasting\PendingBroadcast;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use App\SubscriptionPlan;
use App\SessionDatum;
use App\CustomerFatora;
use App\SalonType;
use App\Contact;
use App\NewsLetter;
use App\SalonService;
use Dompdf\Dompdf;
use App\User;
use App\Profile;
use App\CustomerProduct;
use App\Product;
use App\Feedback;
use App\ServiceCategory;
use App\Slot;
use App\GuideSection;
use App\EmployeeType;
use App\Page;
use App\Ad;
use App\UserSubscription;
use App\ProductCategory;
use App\CustomerService;
use App\Picture;
use App\CustomerAppointment;
use App\EmployeeService;
use DateTime;
use App\FatoraInvoice;
use Illuminate\Support\Facades\Http;
use Stripe\Stripe;
use App\AssignedCustomer;
use App\EmployeeServiceCategory;
use Stripe\Charge;
use Stripe\StripeClient;
use Stripe\Source;
use Stripe\Customer;
use App\Role;
use App\Rating;
use App\EmployeeLeave;
use App\CustomerServiceCategory;
use App\CustomerProductCategory;
use DB;
use Session;
use URL;
use Illuminate\Support\Facades\Hash;
use Mail;
use Artisan;
use Socialite;
use PDF;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Auth;
use Storage;
use Illuminate\Support\Str;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Response;
use Exception;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use App\CashierShuffleBranch;
use App\PaymentCard;
use App\OurService;
use App\PremiumAddonPackage;
use App\PaymentCardDetail;
use App\PremiumAddonSalonCashier;
use App\PremiumAddonSalonEmployee;
use App\CustomerSlot;
use App;
use App\Expense;
use App\Revenue;
use App\ProductInventory;

class WebsiteController extends Controller
{
    public function index()
    {
        $salons = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_setting_updated', 1)->where('register_status', "Accepted")->get();
        $products = Product::where('product_type_id', 1)->get();
        $liink = User::with('profile')->find(2);
        $guideSections = GuideSection::get();
        $homePageContent = Page::where('page', 'home')->get();
        $homePageImages = Picture::get();
        $blogs = Blog::where('status', 1)->latest('created_at')->get();
        $latestBlog = $blogs->first();
        $ourService = OurService::orderBy('position', 'ASC')->get();
        $testimonials = Testimonial::where('status',1)->orderBy('position', 'ASC')->get();
        return view('website.index', compact('salons', 'products', 'liink', 'guideSections', 'homePageContent', 'homePageImages', 'blogs', 'latestBlog', 'ourService','testimonials'));
    }//end function index.
    public function termsConditions()
    {
        $liink = User::with('profile')->find(2);
        $termsConditionPageContent = Page::where('page', 'terms_condition')->first();
        $homePageContent = Page::where('page', 'home')->first();
        $blogs = Blog::where('status', 1)->latest('created_at')->get();
        $latestBlog = $blogs->first();
        return view('website.terms-conditions', compact( 'liink', 'termsConditionPageContent','homePageContent', 'blogs', 'latestBlog'));
    }

    public function aboutUs()
    {
        $salons = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_setting_updated', 1)->where('register_status', "Accepted")->get();
        $products = Product::where('product_type_id', 1)->get();
//        $liink = User::where('id', 2)->with('profile')->get();
        $liink = User::with('profile')->find(2);
        $guideSections = GuideSection::get();
        $aboutPageContent = Page::where('page', 'about')->get();
        $homePageContent = Page::where('page', 'home')->get();
        $homePageImages = Picture::get();
//        $latestBlog = Blog::where('status', 1)->latest('created_at')->first();
//        $blogs = Blog::where('status', 1)->get();
//        $latestBlog = $blogs->pop();
        $blogs = Blog::where('status', 1)->latest('created_at')->get();
        $latestBlog = $blogs->first();
        Session::forget('google_registered_user');
        $userSubscriptionSession = Session::forget('package_id_for_google_signup');
        $salonAppointmentSession = Session::forget('salon_id_for_google_signup');
        Session::forget('step_customer_google_singup');
        return view('website.about_us', compact('salons', 'liink', 'products', 'guideSections', 'aboutPageContent','homePageContent', 'homePageImages', 'blogs', 'latestBlog'));
    }//end aboutUs function.
    public function userInformation(){
        return view('website.user-information');
    }
    public function steperAppoint()
    {
        return view('website.steper_appoint');
    }

    public function contactUs()
    {
        $liink = User::where('id', 2)->get();
        $homePageContent = Page::where('page', 'home')->get();
        $homePageImages = Picture::get();
        return view('website.contact_us', compact('liink', 'homePageContent', 'homePageImages'));
    }//end contactUs function.

    public function blogs()
    {
        $blogContent = Page::where('slug', 'our_blogs_section')->first();
        $latestBlog = Blog::where('status', 1)->latest('created_at')->first();
        $blogs = Blog::where('status', 1)->orderBy('id', 'DESC')->get();
        $blogImage = Picture::where('slug','blog_page_banner')->first()->image;
        $blogPageContent = Page::where('page', 'blog')->first();
        return view('website.blogs', compact('blogContent', 'blogs', 'latestBlog','blogImage','blogPageContent'));
    }//end contactUs function.

    public function blogDetail($id){
        $blog = Blog::findOrFail($id);
        return view('website.blog_detail', compact('blog'));
    }//end contactUs function.

    public function salonListing()
    {
        $categories = ServiceCategory::get();
        $our_salons = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_setting_updated', 1)->where('tour_status','>',3)->where('register_status', "Accepted")->get();
        $PageContent = Page::where('slug', 'our_salons_section')->first();
        if (Session::has('search_salons')) {
            $searchSalons = Session::get('search_salons');
        } else {
            $searchSalons = [];
        }
        if (Session::has('salon_listing_filter')) {
            $filterSalons = Session::get('salon_listing_filter');
            Session::forget('salon_listing_filter');
        } else {
            $filterSalons = [];
        }
        return view('website.salon_listing', compact('our_salons', 'searchSalons', 'categories', 'PageContent', 'filterSalons'));
    }
    public function salonDetail($id = null, $slug = null)
    {
        try {
            $salon = User::where('id', $id)
                ->where('salon_setting_updated', '1')
                ->where('tour_status', '5')
                ->where('register_status', 'Accepted')
                ->first();
            if (isset($salon) && $salon == null){
                return redirect(url('/'))->with(['title' => 'Alert', 'message' => trans('messages.Unable toprocesstryagain'), 'type' => 'error']);;
            }
            if ($salon->profile->link !== url()->current() && $id == $salon->id) {
                return redirect($salon->profile->link);
            }
            Session::forget('salon_listing_filter');
            $salonServices = SalonService::where('is_deleted','1')->where('salon_id', $id)->get();
            $salonProduct = Product::where('is_deleted','1')->where('salon_id', $id)->get();
            $employees = User::whereHas(
                'roles', function ($q) {
                $q->where('name', 'employee');
            }
            )->where('salon_id', $id)->orderBy('id', 'DESC')->get();
            $categories = ServiceCategory::where('salon_id', $id)->orderBy('id', 'DESC')->get();
            $PageContent = Page::where('slug', 'our_salons_section')->first();
            if ($salon != null) {
                return view('website.salon_detail', compact('salon', 'salonServices', 'employees', 'categories', 'salonProduct', 'PageContent'));
            } else {
                return redirect(url('/'))->with(['title' => 'Alert', 'message' => trans('messages.Unable toprocesstryagain'), 'type' => 'error']);;
            }
        } catch (\Exception $e) {
            return redirect(url('/'))->with(['title' => 'Alert', 'message' => trans('messages.Unabletoprocesstryagain'), 'type' => 'error']);;
        }
    }

    public function appointment($id)
    {
        $salon = User::with('userSubscriptionId')->findOrFail($id);
        $userSubscription = UserSubscription::where('user_id', $salon->id)->orderBy('id', 'DESC')->first();
        $categories = ServiceCategory::whereIn('id', $salon->allEmployeeServiceCategoryIds)->orderBy('id', 'DESC')->get();
        $productCategory = ServiceCategory::whereIn('id', $salon->allEmployeeProductCategoryIds)->orderBy('id', 'DESC')->get();
        $services = SalonService::whereIn('id', $salon->allEmployeeServiceIds)->get();
        $products = Product::where('salon_id', $id)->where('product_type_id', 1)->get();
        if (Session::has('google_registered_user')) {
            $google_registered_user = Session::get('google_registered_user');
            $salonAppointmentSession = Session::forget('salon_id_for_google_signup');
        } else {
            $google_registered_user = [];
        }
        return view('website.appointment', compact('id', 'services', 'products', 'categories', 'productCategory', 'salon', 'google_registered_user', 'userSubscription'));
    }

//    public function appointmentSlots(Request $request, $date = null, $id = null)
//    {
//        $array_val = [];
//        $slot_id_array = [];
//        $date = Carbon::createFromFormat('m-d-Y', $date)->format('m/d/Y');
//        $currentDateTime = Carbon::now();
//        if ($currentDateTime->format('m-d-Y') == $request->date) {
//            $serviceIds = $request->input('service_ids');
//            $serviceCount = count($serviceIds);
//            if (isset($serviceIds) && $serviceIds != null) {
//                $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
//                    ->whereRaw('? BETWEEN date_to AND date_from', $date)
//                    ->pluck('employee_id');
//                $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)->whereIn('salon_service_id', $serviceIds)
//                    ->groupBy('employee_id')
//                    ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
//                    ->pluck('employee_id');
//                if ($employeeIds->count() > 0) {
//                    $result = $employeeIds->toArray();
//                    $AppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date', [$date])->whereNotIn('status', ['Cancel'])->pluck('customer_slot_id');
//                    $allAppointmentsDate = $AppointmentsDate
//                        ->map(function ($slotId) {
//                            return json_decode($slotId);
//                        })
//                        ->flatten()
//                        ->unique();
//                    if ($allAppointmentsDate->isEmpty()) {
//                        $salonSlots = Slot::where('salon_id', $id)
//                            ->where('avalible_status', 'Active')
//                            ->where('start_time', '>=', $currentDateTime->format('H:i:s'))
//                            ->get();
//                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                    } else {
//                        if ($employeeIds->count() > 1) {
//                            $countedValues = array_count_values($allAppointmentsDate->toArray());
//                            foreach ($countedValues as $element => $count) {
//                                if ($employeeIds->count() <= $count) {
//                                    array_push($slot_id_array, $element);
//                                }
//                            }
//                            $salonSlots = Slot::where('salon_id', $id)
//                                ->where('avalible_status', 'Active')
//                                ->where('start_time', '>=', $currentDateTime->format('H:i:s'))
//                                ->whereNotIn('id', $slot_id_array)->get();
//                            return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
//
//                        } else {
//                            $salonSlots = Slot::where('salon_id', $id)
//                                ->where('avalible_status', 'Active')
//                                ->where('start_time', '>=', $currentDateTime->format('H:i:s'))
//                                ->whereNotIn('id', $allAppointmentsDate->toArray())->get();
//                            return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                        }
//                    }
//                } else {
//                    return view('website.ajax.not_avalible_slot_ajax');
//                }
//            } else {
//                return view('website.ajax.not_avalible_slot_ajax');
//            }
//        } else {
//            $serviceIds = $request->input('service_ids');
//            $serviceCount = count($serviceIds);
//            if (isset($serviceIds) && $serviceIds != null) {
//                $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
//                    ->whereRaw('? BETWEEN date_to AND date_from', $date)
//                    ->pluck('employee_id');
//                $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)->whereIn('salon_service_id', $serviceIds)
//                    ->groupBy('employee_id')
//                    ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
//                    ->pluck('employee_id');
//                if ($employeeIds->count() > 0) {
//                    $result = $employeeIds->toArray();
//                    $AppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date', [$date])->whereNotIn('status', ['Cancel'])->pluck('customer_slot_id');
//                    $allAppointmentsDate = $AppointmentsDate
//                        ->map(function ($slotId) {
//                            return json_decode($slotId);
//                        })
//                        ->flatten()
//                        ->unique();
//                    if ($allAppointmentsDate->isEmpty()) {
//                        $salonSlots = Slot::where('salon_id', $id)
//                            ->where('avalible_status', 'Active')
//                            ->get();
//                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                    } else {
//                        if ($employeeIds->count() > 1) {
//                            $countedValues = array_count_values($allAppointmentsDate->toArray());
//                            foreach ($countedValues as $element => $count) {
//                                if ($employeeIds->count() <= $count) {
//                                    array_push($slot_id_array, $element);
//                                }
//                            }
//                            $salonSlots = Slot::where('salon_id', $id)
//                                ->where('avalible_status', 'Active')
//                                ->whereNotIn('id', $slot_id_array)->get();
//                            return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
//
//                        } else {
//                            $salonSlots = Slot::where('salon_id', $id)
//                                ->where('avalible_status', 'Active')
//                                ->whereNotIn('id', $allAppointmentsDate->toArray())->get();
//                            return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                        }
//                    }
//                } else {
//                    return view('website.ajax.not_avalible_slot_ajax');
//                }
//            } else {
//                return view('website.ajax.not_avalible_slot_ajax');
//            }
//        }
//        return view('website.ajax.not_avalible_slot_ajax');
//    }

//    public function appointmentSlots(Request $request, $date = null, $id = null)
//    {
//        $array_val = [];
//        $slot_id_array = [];
//        $date = Carbon::createFromFormat('m-d-Y', $date)->format('m/d/Y');
//        $currentDateTime = Carbon::now();
//
//        // Check if the current date is the same as the requested date
//        if ($currentDateTime->format('m-d-Y') == $request->date) {
//            $serviceIds = $request->input('service_ids');
//            $serviceCount = count($serviceIds);
//            // Ensure service IDs are set
//            if (isset($serviceIds) && $serviceIds != null) {
//                $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
//                    ->whereRaw('? BETWEEN date_to AND date_from', $date)
//                    ->pluck('employee_id');
//                $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)
//                    ->whereIn('salon_service_id', $serviceIds)
//                    ->groupBy('employee_id')
//                    ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [$serviceCount])
//                    ->pluck('employee_id');
//                if ($employeeIds->count() > 0) {
//                    $AppointmentsDate = CustomerAppointment::where('salon_id', $id)
//                        ->whereIn('customer_appointment_date', [$date])
//                        ->whereNotIn('status', ['Cancel'])
//                        ->pluck('customer_slot_id');
//                    $allAppointmentsDate = $AppointmentsDate
//                        ->map(function ($slotId) {
//                            return json_decode($slotId);
//                        })
//                        ->flatten();
//                    // If no slots are booked, show available slots
//                    if ($allAppointmentsDate->isEmpty()) {
//                        $salonSlots = Slot::where('salon_id', $id)
//                            ->where('avalible_status', 'Active')
//                            ->where('start_time', '>=', $currentDateTime->format('H:i:s'))
//                            ->get();
//
//                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                    } else {
//                        // If there are existing appointments, filter available slots
//                        $countedValues = array_count_values($allAppointmentsDate->toArray());
//                        foreach ($countedValues as $element => $count) {
//                            if ($employeeIds->count() <= $count) {
//                                array_push($slot_id_array, $element);
//                            }
//                        }
//                        $salonSlots = Slot::where('salon_id', $id)
//                            ->where('avalible_status', 'Active')
//                            ->whereNotIn('id', $slot_id_array)
//                            ->where('start_time', '>=', $currentDateTime->format('H:i:s'))
//                            ->get();
//
//                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                    }
//                } else {
//                    return view('website.ajax.not_avalible_slot_ajax');
//                }
//            } else {
//                return view('website.ajax.not_avalible_slot_ajax');
//            }
//        } else {
//            // Handle cases for future dates
//            $serviceIds = $request->input('service_ids');
//            $serviceCount = count($serviceIds);
//            if (isset($serviceIds) && $serviceIds != null) {
//                $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
//                    ->whereRaw('? BETWEEN date_to AND date_from', $date)
//                    ->pluck('employee_id');
//
//                $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)
//                    ->whereIn('salon_service_id', $serviceIds)
//                    ->groupBy('employee_id')
//                    ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [$serviceCount])
//                    ->pluck('employee_id');
//
//                if ($employeeIds->count() > 0) {
//                    $AppointmentsDate = CustomerAppointment::where('salon_id', $id)
//                        ->whereIn('customer_appointment_date', [$date])
//                        ->whereNotIn('status', ['Cancel'])
//                        ->pluck('customer_slot_id');
//
//                    $allAppointmentsDate = $AppointmentsDate
//                        ->map(function ($slotId) {
//                            return json_decode($slotId);
//                        })
//                        ->flatten();
//                    if ($allAppointmentsDate->isEmpty()) {
//                        $salonSlots = Slot::where('salon_id', $id)
//                            ->where('avalible_status', 'Active')
//                            ->get();
//
//                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                    } else {
//                        $countedValues = array_count_values($allAppointmentsDate->toArray());
//                        foreach ($countedValues as $element => $count) {
//                            if ($employeeIds->count() <= $count) {
//                                array_push($slot_id_array, $element);
//                            }
//                        }
//
//                        $salonSlots = Slot::where('salon_id', $id)
//                            ->where('avalible_status', 'Active')
//                            ->whereNotIn('id', $slot_id_array)
//                            ->get();
//
//                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                    }
//                } else {
//                    return view('website.ajax.not_avalible_slot_ajax');
//                }
//            } else {
//                return view('website.ajax.not_avalible_slot_ajax');
//            }
//        }
//        return view('website.ajax.not_avalible_slot_ajax');
//    }

    public function appointmentSlots(Request $request, $date = null, $id = null)
    {
        $array_val = [];
        $slot_id_array = [];
        $dateTime = DateTime::createFromFormat('m-d-Y', $date);
        $date = $dateTime->format('m/d/Y');
        $currentDateTime = date('h:i A');
        if (date('m-d-Y') == $request->date) {
            $serviceIds = $request->input('service_ids');
            $serviceCount = count($serviceIds);
            if (isset($serviceIds) && $serviceIds != null) {
                $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
                    ->whereRaw('? BETWEEN date_to AND date_from', $date)
                    ->pluck('employee_id');
                $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)
                    ->whereIn('salon_service_id', $serviceIds)
                    ->groupBy('employee_id')
                    ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [$serviceCount])
                    ->pluck('employee_id');
                if ($employeeIds->count() > 0) {
                    $AppointmentsDate = CustomerAppointment::where('salon_id', $id)
                        ->whereIn('customer_appointment_date', [$date])
                        ->whereNotIn('status', ['Cancel'])
                        ->pluck('customer_slot_id');
                    $allAppointmentsDate = $AppointmentsDate
                        ->map(function ($slotId) {
                            return json_decode($slotId);
                        })
                        ->flatten();
                    if ($allAppointmentsDate->isEmpty()) {
                        $salonSlots = Slot::where('salon_id', $id)
                            ->where('avalible_status', 'Active')
                            ->whereRaw("STR_TO_DATE(start_time, '%h:%i %p') >= STR_TO_DATE(?, '%h:%i %p')", [$currentDateTime])
                            ->orderByRaw("STR_TO_DATE(start_time, '%h:%i %p') ASC")
                            ->get();
                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
                    } else {
                        $countedValues = array_count_values($allAppointmentsDate->toArray());
                        foreach ($countedValues as $element => $count) {
                            if ($employeeIds->count() <= $count) {
                                array_push($slot_id_array, $element);
                            }
                        }
                        $salonSlots = Slot::where('salon_id', $id)
                            ->where('avalible_status', 'Active')
                            ->whereNotIn('id', $slot_id_array)
                            ->whereRaw("STR_TO_DATE(start_time, '%h:%i %p') >= STR_TO_DATE(?, '%h:%i %p')", [$currentDateTime])
                            ->orderByRaw("STR_TO_DATE(start_time, '%h:%i %p') ASC")
                            ->get();
                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
                    }
                } else {
                    return view('website.ajax.not_avalible_slot_ajax');
                }
            } else {
                return view('website.ajax.not_avalible_slot_ajax');
            }
        } else {
            // Handle cases for future dates
            $serviceIds = $request->input('service_ids');
            $serviceCount = count($serviceIds);

            if (isset($serviceIds) && $serviceIds != null) {
                $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
                    ->whereRaw('? BETWEEN date_to AND date_from', $date)
                    ->pluck('employee_id');

                $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)
                    ->whereIn('salon_service_id', $serviceIds)
                    ->groupBy('employee_id')
                    ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [$serviceCount])
                    ->pluck('employee_id');

                if ($employeeIds->count() > 0) {
                    $AppointmentsDate = CustomerAppointment::where('salon_id', $id)
                        ->whereIn('customer_appointment_date', [$date])
                        ->whereNotIn('status', ['Cancel'])
                        ->pluck('customer_slot_id');
                    $allAppointmentsDate = $AppointmentsDate
                        ->map(function ($slotId) {
                            return json_decode($slotId);
                        })
                        ->flatten();
                    if ($allAppointmentsDate->isEmpty()) {
                        $salonSlots = Slot::where('salon_id', $id)
                            ->where('avalible_status', 'Active')
                            ->get();
                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
                    } else {
                        $countedValues = array_count_values($allAppointmentsDate->toArray());
                        foreach ($countedValues as $element => $count) {
                            if ($employeeIds->count() <= $count) {
                                array_push($slot_id_array, $element);
                            }
                        }
                        $salonSlots = Slot::where('salon_id', $id)
                            ->where('avalible_status', 'Active')
                            ->whereNotIn('id', $slot_id_array)
                            ->get();
                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date', 'serviceCount'));
                    }
                } else {
                    return view('website.ajax.not_avalible_slot_ajax');
                }
            } else {
                return view('website.ajax.not_avalible_slot_ajax');
            }
        }
        return view('website.ajax.not_avalible_slot_ajax');
    }


//    public function customerAppointmentSlots(Request $request, $date = null, $id = null)
//    {
//        $array_val = [];
//        $slot_id_array = [];
//        $date = Carbon::createFromFormat('m-d-Y', $date)->format('m/d/Y');
//        $currentDateTime = Carbon::now()->addMinutes(15);
//        if ($currentDateTime->format('m-d-Y') == $request->date) {
//            $serviceIds = $request->input('serviceIdsSelected');
//            $serviceCount = count($serviceIds);
//            if (isset($request->employeeId) && $request->employeeId != null){
//                if (isset($serviceIds) && $serviceIds != null) {
//                    $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
//                        ->whereRaw('? BETWEEN date_to AND date_from', $date)
//                        ->pluck('employee_id');
//                    $employeeIds = EmployeeService::where('employee_id',$request->employeeId)->whereNotIn('employee_id', $employeeLeaves)->whereIn('salon_service_id', $serviceIds)
//                        ->groupBy('employee_id')
//                        ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
//                        ->pluck('employee_id');
//                    if ($employeeIds->count() > 0) {
//                        $AppointmentsDate = CustomerAppointment::where('salon_id', $id)->whereIn('customer_appointment_date', [$date])->whereNotIn('status', ['Cancel'])->pluck('customer_slot_id');
//                        $allAppointmentsDate = $AppointmentsDate
//                            ->map(function ($slotId) {
//                                return json_decode($slotId);
//                            })
//                            ->flatten();
//                        if ($allAppointmentsDate->isEmpty()) {
//                            $salonSlots = Slot::where('salon_id', $id)
//                                ->where('avalible_status', 'Active')
//                                ->where('start_time', '>=', $currentDateTime->format('H:i:s'))
//                                ->get();
//                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                        } else {
//                            $countedValues = array_count_values($allAppointmentsDate->toArray());
//                            foreach ($countedValues as $element => $count) {
//                                if ($employeeIds->count() <= $count) {
//                                    array_push($slot_id_array, $element);
//                                }
//                            }
//                            $salonSlots = Slot::where('salon_id', $id)
//                                ->where('avalible_status', 'Active')
//                                ->whereNotIn('id', $slot_id_array)
//                                ->where('start_time', '>=', $currentDateTime->format('H:i:s'))
//                                ->get();
//                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                        }
//                    } else {
//                        return view('website.ajax.not_avalible_slots_website_ajax');
//                    }
//                } else {
//                    return view('website.ajax.not_avalible_slots_website_ajax');
//                }
//            }else{
//                if (isset($serviceIds) && $serviceIds != null) {
//                    $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
//                        ->whereRaw('? BETWEEN date_to AND date_from', $date)
//                        ->pluck('employee_id');
//                    $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)->whereIn('salon_service_id', $serviceIds)
//                        ->groupBy('employee_id')
//                        ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
//                        ->pluck('employee_id');
//                    if ($employeeIds->count() > 0) {
//                        $AppointmentsDate = CustomerAppointment::where('salon_id', $id)->whereIn('customer_appointment_date', [$date])->whereNotIn('status', ['Cancel'])->pluck('customer_slot_id');
//                        $allAppointmentsDate = $AppointmentsDate
//                            ->map(function ($slotId) {
//                                return json_decode($slotId);
//                            })
//                            ->flatten();
//                        if ($allAppointmentsDate->isEmpty()) {
//                            $salonSlots = Slot::where('salon_id', $id)
//                                ->where('avalible_status', 'Active')
//                                ->where('start_time', '>=', $currentDateTime->format('H:i:s'))
//                                ->get();
//                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                        } else {
//                            $countedValues = array_count_values($allAppointmentsDate->toArray());
//                            foreach ($countedValues as $element => $count) {
//                                if ($employeeIds->count() <= $count) {
//                                    array_push($slot_id_array, $element);
//                                }
//                            }
//                            $salonSlots = Slot::where('salon_id', $id)
//                                ->where('avalible_status', 'Active')
//                                ->whereNotIn('id', $slot_id_array)
//                                ->where('start_time', '>=', $currentDateTime->format('H:i:s'))
//                                ->get();
//                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                        }
//                    } else {
//                        return view('website.ajax.not_avalible_slots_website_ajax');
//                    }
//                } else {
//                    return view('website.ajax.not_avalible_slots_website_ajax');
//                }
//            }
//        } else {
//            $serviceIds = $request->input('serviceIdsSelected');
//            $serviceCount = count($serviceIds);
//            if (isset($request->employee_id) && $request->employee_id != null){
//                if (isset($serviceIds) && $serviceIds != null) {
//                    $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
//                        ->whereRaw('? BETWEEN date_to AND date_from', $date)
//                        ->pluck('employee_id');
//                    $employeeIds = EmployeeService::where('employee_id',$request->employee_id)->whereNotIn('employee_id', $employeeLeaves)->whereIn('salon_service_id', $serviceIds)
//                        ->groupBy('employee_id')
//                        ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
//                        ->pluck('employee_id');
//                    if ($employeeIds->count() > 0) {
//                        $result = $employeeIds->toArray();
//                        $AppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date', [$date])->pluck('customer_slot_id');
//                        $allAppointmentsDate = $AppointmentsDate
//                            ->map(function ($slotId) {
//                                return json_decode($slotId);
//                            })
//                            ->flatten();
//                        if ($allAppointmentsDate->isEmpty()) {
//                            $salonSlots = Slot::where('salon_id', $id)->where('avalible_status', 'Active')->get();
//                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                        } else {
//                            $countedValues = array_count_values($allAppointmentsDate->toArray());
//                            foreach ($countedValues as $element => $count) {
//                                if ($employeeIds->count() <= $count) {
//                                    array_push($slot_id_array, $element);
//                                }
//                            }
//                            $salonSlots = Slot::where('salon_id', $id)
//                                ->where('avalible_status', 'Active')
//                                ->whereNotIn('id', $slot_id_array)
//                                ->get();
//                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                        }
//                    } else {
//                        return view('website.ajax.not_avalible_slots_website_ajax');
//                    }
//                } else {
//                    return view('website.ajax.not_avalible_slots_website_ajax');
//                }
//            }else{
//                if (isset($serviceIds) && $serviceIds != null) {
//                    $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
//                        ->whereRaw('? BETWEEN date_to AND date_from', $date)
//                        ->pluck('employee_id');
//                    $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)->whereIn('salon_service_id', $serviceIds)
//                        ->groupBy('employee_id')
//                        ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
//                        ->pluck('employee_id');
//                    if ($employeeIds->count() > 0) {
//                        $result = $employeeIds->toArray();
//                        $AppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date', [$date])->pluck('customer_slot_id');
//                        $allAppointmentsDate = $AppointmentsDate
//                            ->map(function ($slotId) {
//                                return json_decode($slotId);
//                            })
//                            ->flatten();
//                        if ($allAppointmentsDate->isEmpty()) {
//                            $salonSlots = Slot::where('salon_id', $id)->where('avalible_status', 'Active')->get();
//                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                        } else {
//                            $countedValues = array_count_values($allAppointmentsDate->toArray());
//                            foreach ($countedValues as $element => $count) {
//                                if ($employeeIds->count() <= $count) {
//                                    array_push($slot_id_array, $element);
//                                }
//                            }
//                            $salonSlots = Slot::where('salon_id', $id)
//                                ->where('avalible_status', 'Active')
//                                ->whereNotIn('id', $slot_id_array)
//                                ->get();
//                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
//                        }
//                    } else {
//                        return view('website.ajax.not_avalible_slots_website_ajax');
//                    }
//                } else {
//                    return view('website.ajax.not_avalible_slots_website_ajax');
//                }
//            }
//        }
//        return view('website.ajax.not_avalible_slots_website_ajax');
//    }

    public function customerAppointmentSlots(Request $request, $date = null, $id = null)
    {
        $array_val = [];
        $slot_id_array = [];
        $dateTime = DateTime::createFromFormat('m-d-Y', $date);
        $date = $dateTime->format('m/d/Y');
        $currentDateTime = date('h:i A', strtotime('+15 minutes'));
        if (date('m-d-Y') == $request->date) {
            $serviceIds = $request->input('serviceIdsSelected');
            $serviceCount = count($serviceIds);
            if (isset($request->employeeId) && $request->employeeId != null){
                if (isset($serviceIds) && $serviceIds != null) {
                    $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
                        ->whereRaw('? BETWEEN date_to AND date_from', $date)
                        ->pluck('employee_id');

                    $employeeIds = EmployeeService::where('employee_id', $request->employeeId)
                        ->whereNotIn('employee_id', $employeeLeaves)
                        ->whereIn('salon_service_id', $serviceIds)
                        ->groupBy('employee_id')
                        ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
                        ->pluck('employee_id');

                    if ($employeeIds->count() > 0) {
                        $AppointmentsDate = CustomerAppointment::where('salon_id', $id)
                            ->whereIn('customer_appointment_date', [$date])
                            ->whereNotIn('status', ['Cancel'])
                            ->pluck('customer_slot_id');

                        $allAppointmentsDate = $AppointmentsDate
                            ->map(function ($slotId) {
                                return json_decode($slotId);
                            })
                            ->flatten();

                        if ($allAppointmentsDate->isEmpty()) {
                            $salonSlots = Slot::where('salon_id', $id)
                                ->where('avalible_status', 'Active')
                                ->whereRaw("STR_TO_DATE(start_time, '%h:%i %p') >= STR_TO_DATE(?, '%h:%i %p')", [$currentDateTime])
                                ->orderByRaw("STR_TO_DATE(start_time, '%h:%i %p') ASC")
                                ->get();
                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
                        } else {
                            $countedValues = array_count_values($allAppointmentsDate->toArray());
                            foreach ($countedValues as $element => $count) {
                                if ($employeeIds->count() <= $count) {
                                    array_push($slot_id_array, $element);
                                }
                            }
                            $salonSlots = Slot::where('salon_id', $id)
                                ->where('avalible_status', 'Active')
                                ->whereNotIn('id', $slot_id_array)
                                ->whereRaw("STR_TO_DATE(start_time, '%h:%i %p') >= STR_TO_DATE(?, '%h:%i %p')", [$currentDateTime])
                                ->orderByRaw("STR_TO_DATE(start_time, '%h:%i %p') ASC")
                                ->get();
                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
                        }
                    } else {
                        return view('website.ajax.not_avalible_slots_website_ajax');
                    }
                } else {
                    return view('website.ajax.not_avalible_slots_website_ajax');
                }
            } else {
                if (isset($serviceIds) && $serviceIds != null) {
                    $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
                        ->whereRaw('? BETWEEN date_to AND date_from', $date)
                        ->pluck('employee_id');
                    $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)
                        ->whereIn('salon_service_id', $serviceIds)
                        ->groupBy('employee_id')
                        ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
                        ->pluck('employee_id');
                    if ($employeeIds->count() > 0) {
                        $AppointmentsDate = CustomerAppointment::where('salon_id', $id)
                            ->whereIn('customer_appointment_date', [$date])
                            ->whereNotIn('status', ['Cancel'])
                            ->pluck('customer_slot_id');
                        $allAppointmentsDate = $AppointmentsDate
                            ->map(function ($slotId) {
                                return json_decode($slotId);
                            })
                            ->flatten();
                        if ($allAppointmentsDate->isEmpty()) {
                            $salonSlots = Slot::where('salon_id', $id)
                                ->where('avalible_status', 'Active')
                                ->whereRaw("STR_TO_DATE(start_time, '%h:%i %p') >= STR_TO_DATE(?, '%h:%i %p')", [$currentDateTime])
                                ->orderByRaw("STR_TO_DATE(start_time, '%h:%i %p') ASC")
                                ->get();
                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
                        } else {
                            $countedValues = array_count_values($allAppointmentsDate->toArray());
                            foreach ($countedValues as $element => $count) {
                                if ($employeeIds->count() <= $count) {
                                    array_push($slot_id_array, $element);
                                }
                            }
                            $salonSlots = Slot::where('salon_id', $id)
                                ->where('avalible_status', 'Active')
                                ->whereNotIn('id', $slot_id_array)
                                ->whereRaw("STR_TO_DATE(start_time, '%h:%i %p') >= STR_TO_DATE(?, '%h:%i %p')", [$currentDateTime])
                                ->orderByRaw("STR_TO_DATE(start_time, '%h:%i %p') ASC")
                                ->get();
                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
                        }
                    } else {
                        return view('website.ajax.not_avalible_slots_website_ajax');
                    }
                } else {
                    return view('website.ajax.not_avalible_slots_website_ajax');
                }
            }
        } else {
            $serviceIds = $request->input('serviceIdsSelected');
            $serviceCount = count($serviceIds);

            if (isset($request->employee_id) && $request->employee_id != null){
                if (isset($serviceIds) && $serviceIds != null) {
                    $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
                        ->whereRaw('? BETWEEN date_to AND date_from', $date)
                        ->pluck('employee_id');

                    $employeeIds = EmployeeService::where('employee_id', $request->employee_id)
                        ->whereNotIn('employee_id', $employeeLeaves)
                        ->whereIn('salon_service_id', $serviceIds)
                        ->groupBy('employee_id')
                        ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
                        ->pluck('employee_id');

                    if ($employeeIds->count() > 0) {
                        $result = $employeeIds->toArray();
                        $AppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date', [$date])->pluck('customer_slot_id');
                        $allAppointmentsDate = $AppointmentsDate
                            ->map(function ($slotId) {
                                return json_decode($slotId);
                            })
                            ->flatten();

                        if ($allAppointmentsDate->isEmpty()) {
                            $salonSlots = Slot::where('salon_id', $id)
                                ->where('avalible_status', 'Active')
                                ->get();
                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
                        } else {
                            $countedValues = array_count_values($allAppointmentsDate->toArray());
                            foreach ($countedValues as $element => $count) {
                                if ($employeeIds->count() <= $count) {
                                    array_push($slot_id_array, $element);
                                }
                            }
                            $salonSlots = Slot::where('salon_id', $id)
                                ->where('avalible_status', 'Active')
                                ->whereNotIn('id', $slot_id_array)
                                ->get();
                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
                        }
                    } else {
                        return view('website.ajax.not_avalible_slots_website_ajax');
                    }
                } else {
                    return view('website.ajax.not_avalible_slots_website_ajax');
                }
            } else {
                if (isset($serviceIds) && $serviceIds != null) {
                    $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
                        ->whereRaw('? BETWEEN date_to AND date_from', $date)
                        ->pluck('employee_id');

                    $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)
                        ->whereIn('salon_service_id', $serviceIds)
                        ->groupBy('employee_id')
                        ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
                        ->pluck('employee_id');

                    if ($employeeIds->count() > 0) {
                        $result = $employeeIds->toArray();
                        $AppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date', [$date])->pluck('customer_slot_id');
                        $allAppointmentsDate = $AppointmentsDate
                            ->map(function ($slotId) {
                                return json_decode($slotId);
                            })
                            ->flatten();

                        if ($allAppointmentsDate->isEmpty()) {
                            $salonSlots = Slot::where('salon_id', $id)
                                ->where('avalible_status', 'Active')
                                ->get();
                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
                        } else {
                            $countedValues = array_count_values($allAppointmentsDate->toArray());
                            foreach ($countedValues as $element => $count) {
                                if ($employeeIds->count() <= $count) {
                                    array_push($slot_id_array, $element);
                                }
                            }
                            $salonSlots = Slot::where('salon_id', $id)
                                ->where('avalible_status', 'Active')
                                ->whereNotIn('id', $slot_id_array)
                                ->get();
                            return view('website.ajax.appointment_slots_website_ajax', compact('salonSlots', 'date', 'serviceCount'));
                        }
                    } else {
                        return view('website.ajax.not_avalible_slots_website_ajax');
                    }
                } else {
                    return view('website.ajax.not_avalible_slots_website_ajax');
                }
            }
        }
        return view('website.ajax.not_avalible_slots_website_ajax');
    }

//    public function avalibleSlotAppointmentEmployeeWebsite(Request $request, $date = null, $id = null)
//    {
//        $date = Carbon::createFromFormat('m-d-Y', $date)->format('m/d/Y');
//        $serviceIds = $request->input('serviceIdsSelected');
//        $salonId = $request->salonId;
//        $slotIds = $request->selectedOption;
//        if (isset($request->employeeId) && $request->employeeId != null){
//            if (isset($serviceIds) && $serviceIds != null) {
//                $customerSlots = CustomerSlot::where('date', $date)->where('salon_id', $salonId)->whereIn('slot_id', $slotIds)->where('employee_id',$request->employeeId)->pluck('appointment_id')->unique()->toArray();
//                $appointmentsDate = CustomerAppointment::whereIn('id', $customerSlots)->where('customer_appointment_date', $date)->whereNotIn('status', ['Cancel'])->pluck('id');
//                $allAssignedEmployeeIds = AssignedCustomer::whereIn('appointment_id', $appointmentsDate)->pluck('employee_id');
//                $employeeLeavesIds = EmployeeLeave::whereIn('salon_id', [$salonId])
//                    ->whereRaw('? BETWEEN date_to AND date_from', $date)
//                    ->pluck('employee_id');
//                $noEmployees = $employeeLeavesIds->merge($allAssignedEmployeeIds)->toArray();
//                $employee = EmployeeService::where('employee_id',$request->employeeId)->whereNotIn('employee_id', $noEmployees)->whereIn('salon_service_id', $serviceIds)->get();
//                $groupedEmployees = $employee->groupBy('employee_id');
//                $employee = $groupedEmployees->filter(function ($employeeGroup) use ($serviceIds) {
//                    $employeeServiceIds = $employeeGroup->pluck('salon_service_id')->unique();
//                    return $employeeServiceIds->intersect($serviceIds)->count() === count($serviceIds);
//                });
//                if ($employee->count() > 0) {
//                    return view('website.ajax.avalible_employee_card_website_ajax', compact('employee', 'serviceIds', 'salonId'));
//                } else {
//                    return view('website.ajax.not_avalible_employee_card_website_ajax');
//                }
//            } else {
//                return view('website.ajax.not_avalible_employee_card_website_ajax');
//            }
//        }else{
//            if (isset($serviceIds) && $serviceIds != null) {
//                $customerSlots = CustomerSlot::where('date', $date)
//                    ->where('salon_id', $salonId)
//                    ->whereIn('slot_id', $slotIds)
//                    ->pluck('appointment_id')
//                    ->unique()
//                    ->toArray();
//                $appointmentsDate = CustomerAppointment::whereIn('id', $customerSlots)
//                    ->where('customer_appointment_date', $date)
//                    ->whereNotIn('status', ['Cancel'])
//                    ->pluck('id');
//                $allAssignedEmployeeIds = AssignedCustomer::whereIn('appointment_id', $appointmentsDate)
//                    ->pluck('employee_id');
//                $employeeLeavesIds = EmployeeLeave::whereIn('salon_id', [$salonId])
//                    ->whereRaw('? BETWEEN date_to AND date_from', $date)
//                    ->pluck('employee_id');
//                $noEmployees = $employeeLeavesIds->merge($allAssignedEmployeeIds)->toArray();
//                $employees = EmployeeService::whereNotIn('employee_id', $noEmployees)
//                    ->whereIn('salon_service_id', $serviceIds)
//                    ->with('getEmployees')
//                    ->get();
//                $employeeType = $request->appointmentType;
//                // Filter employees by appointment type
//                $filteredEmployees = $employees->flatMap(function ($employeeGroup) use ($serviceIds, $employeeType) {
//                    $employeeServiceIds = $employeeGroup->pluck('salon_service_id')->unique();
//                    if ($employeeServiceIds->intersect($serviceIds)->count() === count($serviceIds)) {
//                        return $employeeGroup->getEmployees->filter(function ($employee) use ($employeeType) {
//                            return isset($employeeType) && $employee->employeeType->salon_type_id == $employeeType;
//                        });
//                    }
//                    return collect();
//                });
//                $uniqueEmployeeIds = $filteredEmployees->pluck('id')->unique();
//                if ($uniqueEmployeeIds->count() > 0) {
//                    return view('website.ajax.avalible_employee_card_website_ajax', compact('filteredEmployees', 'uniqueEmployeeIds', 'serviceIds', 'salonId', 'employeeType'));
//                } else {
//                    return view('website.ajax.not_avalible_employee_card_website_ajax');
//                }
//            } else {
//                return view('website.ajax.not_avalible_employee_card_website_ajax');
//            }
//        }
//    }
    public function avalibleSlotAppointmentEmployeeWebsite(Request $request, $date = null, $id = null)
    {
        $date = Carbon::createFromFormat('m-d-Y', $date)->format('m/d/Y');
        $serviceIds = $request->input('serviceIdsSelected');
        $salonId = $request->salonId;
        $slotIds = $request->selectedOption;
        $employeeType = $request->appointmentType??'1';
        if (!$serviceIds) {
            return view('website.ajax.not_avalible_employee_card_website_ajax');
        }
        $customerSlots = CustomerSlot::where('date', $date)
            ->where('salon_id', $salonId)
            ->whereIn('slot_id', $slotIds);
        if ($request->employeeId) {
            $customerSlots = $customerSlots->where('employee_id', $request->employeeId);
        }
        $customerSlots = $customerSlots->pluck('appointment_id')->unique()->toArray();
        $appointmentsDate = CustomerAppointment::whereIn('id', $customerSlots)
            ->where('customer_appointment_date', $date)
            ->whereNotIn('status', ['Cancel'])
            ->pluck('id');
        $allAssignedEmployeeIds = AssignedCustomer::whereIn('appointment_id', $appointmentsDate)
            ->pluck('employee_id');
        $employeeLeavesIds = EmployeeLeave::whereIn('salon_id', [$salonId])
            ->whereRaw('? BETWEEN date_to AND date_from', [$date])
            ->pluck('employee_id');
        $noEmployees = $employeeLeavesIds->merge($allAssignedEmployeeIds)->toArray();
        $employeesQuery = EmployeeService::whereNotIn('employee_id', $noEmployees)
            ->whereIn('salon_service_id', $serviceIds)
            ->with('getEmployees');
        if ($request->employeeId) {
            $employeesQuery = $employeesQuery->where('employee_id', $request->employeeId);
        }
        $employee = $employeesQuery->get();
        $groupedEmployees = $employee->groupBy('employee_id');
        $filteredEmployees = $groupedEmployees->filter(function ($employeeGroup) use ($serviceIds) {
            $employeeServiceIds = $employeeGroup->pluck('salon_service_id')->unique();
            return $employeeServiceIds->intersect($serviceIds)->count() === count($serviceIds);
        })->map(function ($employeeGroup) {
            return $employeeGroup->first()->getEmployees;
        })->filter()->flatten(1)->unique('id')->values();
//        $filteredEmployees = $filteredEmployees->flatten(1)->unique('employee_id')->values();
//        $employees = $employeesQuery->get();
//        $filteredEmployees = $employees->flatMap(function ($employeeGroup) use ($serviceIds) {
//            $employeeServiceIds = $employeeGroup->pluck('salon_service_id')->unique();
//            if ($employeeServiceIds->intersect($serviceIds)->count() === count($serviceIds)) {
//                return $employeeGroup->getEmployees->filter(function ($employee) use ($employeeType) {
//                    return !$employeeType || $employee->employeeType->salon_type_id == $employeeType;
//                });
//            }
//            return collect();
//        });
//        return $filteredEmployees = $filteredEmployees->unique(function ($employee) {
//            return $employee->id;
//        });
        $uniqueEmployeeIds = $filteredEmployees->pluck('id')->unique();
        if ($uniqueEmployeeIds->count() > 0) {
            return view('website.ajax.avalible_employee_card_website_ajax', compact('filteredEmployees', 'uniqueEmployeeIds', 'serviceIds', 'salonId', 'employeeType'));
        } else {
            return view('website.ajax.not_avalible_employee_card_website_ajax');
        }
    }

    public function avalibleEmployee(Request $request, $date = null, $id = null)
    {
        $date = Carbon::createFromFormat('m-d-Y', $date)->format('m/d/Y');
        $serviceIds = $request->input('service_ids');
        $salonId = $request->salonId;
        $slotIds = $id;
        if (isset($serviceIds) && $serviceIds != null) {
            $customerSlots = CustomerSlot::where('date', $date)->where('salon_id', $salonId)->whereIn('slot_id', [$slotIds])->pluck('appointment_id')->unique()->toArray();
            $appointmentsDate = CustomerAppointment::whereIn('id', $customerSlots)->where('customer_appointment_date', $date)->whereNotIn('status', ['Cancel'])->pluck('id');
            $allAssignedEmployeeIds = AssignedCustomer::whereIn('appointment_id', $appointmentsDate)->pluck('employee_id');
            $employeeLeavesIds = EmployeeLeave::whereIn('salon_id', [$salonId])
                ->whereRaw('? BETWEEN date_to AND date_from', $date)
                ->pluck('employee_id');
            $noEmployees = $employeeLeavesIds->merge($allAssignedEmployeeIds)->toArray();
            $employee = EmployeeService::whereNotIn('employee_id', $noEmployees)->whereIn('salon_service_id', $serviceIds)->with('getEmployees')->get();
            $groupedEmployees = $employee->groupBy('employee_id');
            $employee = $groupedEmployees->filter(function ($employeeGroup) use ($serviceIds) {
                $employeeServiceIds = $employeeGroup->pluck('salon_service_id')->unique();
                return $employeeServiceIds->intersect($serviceIds)->count() === count($serviceIds);
            });
            if ($employee->count() > 0) {
                return view('website.ajax.avalible_employee_ajax', compact('date', 'employee'));
            } else {
                return view('website.ajax.not_avalible_slot_ajax');
            }
        } else {
            return view('website.ajax.not_avalible_slot_ajax');
        }
        return view('website.ajax.not_avalible_slot_ajax');
    }

    public function avalibleAppointmentEmployee(Request $request, $date = null, $id = null)
    {
        $serviceIds = $request->input('service_ids');
        $date = date('d/m/Y', strtotime($date));
        if (isset($serviceIds) && $serviceIds != null) {
            $AllemployeeIds = EmployeeService::whereIn('salon_service_id', $serviceIds)
                ->groupBy('employee_id')
                ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
                ->pluck('employee_id');
            $employeeIds = array_unique($AllemployeeIds);
            $avalibleEmployee = User::whereIn('id', $employeeIds)->get();
            if ($avalibleEmployee->count() > 0) {
                return view('website.ajax.avalible_employee_tab_ajax', compact('avalibleEmployee'));
            }
        } else {
            return view('website.ajax.not_avalible_slot_ajax');
        }
        return view('website.ajax.not_avalible_slot_ajax');
    }

//    public function avalibleSlotAppointmentEmployee(Request $request, $date = null, $id = null)
//    {
//        $date = Carbon::createFromFormat('m-d-Y', $date)->format('m/d/Y');
//        $serviceIds = $request->input('service_ids');
//        $salonId = Auth::user()->salon_id;
//        if (isset($serviceIds) && $serviceIds != null) {
//            $employeeIds = EmployeeService::whereIn('salon_service_id', $serviceIds)
//                ->groupBy('employee_id')
//                ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
//                ->pluck('employee_id');
//            $allAppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date', [$date])->whereNotIn('status', ['Pending'])->where('customer_slot_id', $id)->pluck('id');
//            $allAssignedAppointments = AssignedCustomer::whereIn('appointment_id', $allAppointmentsDate)->pluck('employee_id');
//            $avalibleEmployee = EmployeeService::whereIn('salon_service_id', $serviceIds)->whereNotIn('employee_id', $allAssignedAppointments)->with('getEmployees')->get();
//            if ($avalibleEmployee->count() > 0) {
//                return view('website.ajax.avalible_employee_tab_ajax', compact('avalibleEmployee', 'serviceIds', 'salonId'));
//            }
//        } else {
//            return view('website.ajax.not_avalible_slot_ajax');
//        }
//    }

    public function avalibleSlotAppointmentEmployee(Request $request, $date = null, $id = null)
    {
        $date = Carbon::createFromFormat('m-d-Y', $date)->format('m/d/Y');
        $serviceIds = $request->input('service_ids');
        $salonId = Auth::user()->salon_id; // Use Auth for salon ID
        $slotIds = [$id]; // Use customer slot ID as slotIds

        if (isset($serviceIds) && $serviceIds != null) {
            // Get customer slots based on the selected date, salon, and slot ID
            $customerSlots = CustomerSlot::where('date', $date)
                ->where('salon_id', $salonId)
                ->whereIn('slot_id', $slotIds)
                ->pluck('appointment_id')
                ->unique()
                ->toArray();

            // Get appointments that are not canceled and match the date
            $appointmentsDate = CustomerAppointment::whereIn('id', $customerSlots)
                ->where('customer_appointment_date', $date)
                ->whereNotIn('status', ['Cancel'])
                ->pluck('id');

            // Get employees assigned to the selected appointments
            $allAssignedEmployeeIds = AssignedCustomer::whereIn('appointment_id', $appointmentsDate)
                ->pluck('employee_id');

            // Fetch employees on leave on the selected date
            $employeeLeavesIds = EmployeeLeave::whereIn('salon_id', [$salonId])
                ->whereRaw('? BETWEEN date_to AND date_from', $date)
                ->pluck('employee_id');

            // Merge assigned and on-leave employees to exclude them from available employees
            $noEmployees = $employeeLeavesIds->merge($allAssignedEmployeeIds)->toArray();

            // Fetch employees who are not in the no-employee list and offer the selected services
            $employee = EmployeeService::whereNotIn('employee_id', $noEmployees)
                ->whereIn('salon_service_id', $serviceIds)
                ->with('getEmployees')
                ->get();

            // Group employees by their ID and filter those who offer all selected services
            $groupedEmployees = $employee->groupBy('employee_id');
            $employee = $groupedEmployees->filter(function ($employeeGroup) use ($serviceIds) {
                $employeeServiceIds = $employeeGroup->pluck('salon_service_id')->unique();
                return $employeeServiceIds->intersect($serviceIds)->count() === count($serviceIds);
            });

            if ($employee->count() > 0) {
                return view('website.ajax.avalible_employee_tab_ajax', compact('employee', 'serviceIds', 'salonId'));
            } else {
                return view('website.ajax.not_avalible_employee_tab_ajax');
            }
        } else {
            return view('website.ajax.not_avalible_employee_tab_ajax');
        }
    }


//    PENDING MODIFICATION
//    public function avalibleAppointmentEmployee(Request $request, $date = null, $id = null){
//        $serviceIds = $request->input('service_ids');
//        $date =  date('d/m/Y', strtotime($date));
//        $employee_id_array=[];
//        if(isset($serviceIds) && $serviceIds!=null){
//            $employeeIds = EmployeeService::whereIn('salon_service_id', $serviceIds)
//            ->groupBy('employee_id')
//            ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
//            ->pluck('employee_id');
//            if ($employeeIds->count() > 0) {
//                $result = $employeeIds->toArray();
//                $allAppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date',[$date])->pluck('customer_slot_id');
//                if(isset($allAppointmentsDate) && $allAppointmentsDate->count('customer_slot_id')==0){
//                    $avalibleEmployee = User::whereIn('id',$employeeIds)->get();
//                    return view('website.ajax.avalible_employee_tab_ajax', compact('avalibleEmployee'));
//                }else{
//                    if ($employeeIds->count()>1) {
//                        $countedValues = array_count_values($result);
//                        foreach ($countedValues as $element => $count) {
//                            if ($employeeIds->count()<=$count) {
//                                array_push($employee_id_array,$element);
//                            }
//                        }
//                        return $employee_id_array;die;
//                        $salonSlots = Slot::where('salon_id',$id)->where('avalible_status','Active')->whereNotIn('id',$slot_id_array)->get();
//                        return view('website.ajax.appointment_slot_ajax',compact('salonSlots','date'));
//
//                    }else{
//                        $salonSlots = Slot::where('salon_id',$id)->where('avalible_status','Active')->whereNotIn('id',$allAppointmentsDate)->get();
//                        return view('website.ajax.appointment_slot_ajax',compact('salonSlots','date'));
//                    }
//                }
//            } else {
//                return view('website.ajax.not_avalible_slot_ajax');
//            }
//            $avalibleEmployee = User::whereIn('id',$employeeIds)->get();
//            if($avalibleEmployee->count() > 0){
//                return view('website.ajax.avalible_employee_tab_ajax', compact('avalibleEmployee'));
//            }
//        }else{
//            return view('website.ajax.not_avalible_slot_ajax');
//        }
//        return view('website.ajax.not_avalible_slot_ajax');
//    }
    public function liveEmployeeAppointmentSlots(Request $request, $id = null, $employeeId = null)
    {
        $array_val = [];
        $slot_id_array = [];
        $date = $request->date;
        $serviceIds = $request->input('service_ids');
        $currentDateTime = date('h:i A');
        if (isset($serviceIds) && $serviceIds != null) {
            $employeeIds = EmployeeService::whereIn('salon_service_id', $serviceIds)
                ->groupBy('employee_id')
                ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
                ->pluck('employee_id');
            if ($employeeIds->count() > 0) {
                $result = $employeeIds->toArray();
                $allAppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date', [$date])->pluck('customer_slot_id');
                if (isset($allAppointmentsDate) && $allAppointmentsDate->count('customer_slot_id') == 0) {
                    $salonSlots = Slot::where('salon_id', $id)
                        ->where('avalible_status', 'Active')
                        ->get();
                    return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date'));
                } else {
                    if ($employeeIds->count() > 1) {
                        $countedValues = array_count_values($allAppointmentsDate->toArray());
                        foreach ($countedValues as $element => $count) {
                            if ($employeeIds->count() <= $count) {
                                array_push($slot_id_array, $element);
                            }
                        }
                        $salonSlots = Slot::where('salon_id', $id)
                            ->where('avalible_status', 'Active')
                            ->whereNotIn('id', $slot_id_array)
                            ->whereRaw("STR_TO_DATE(start_time, '%h:%i %p') >= STR_TO_DATE(?, '%h:%i %p')", [$currentDateTime])
                            ->orderByRaw("STR_TO_DATE(start_time, '%h:%i %p') ASC")
                            ->get();
                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date'));
                    } else {
                        $salonSlots = Slot::where('salon_id', $id)
                            ->where('avalible_status', 'Active')
                            ->whereNotIn('id', $allAppointmentsDate)
                            ->whereRaw("STR_TO_DATE(start_time, '%h:%i %p') >= STR_TO_DATE(?, '%h:%i %p')", [$currentDateTime])
                            ->orderByRaw("STR_TO_DATE(start_time, '%h:%i %p') ASC")
                            ->get();
                        return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date'));
                    }
                }
            } else {
                return view('website.ajax.not_avalible_slot_ajax');
            }
        } else {
            return view('website.ajax.not_avalible_slot_ajax');
        }
    }

    public function liveAppointmentSlots(Request $request, $id = null)
    {
        $date = $request->date;
        $serviceIds = $request->input('service_ids');
        $currentDateTime = date('h:i A');
        if (isset($serviceIds) && $serviceIds != null) {
            $employeeIds = EmployeeService::whereIn('salon_service_id', $serviceIds)
                ->groupBy('employee_id')
                ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
                ->pluck('employee_id');
            if ($employeeIds->count() > 0) {
                $result = $employeeIds->toArray();
                $allAppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date', [$date])->pluck('customer_slot_id');
                if (isset($allAppointmentsDate) && $allAppointmentsDate->count('customer_slot_id') == 0) {
                    $salonSlots = Slot::where('salon_id', $id)->where('avalible_status', 'Active')->get();
                    return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date'));
                } else {
                    $salonSlots = Slot::where('salon_id', $id)->where('avalible_status', 'Active')->whereNotIn('id', $allAppointmentsDate)->get();
                    return view('website.ajax.appointment_slot_ajax', compact('salonSlots', 'date'));
                }
            } else {
                return view('website.ajax.not_avalible_slot_ajax');
            }
        } else {
            return view('website.ajax.not_avalible_slot_ajax');
        }
        return view('website.ajax.not_avalible_slot_ajax');
    }
//    public function avalibleEmployee(Request $request, $date = null, $id = null){
//        $serviceIds = $request->input('service_ids');
//        if(isset($serviceIds) && $serviceIds!=null){
//            // return $employeeIds = EmployeeService::whereIn('salon_service_id', [$serviceIds])->pluck('employee_id');
//            $employeeIds = EmployeeService::whereIn('salon_service_id', $serviceIds)
//            ->groupBy('employee_id')
//            ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
//            ->pluck('employee_id');
//            if ($employeeIds->count() > 0) {
//                $result = $employeeIds->toArray();
//                $allAppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date',[$date])->pluck('customer_slot_id');
//                if(isset($allAppointmentsDate) && $allAppointmentsDate->count('customer_slot_id')==0){
//                    $employee = User::findOrFail($employeeIds);
//                    return view('website.ajax.avalible_employee_ajax',compact('date','employee'));
//                }else{
//                    $employee = User::whereHas(
//                        'roles', function($q){
//                        $q->where('name', 'employee');
//                    }
//                    )->where('salon_id',Auth::id())->orderBy('id','DESC')->get();
//                    return view('website.ajax.avalible_employee_ajax',compact('date','employee'));
//                }
//            } else {
//                return view('website.ajax.not_avalible_slot_ajax');
//            }
//        }else{
//            return view('website.ajax.not_avalible_slot_ajax');
//        }
//        return view('website.ajax.not_avalible_slot_ajax');
//    }

    public function appointmentAssigned($id, $slug, $employeeId)
    {
        $salon = User::findOrFail($id);
        $employee = User::findOrFail($employeeId);
        $categories = ServiceCategory::whereIn('id', $employee->employeeServiceCategoryIds)->orderBy('id', 'DESC')->get();
        $productCategory = ServiceCategory::whereIn('id', $salon->allEmployeeProductCategoryIds)->orderBy('id', 'DESC')->get();
        $services = SalonService::whereIn('id', $employee->EmployeeServiceIds)->get();
        $products = Product::where('salon_id', $id)->where('product_type_id', 1)->get();
        $google_registered_user = [];
        // $salon = User::findOrFail($id);
        return view('website.appointment', compact('id', 'services', 'products', 'categories', 'productCategory', 'salon', 'employeeId', 'google_registered_user'));
    }//end contactUs function.

    public function editCustomerFatora(Request $request)
    {
        $profile = $request->all();
        Profile::where('user_id', $request->user_id)->update($profile);
        return back()->with('flash_message', trans('messages.update'));
    }

    public function products()
    {
//        $products = Product::where('product_type_id', 1)->get();
        $products=Product::get();
//        $testimonials = Testimonial::where('status', 1)->get();
        return view('website.products', compact('products'));
    }//end contactUs function.

    public function productDetail($id)
    {
        $product = Product::findOrFail($id);
        $avalibleProducts = Product::whereNotIn('id', [$id])->where('product_type_id', 1)->where('salon_id', $product->salon_id)->get();
        $ratings = Rating::where('salon_id', $product->salon_id)->get();
        return view('website.product_detail', compact('product', 'avalibleProducts', 'ratings'));
    }//end contactUs function.

    public function packages()
    {
        $monthlySubscriptionPlans = SubscriptionPlan::where('status', 1)->whereHas('subscriptionType', function ($q) {
            $q->where('name', 'monthly');
        })->get();
        $yearlySubscriptionPlans = SubscriptionPlan::where('status', 1)->whereHas('subscriptionType', function ($q) {
            $q->where('name', 'yearly');
        })->get();
        $freeTrailSubscriptionPlans = SubscriptionPlan::where('status', 1)->whereHas('subscriptionType', function ($q) {
            $q->where('name', 'Free Trail');
        })->get();
        return view('website.packages', compact('monthlySubscriptionPlans', 'yearlySubscriptionPlans', 'freeTrailSubscriptionPlans'));
    }//end contactUs function.

    public function signup(Request $request, $id = null)
    {
        if ($id != null && $subscriptionPlan = SubscriptionPlan::find($id)) {
            $salonTypes = SalonType::where('status', 1)->get();
            if (Session::has('google_registered_user')) {
                $google_registered_user = Session::get('google_registered_user');
                $package_subscription_session = Session::get('package_id_for_google_signup');
            } else {
                $google_registered_user = [];
            }
            return view('auth.register', compact('subscriptionPlan', 'salonTypes', 'google_registered_user'));
        } else {
            return redirect()->back()->with(['title' => trans('messages.Fail'), 'message' => trans('messages.Unabletoprocesstryagain'), 'type' => 'error']);;
        }//end if else.
    }//end signup function.

    public function editCustomer($appointment_id)
    {
        $appointment = CustomerAppointment::findOrFail($appointment_id);
        $salon = User::findOrFail($appointment->salon_id);
        $customer = User::where('id', $appointment->customer_id)->get();
        $services = SalonService::whereIn('id', $appointment->customerAppointmentIdemployee->employee->employeeServiceIds)->get();
        $products = Product::where('salon_id', $appointment->salon_id)->where('product_type_id', 1)->get();
        $categories = ServiceCategory::whereIn('id', $appointment->customerAppointmentIdemployee->employee->employeeServiceCategoryIds)->orderBy('id', 'DESC')->get();
        $productCategory = ServiceCategory::whereIn('id', $appointment->salonId->allEmployeeProductCategoryIds)->orderBy('id', 'DESC')->get();
        $userSubscription = UserSubscription::where('user_id', $salon->id)->orderBy('id', 'DESC')->first();
        $slots = Slot::whereIn('id',$appointment->allCustomerSlotId)->get();
        return view('website.ajax.customer_edit_ajax', compact('appointment_id', 'appointment', 'services', 'products', 'categories', 'productCategory', 'salon', 'userSubscription','slots'));
    }//end generateMasterFile function.
    public function newUpcomingAppointment(){
        $branches = User::where('salon_id', Auth::user()->salon_id)
            ->orWhere('id', Auth::user()->salon_id)
            ->whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->orderBy('id','ASC')->get();
        $branchIds = $branches->pluck('id');
        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
            ->orderBy('user_id', 'ASC')
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy('user_id');
        $currentDate = now();
        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
            $dueDate = $subs->first()->fatoraPdf->due_date;
            return $dueDate->isPast();
        })->keys();
        $salon = Auth::user();
        $customer_ids = CustomerAppointment::whereIn('salon_id', $branchIds)->whereNotIn('salon_id',$expiredUserIds)->pluck('customer_id');
        $companyCustomer = User::whereIn('salon_id',$branchIds)
            ->whereNotIn('salon_id',$expiredUserIds)
            ->whereHas('roles', function($q){
                $q->where('name', 'customer');
            })
            ->get();
        $appointmentCustomer = User::whereIn('id', $customer_ids)->get();
        $customers = $companyCustomer->merge($appointmentCustomer)->sortByDesc('id')->values();
        $services = SalonService::where('is_deleted','1')->where('salon_id', $salon->id)->get();
        $products = Product::where('is_deleted','1')->where('salon_id', $salon->id)->where('product_type_id', 1)->get();
        $categories = ServiceCategory::whereIn('id', $salon->allEmployeeServiceCategoryIds ?? [])->orderBy('id', 'DESC')->get();
        $productCategory = ServiceCategory::whereIn('id', $salon->allEmployeeProductCategoryIds ?? [])->orderBy('id', 'DESC')->get();
        $userSubscription = UserSubscription::where('user_id', $salon->id)->orderBy('id', 'DESC')->first();
        $slots = Slot::where('salon_id', $salon->id)->get();
        return view('website.ajax.upcoming_customer_ajax', compact('customers','services', 'products', 'categories', 'productCategory', 'salon', 'userSubscription', 'slots'));
    }

    public function editCategoryService(Request $request){
        if($request->appointmentId != null){
            $salon = User::findOrFail($request->salonId);
            $appointment = CustomerAppointment::where('id',$request->appointmentId)->first();
            $services = SalonService::whereIn('category_id', $request->categoryIds)->whereIn('id', $appointment->customerAppointmentIdemployee->employee->employeeServiceIds)->get();
            return view('website.ajax.edit_services', compact('services', 'salon','appointment'));
        }else{
            $salon = User::findOrFail($request->salonId);
            $services = SalonService::whereIn('category_id', $request->categoryIds)->get();
            return view('website.ajax.edit_services', compact('services', 'salon'));
        }
    }
    public function editCategoryProduct(Request $request){
        $salon = User::findOrFail($request->salonId);
        $appointment = CustomerAppointment::where('id',$request->appointmentId)->first();
        $products = Product::whereIn('product_category_id', $request->categoryIds)->where('salon_id', $salon->id)->get();
        return view('website.ajax.edit_products', compact('products', 'salon','appointment'));
    }
    public function editCustomerAppointmentSlots(Request $request, $date = null, $id = null)
    {
        $array_val = [];
        $slot_id_array = [];
        $date = Carbon::createFromFormat('m-d-Y', $date)->format('m/d/Y');
        $currentDateTime = date('h:i A');
        if (date('m-d-Y') == $request->date) {
            $serviceIds = $request->input('serviceIdsSelected');
            $serviceCount = count($serviceIds);
            if (isset($serviceIds) && $serviceIds != null) {
                $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
                    ->whereRaw('? BETWEEN date_to AND date_from', $date)
                    ->pluck('employee_id');
                $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)->whereIn('salon_service_id', $serviceIds)
                    ->groupBy('employee_id')
                    ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
                    ->pluck('employee_id');
                if ($employeeIds->count() > 0) {
                    $AppointmentsDate = CustomerAppointment::where('salon_id', $id)->whereIn('customer_appointment_date', [$date])->whereNotIn('status', ['Cancel'])->pluck('customer_slot_id');
                    $allAppointmentsDate = $AppointmentsDate
                        ->map(function ($slotId) {
                            return json_decode($slotId);
                        })
                        ->flatten();
                    if ($allAppointmentsDate->isEmpty()) {
                        $salonSlots = Slot::where('salon_id', $id)
                            ->where('avalible_status', 'Active')
                            ->whereRaw("STR_TO_DATE(start_time, '%h:%i %p') >= STR_TO_DATE(?, '%h:%i %p')", [$currentDateTime])
                            ->orderByRaw("STR_TO_DATE(start_time, '%h:%i %p') ASC")
                            ->get();
                        return view('website.ajax.edit_appointment_slots_ajax', compact('salonSlots', 'date', 'serviceCount'));
                    } else {
                        $countedValues = array_count_values($allAppointmentsDate->toArray());
                        foreach ($countedValues as $element => $count) {
                            if ($employeeIds->count() <= $count) {
                                array_push($slot_id_array, $element);
                            }
                        }
                        $salonSlots = Slot::where('salon_id', $id)
                            ->where('avalible_status', 'Active')
                            ->whereNotIn('id', $slot_id_array)
                            ->whereRaw("STR_TO_DATE(start_time, '%h:%i %p') >= STR_TO_DATE(?, '%h:%i %p')", [$currentDateTime])
                            ->orderByRaw("STR_TO_DATE(start_time, '%h:%i %p') ASC")
                            ->get();
                        return view('website.ajax.edit_appointment_slots_ajax', compact('salonSlots', 'date', 'serviceCount'));
                    }
                } else {
                    return view('website.ajax.not_avalible_slots_website_ajax');
                }
            } else {
                return view('website.ajax.not_avalible_slots_website_ajax');
            }
        } else {
            $serviceIds = $request->input('serviceIdsSelected');
            $serviceCount = count($serviceIds);
            if (isset($serviceIds) && $serviceIds != null) {
                $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
                    ->whereRaw('? BETWEEN date_to AND date_from', $date)
                    ->pluck('employee_id');
                $employeeIds = EmployeeService::whereNotIn('employee_id', $employeeLeaves)->whereIn('salon_service_id', $serviceIds)
                    ->groupBy('employee_id')
                    ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($serviceIds)])
                    ->pluck('employee_id');
                if ($employeeIds->count() > 0) {
                    $result = $employeeIds->toArray();
                    $AppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date', [$date])->pluck('customer_slot_id');
                    $allAppointmentsDate = $AppointmentsDate
                        ->map(function ($slotId) {
                            return json_decode($slotId);
                        })
                        ->flatten();
                    if ($allAppointmentsDate->isEmpty()) {
                        $salonSlots = Slot::where('salon_id', $id)->where('avalible_status', 'Active')->get();
                        return view('website.ajax.edit_appointment_slots_ajax', compact('salonSlots', 'date', 'serviceCount'));
                    } else {
                        $countedValues = array_count_values($allAppointmentsDate->toArray());
                        foreach ($countedValues as $element => $count) {
                            if ($employeeIds->count() <= $count) {
                                array_push($slot_id_array, $element);
                            }
                        }
                        $salonSlots = Slot::where('salon_id', $id)
                            ->where('avalible_status', 'Active')
                            ->whereNotIn('id', $slot_id_array)
                            ->get();
                        return view('website.ajax.edit_appointment_slots_ajax', compact('salonSlots', 'date', 'serviceCount'));
                    }
                } else {
                    return view('website.ajax.not_avalible_slots_website_ajax');
                }
            } else {
                return view('website.ajax.not_avalible_slots_website_ajax');
            }
        }
        return view('website.ajax.not_avalible_slots_website_ajax');
    }
    public function editAvalibleSlotAppointmentEmployee(Request $request, $date = null, $id = null)
    {
        $date = Carbon::createFromFormat('m-d-Y', $date)->format('m/d/Y');
        $serviceIds = $request->input('serviceIdsSelected');
        $salonId = $request->salonId;
        $slotIds = $request->selectedOption;
        if (isset($serviceIds) && $serviceIds != null) {
            $customerSlots = CustomerSlot::where('date', $date)->where('salon_id', $salonId)->whereIn('slot_id', $slotIds)->pluck('appointment_id')->unique()->toArray();
            $appointmentsDate = CustomerAppointment::whereIn('id', $customerSlots)->where('customer_appointment_date', $date)->whereNotIn('status', ['Cancel'])->pluck('id');
            $allAssignedEmployeeIds = AssignedCustomer::whereIn('appointment_id', $appointmentsDate)->pluck('employee_id');
            $employeeLeavesIds = EmployeeLeave::whereIn('salon_id', [$salonId])
                ->whereRaw('? BETWEEN date_to AND date_from', $date)
                ->pluck('employee_id');
            $noEmployees = $employeeLeavesIds->merge($allAssignedEmployeeIds)->toArray();
            $employee = EmployeeService::whereNotIn('employee_id', $noEmployees)->whereIn('salon_service_id', $serviceIds)->with('getEmployees')->get();
            $groupedEmployees = $employee->groupBy('employee_id');
            $employee = $groupedEmployees->filter(function ($employeeGroup) use ($serviceIds) {
                $employeeServiceIds = $employeeGroup->pluck('salon_service_id')->unique();
                return $employeeServiceIds->intersect($serviceIds)->count() === count($serviceIds);
            });
            if ($employee->count() > 0) {
                return view('website.ajax.edit_avalible_employee_card_ajax', compact('employee', 'serviceIds', 'salonId'));
            } else {
                return 'false';
            }
        } else {
            return 'false';
        }
    }
    public function updateCustomer(Request $request)
    {
        extract($request->all());
        $user = User::findOrFail($id);
        $userId = $request->id;
        $phoneDefault = str_replace(' ', '', $request->phone);
        $user->update(['phone'=>$phoneDefault??Null]);
        Profile::where('user_id',$user->id)->update(['phone'=>$request->phone??Null,'address'=>$request->address??Null,'dob'=>$request->dob??Null]);
        $appointment = CustomerAppointment::findOrFail($request->customer_appointment_id);
        if (!empty($request->customer_appointment_id) && !empty($request->customer_service_id) && !empty($request->customer_slot_id) && !empty($request->employee_id)) {
            $appointment = CustomerAppointment::updateOrCreate(
                ['customer_id' => $user->id, 'id' => $request->customer_appointment_id],
                [
                    'customer_appointment_date' => $request->customer_appointment_date,
                    'customer_slot_id' => json_encode($request->customer_slot_id),
                    'status' => 'Approved',
                    'salon_id' => $request->salon_id,
                ]
            );
            $assigned = AssignedCustomer::updateOrCreate(
                [
                    'salon_id' => $salon_id,
                    'appointment_id' => $appointment->id,
                    'customer_id' => $user->id,
                ],
                [
                    'assigned_user_id' => Auth::id(),
                    'employee_id' => $request->employee_id
                ]
            );
            CustomerSlot::where('appointment_id', $appointment->id)->delete();
            foreach ($request->customer_slot_id as $slotId) {
                CustomerSlot::create([
                    'date' => $request->customer_appointment_date,
                    'salon_id' => $request->salon_id,
                    'employee_id' => $request->employee_id,
                    'slot_id' => $slotId,
                    'appointment_id' => $appointment->id,
                    'status' => 'Pending'
                ]);
            }
            CustomerService::where('appointment_id', $appointment->id)->delete();
            CustomerServiceCategory::where('appointment_id', $appointment->id)->delete();
            foreach ($request->customer_service_id as $serviceId) {
                $CustomerService = CustomerService::create([
                    'customer_id' => $userId,
                    'salon_service_id' => $serviceId,
                    'appointment_id' => $appointment->id
                ]);
                CustomerServiceCategory::create([
                    'customer_service_id' => $CustomerService->id,
                    'service_category_id' => $CustomerService->salonService->getServiceCategory->id,
                    'appointment_id' => $appointment->id ?? null
                ]);
            }
        }
        if (!empty($request->customer_product_id)) {
            $productIds = $request->customer_product_id;
            $products = Product::whereIn('id', $productIds)->get();
            $totalAmountWithVat = 0;
            $totalAmountWithoutVat = 0;
            foreach ($products as $product) {
                if ($product->productCurrentInventory) {
                    $priceWithoutVat = $product->productCurrentInventory->price;
                    $totalAmountWithoutVat += $priceWithoutVat;
                }
            }
            $admin = Profile::where('user_id', 2)->first();
            $totalAmountWithVat = $totalAmountWithoutVat + (($admin->vat / 100) * $totalAmountWithoutVat);
            $purchaseOrderId = $appointment->appointmentPurchaseOrder->id ?? null;
            $purchaseOrder = PurchaseOrder::updateOrCreate(
                ['user_id' => $userId, 'id' => $purchaseOrderId],
                [
                    "salon_id" => $request->salon_id,
                    "notes" => $request->notes??'',
                    "vat" => $admin->vat,
                    "total_amount_with_vat" => $totalAmountWithVat,
                    "total_amount_without_vat" => $totalAmountWithoutVat,
                    'date' => $request->customer_appointment_date ?? date('Y-m-d'),
                    'appointment_id' => $appointment->id ?? null,
                    'status' => "sales",
                    'total_quantity' => count($productIds)
                ]
            );
            if (is_array($productIds)) {
                CustomerProduct::where('appointment_id', $appointment->id)->delete();
                foreach ($productIds as $productId) {
                    $CustomerProduct = CustomerProduct::create([
                        'customer_id' => $user->id,
                        'salon_product_id' => $productId,
                        'appointment_id' => $appointment->id ?? null
                    ]);
                    CustomerProductCategory::create([
                        'customer_product_id' => $CustomerProduct->id,
                        'product_category_id' => $CustomerProduct->salonProduct->product_category_id,
                        'appointment_id' => $appointment->id ?? null
                    ]);
                }
            }
            foreach ($productIds as $productId) {
                $productInventory = ProductInventory::where('product_id', $productId)
                    ->where('is_deleted','0')
                    ->where('expiry_date', '>=', now())
                    ->where('quantity', '>', 0)
                    ->first();
                if ($productInventory) {
                    $productInventory->quantity--;
                    $productInventory->consumed_quantity++;
                    $productInventory->save();
                    StockOut::create([
                        'product_id' => $productId,
                        'quantity' => 1,
                        'price_per_product' => $productInventory->price,
                        'total_price_per_product' => $productInventory->price,
                        'salon_id' => $request->salon_id,
                        'purchase_order_id' => $purchaseOrder->id,
                        'sku_id' => $productInventory->sku_id,
                    ]);
                }
            }
        }
        return back()->with('flash_message', __('messages.customer_updated'));
    }

    public function signupProcess(Request $request)
    {
        extract($request->all());
        $subscriptionPlan = SubscriptionPlan::findOrFail($subscription_plan_id);
        $amount = $subscriptionPlan->price + $subscriptionPlan->tax;
        $description = $subscriptionPlan->subscriptionType->name . " Subscription of `" . $card_first_name . " " . $card_last_name . "`
        Including tax.
            Billing Address:$billing_address,
            Country:$country,
            City:$city,
            State:$state,
            Zip:$zip_code.
        ";
        $response = [];
        if ($request->hasFile('vat_certification')) {
            $vatCertification = Storage::disk('website')->put('vat_certification', $request->vat_certification);
        } else {
            $vatCertification = (NULL);
        }
        if ($request->hasFile('trade_certification')) {
            $tradeCertification = Storage::disk('website')->put('trade_certification', $request->trade_certification);
        } else {
            $tradeCertification = (NULL);
        }
        $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
        $current_date = $date->format('Y-m-d H:i:s');
        try {
            if (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 1) {
                $due_date = $date->modify('+30 days')->format('Y-m-d');
                $baseUrl = url('sales_payment');
                $cardNumber = str_replace(' ', '', $request->card_number);
                $payload = [
                    'payer_country' => 'SA',
                    'payer_address' => $request->billing_address,
                    'order_amount' => number_format($amount, 2),
                    'action' => 'SALE',
                    'card_cvv2' => $request->cvc_number,
                    'payer_zip' => $request->zip_code,
                    'payer_ip' => request()->ip(),
                    'order_currency' => 'SAR',
                    'payer_first_name' => $request->card_first_name,
                    'card_exp_month' => explode('/', $request->expiry_date)[0],
                    'payer_city' => $request->city,
                    'card_exp_year' => explode('/', $request->expiry_date)[1],
                    'payer_last_name' => $request->card_last_name,
                    'payer_phone' => $request->phone,
                    'order_description' => $description,
                    'payer_email' => $request->email,
                    'card_number' => $cardNumber,
                    'vatCertification' => $vatCertification,
                    'tradeCertification' => $tradeCertification,
                    'subscription_plan_id' => $subscription_plan_id,
                    'name' => $name,
                    'card_first_name' => $card_first_name,
                    'card_last_name' => $card_last_name,
                    'billing_address' => $billing_address,
                    'country' => $country,
                    'city' => $city,
                    'state' => $state,
                    'zip_code' => $zip_code,
                    'phone' => $phone,
                    'email' => $email,
                    'password' => $password,
                    'salon_type_id' => $salon_type_id,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'vat_number' => $vat_number,
                    'vat_certification_expiry_date' => $vat_certification_expiry_date,
                    'trade_certification_expiry_date' => $trade_certification_expiry_date,
                    'due_date' => $due_date,
                    'type' => 'new_user_subscription',
                ];
                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ])->asForm()->get($baseUrl, $payload);
                return $response;
            } elseif (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 2) {
                $due_date = $date->modify('+1 year')->format('Y-m-d');
                $baseUrl = url('sales_payment');
                $cardNumber = str_replace(' ', '', $request->card_number);
                $payload = [
                    'payer_country' => 'SA',
                    'payer_address' => $request->billing_address,
                    'order_amount' => number_format($amount, 2),
                    'action' => 'SALE',
                    'card_cvv2' => $request->cvc_number,
                    'payer_zip' => $request->zip_code,
                    'payer_ip' => request()->ip(),
                    'order_currency' => 'SAR',
                    'payer_first_name' => $request->card_first_name,
                    'card_exp_month' => explode('/', $request->expiry_date)[0],
                    'payer_city' => $request->city,
                    'card_exp_year' => explode('/', $request->expiry_date)[1],
                    'payer_last_name' => $request->card_last_name,
                    'payer_phone' => $request->phone,
                    'order_description' => $description,
                    'payer_email' => $request->email,
                    'card_number' => $cardNumber,
                    'vatCertification' => $vatCertification,
                    'tradeCertification' => $tradeCertification,
                    'subscription_plan_id' => $subscription_plan_id,
                    'name' => $name,
                    'card_first_name' => $card_first_name,
                    'card_last_name' => $card_last_name,
                    'billing_address' => $billing_address,
                    'country' => $country,
                    'city' => $city,
                    'state' => $state,
                    'zip_code' => $zip_code,
                    'phone' => $phone,
                    'email' => $email,
                    'password' => $password,
                    'salon_type_id' => $salon_type_id,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'vat_number' => $vat_number,
                    'vat_certification_expiry_date' => $vat_certification_expiry_date,
                    'trade_certification_expiry_date' => $trade_certification_expiry_date,
                    'due_date' => $due_date,
                    'type' => 'new_user_subscription',
                ];
                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ])->asForm()->get($baseUrl, $payload);
                return $response;
            }
        } catch (\Exception $e) {
            return redirect()->back()->with(['title' => trans('messages.Oops!'), 'message' => trans('messages.Something went wrong, try again'), 'type' => 'error']);;
        }
    }

    public function createNewSubscriber(Request $request)
    {
        extract($request->all());
        $subscriptionPlan = SubscriptionPlan::find($subscription_plan_id);
        if ($subscriptionPlan->price != null) {
            $amount = $subscriptionPlan->price + $subscriptionPlan->tax;
        } else {
            $amount = "Free";
        }

        $full_name = $request->name;
        $full_name_no_space = str_replace(' ', '', $full_name);
        $name_parts = explode(' ', $full_name);
        $first_name = array_shift($name_parts);
        $last_name = implode(' ', $name_parts);
        $description = $subscriptionPlan->subscriptionType->name . " Subscription of `" . $first_name . " " . $last_name . "`
            Including tax.
                Billing Address:$billing_address,
                Country:$country,
                City:$city,
                State:$state,
                Zip:$zip_code.
        ";
        $company = User::whereHas(
            'roles', function ($q) {
            $q->where('name', 'user');
        }
        )->first();
        $invoice_number = rand('11111111', '99999999');
        $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
        $current_date = $date->format('Y-m-d H:i:s');
        try {
            if (isset($subscriptionPlan->id) && $subscriptionPlan->id == 8) {
                $due_date = $date->modify('+30 days')->format('Y-m-d');
                if ($request->hasFile('vat_certification')) {
                    $vatCertification = Storage::disk('website')->put('vat_certification', $request->vat_certification);
                } else {
                    $vatCertification = (NULL);
                }
                if ($request->hasFile('trade_certification')) {
                    $tradeCertification = Storage::disk('website')->put('trade_certification', $request->trade_certification);
                } else {
                    $tradeCertification = (NULL);
                }
                $phone = $request->phone;
//                $user = User::create(['name' => $first_name . ' ' . $last_name, 'first_name' => $first_name, 'last_name' => $last_name, 'email' => $email, 'password' => bcrypt($password), 'salon_type_id' => 3, 'appointment_type_id' => 2, 'register_status' => "Accepted", 'phone' => $phone, 'show_password' => $password]);
                $user = User::create(['name' => $first_name . ' ' . $last_name, 'first_name' => $first_name, 'last_name' => $last_name, 'email' => $email, 'password' => bcrypt($password), 'salon_type_id' => 1, 'appointment_type_id' => 2, 'register_status' => "Accepted", 'phone' => $phone, 'show_password' => $password]);
                $link = url('salon_detail', ['id' => $user->id]) . '/' . $full_name_no_space;
                Profile::create(['user_id' => $user->id, 'phone' => $request->phone, 'pic' => 'no_image.png', 'address' => $billing_address, 'latitude' => $latitude, 'link' => $link, 'longitude' => $longitude, 'city' => $city, 'state' => $state, 'postal' => $zip_code, 'country' => $country, 'vat_number' => $vat_number, 'vat_certification' => $vatCertification, 'vat_certification_expiry_date' => $vat_certification_expiry_date, 'trade_certification' => $tradeCertification, 'trade_certification_expiry_date' => $trade_certification_expiry_date]);
                $userSubscription = UserSubscription::create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $subscription_plan_id,
                    'amount_captured' => null,
                    'captured_status' => "Free",
                    'captured_at' => $current_date,
                    'currency' => null,
                    'invoice_id' => null,
                    'charge_id' => null,
                    'description' => $description,
                    'customer_id' => null,
                    'product_id' => null,
                    'price_id' => null,
                ]);
                FatoraInvoice::create(['user_subscription_id' => $userSubscription->id, 'company_name' => $company->name, 'company_address' => $company->profile->address, 'company_vat_number' => $company->profile->vat_number, 'commercial_registration_number' => 38833738, 'customer_name' => $first_name . ' ' . $last_name, 'customer_address' => $billing_address, 'customer_vat_number' => $vat_number, 'total_amount' => $amount, 'unit_price' => "Free", 'vat_amount' => 1 * 15, 'quantity' => 1, 'description' => $subscriptionPlan->description, 'invoice_number' => $invoice_number, 'current_date' => $current_date, 'due_date' => $due_date]);
                $user->roles()->attach([1 => ['role_id' => 3, 'user_id' => $user->id]]);
            } elseif (isset($subscriptionPlan->id) && $subscriptionPlan->id == 9) {
                $due_date = $date->modify('+1 year')->format('Y-m-d');
                if ($request->hasFile('vat_certification')) {
                    $vatCertification = Storage::disk('website')->put('vat_certification', $request->vat_certification);
                } else {
                    $vatCertification = (NULL);
                }
                if ($request->hasFile('trade_certification')) {
                    $tradeCertification = Storage::disk('website')->put('trade_certification', $request->trade_certification);
                } else {
                    $tradeCertification = (NULL);
                }
                $phone = $request->phone;
                $user = User::create(['name' => $first_name . ' ' . $last_name, 'first_name' => $first_name, 'last_name' => $last_name, 'email' => $email, 'password' => bcrypt($password), 'salon_type_id' => 3, 'appointment_type_id' => 2, 'register_status' => "Accepted", 'phone' => $phone, 'show_password' => $password]);
                $link = url('salon_detail', ['id' => $user->id]) . '/' . $full_name_no_space;
                Profile::create(['user_id' => $user->id, 'phone' => $request->phone, 'pic' => 'no_image.png', 'address' => $billing_address, 'latitude' => $latitude, 'link' => $link, 'longitude' => $longitude, 'city' => $city, 'state' => $state, 'postal' => $zip_code, 'country' => $country, 'vat_number' => $vat_number, 'vat_certification' => $vatCertification, 'vat_certification_expiry_date' => $vat_certification_expiry_date, 'trade_certification' => $tradeCertification, 'trade_certification_expiry_date' => $trade_certification_expiry_date]);
                $userSubscription = UserSubscription::create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $subscription_plan_id,
                    'amount_captured' => null,
                    'captured_status' => "Free",
                    'captured_at' => $current_date,
                    'currency' => null,
                    'invoice_id' => null,
                    'charge_id' => null,
                    'description' => $description,
                    'customer_id' => null,
                    'product_id' => null,
                    'price_id' => null,
                ]);
                FatoraInvoice::create(['user_subscription_id' => $userSubscription->id, 'company_name' => $company->name, 'company_address' => $company->profile->address, 'company_vat_number' => $company->profile->vat_number, 'commercial_registration_number' => 38833738, 'customer_name' => $first_name . ' ' . $last_name, 'customer_address' => $billing_address, 'customer_vat_number' => $vat_number, 'total_amount' => $amount, 'unit_price' => "Free", 'vat_amount' => 1 * 15, 'quantity' => 1, 'description' => $subscriptionPlan->description, 'invoice_number' => $invoice_number, 'current_date' => $current_date, 'due_date' => $due_date]);
                $user->roles()->attach([1 => ['role_id' => 3, 'user_id' => $user->id]]);
            } elseif (isset($subscriptionPlan->id) && $subscriptionPlan->id == 10) {
                $due_date = $date->modify('+50 year')->format('Y-m-d');
                if ($request->hasFile('vat_certification')) {
                    $vatCertification = Storage::disk('website')->put('vat_certification', $request->vat_certification);
                } else {
                    $vatCertification = (NULL);
                }
                if ($request->hasFile('trade_certification')) {
                    $tradeCertification = Storage::disk('website')->put('trade_certification', $request->trade_certification);
                } else {
                    $tradeCertification = (NULL);
                }
                $phone = $request->phone;

                $user = User::create(['name' => $first_name . ' ' . $last_name, 'first_name' => $first_name, 'last_name' => $last_name, 'email' => $email, 'password' => bcrypt($password), 'salon_type_id' => 3, 'appointment_type_id' => 2, 'register_status' => "Accepted", 'phone' => $phone, 'show_password' => $password]);
                $link = url('salon_detail', ['id' => $user->id]) . '/' . $full_name_no_space;
                Profile::create(['user_id' => $user->id, 'phone' => $request->phone, 'pic' => 'no_image.png', 'address' => $billing_address, 'latitude' => $latitude, 'link' => $link, 'longitude' => $longitude, 'city' => $city, 'state' => $state, 'postal' => $zip_code, 'country' => $country, 'vat_number' => $vat_number, 'vat_certification' => $vatCertification, 'vat_certification_expiry_date' => $vat_certification_expiry_date, 'trade_certification' => $tradeCertification, 'trade_certification_expiry_date' => $trade_certification_expiry_date]);
                $userSubscription = UserSubscription::create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $subscription_plan_id,
                    'amount_captured' => null,
                    'captured_status' => "Free",
                    'captured_at' => $current_date,
                    'currency' => null,
                    'invoice_id' => null,
                    'charge_id' => null,
                    'description' => $description,
                    'customer_id' => null,
                    'product_id' => null,
                    'price_id' => null,
                ]);
                $fatoraInvoice = FatoraInvoice::create(['user_subscription_id' => $userSubscription->id, 'company_name' => $company->name, 'company_address' => $company->profile->address,
                    'company_vat_number' => $company->profile->vat_number, 'commercial_registration_number' => 38833738, 'customer_name' => $first_name . ' ' . $last_name,
                    'customer_address' => $billing_address, 'customer_vat_number' => $vat_number, 'total_amount' => $amount, 'unit_price' => "Free", 'vat_amount' => 1 * 15,
                    'quantity' => 1, 'description' => $subscriptionPlan->description, 'invoice_number' => $invoice_number, 'current_date' => $current_date, 'due_date' => $due_date]);
                $user->roles()->attach([1 => ['role_id' => 3, 'user_id' => $user->id]]);
            }
            $admin = User::findOrFail(2);
//            $data = array(
//                'name'  => $first_name . ' ' . $last_name,
//                'email'    => $email,
//                'amount' => $amount,
//                'description' => $subscriptionPlan->descriptionDetails,
//                'welcome_message' => 'Welcome',
//                'information_message' => 'Shop Registration Successful!',
//                'detail' => env('APP_URL'),
//                'login_url' => env('APP_URL'),
//                'description' => $description,
//                'site_url' => env('APP_URL'),
//                'trial_end_date' => $due_date,
//                'type' => 'shopRegister'
//            );
            $data = array(
                'user_id'             => $user->id,
                'name'                => $first_name . ' ' . $last_name,
                'description'         => $description,
                'vat_certification' => $vatCertification,
                'vat_certification_expiry_date' => $vat_certification_expiry_date,
                'trade_certification' => $tradeCertification,
                'trade_certification_expiry_date' => $trade_certification_expiry_date,
                'email'               => $email,
                'captured_at'         => $current_date,
                'amount'              => $amount,
                'free_package'        => 'Free Package',
                'package_name'        => $subscriptionPlan->name,
                'welcome_message'     => 'Welcome',
                'information_message' => 'Welcome to LIINK! Your Free Trial Awaits',
                'detail'              => env('APP_URL'),
                'login_url'           => env('APP_URL'),
                'description'         => $subscriptionPlan->descriptionDetails,
                'site_url'            => env('APP_URL'),
                'support_phone'       => $admin->phone??'',
                'support_email'       => $admin->email,
                'trial_end_date'      => $due_date,
            );
            $notifyData = array(
                'name'  => $first_name . ' ' . $last_name,
                'email'    => $email,
                'amount' => $amount,
                'description' => $subscriptionPlan->descriptionDetails,
                'welcome_message' => 'Welcome',
                'information_message' => 'Shop Registration Successful!',
                'detail' => env('APP_URL'),
                'login_url' => env('APP_URL'),
                'description' => $description,
                'site_url' => env('APP_URL'),
                'trial_end_date' => $due_date,
                'type' => 'shopRegister'
            );
            $custom = CustomNotification::create(
                [
                    'notifiable_id'=> $admin->id,
                    'notifiable_type'=> 'App\User',
                    'type'=> 'NewUserRegistered',
                    'data'=> $notifyData,
                ]
            );
            Mail::send('website.email_templates.registration_welcome_free_trail_email', ['data' => $data], function ($message) use ($data) {
                $message->to($data['email'], $data['name'])->bcc('<EMAIL>', 'Usman Dev')->subject('مرحبًا بك في LIINK! تجربتك المجانية بإنتظارك');
            });
            return redirect(url('userSubscription/user-subscription'));
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }
    public function fatoraPdf($id)
    {
        $fatora = FatoraInvoice::findOrFail($id);
        $company_name = $fatora->company_name;
        $company_address = $fatora->company_address;
        $invoice_number = $fatora->invoice_number;
        $date = $fatora->current_date;
        $fatora->user_subscription_id;
        $qrCode = QrCode::size(300)->generate(json_encode($fatora));
        $qrCodeFinal = ($qrCode);
        $admin = User::findOrFail(2);
        return view('website.pdf.fatora', compact('qrCodeFinal', 'fatora', 'admin'));//['fatora'=>$fatora,'qrCodeFinal'=>$qrCodeFinal]);
        $pdf = PDF::loadView('website.pdf.fatora', ['fatora' => $fatora, 'qrCodeFinal' => $qrCodeFinal]);
        $fileName = 'E-Invoice';
        return $pdf->stream($fileName . '.pdf');
    }
    public function addonFatoraPdf($id,$slug){
        if ($slug == "cashier"){
            $fatora = PremiumAddonSalonCashier::findOrFail($id);
        }else{
            $fatora = PremiumAddonSalonEmployee::findOrFail($id);
        }
        $qrCode = QrCode::size(300)->generate(json_encode($fatora));
        $qrCodeFinal = ($qrCode);
        $admin = User::findOrFail(2);
        return view('website.pdf.addon_fatora', compact('qrCodeFinal', 'admin','fatora'));
    }

    public function customerFatoraPdf($id)
    {
        $customerFatora = CustomerAppointment::findOrFail($id);
        $services = SalonService::where('salon_id', $customerFatora->salon_id)->get();
        $products = Product::where('salon_id', $customerFatora->salon_id)->where('product_type_id', 1)->get();
        $admin = User::FindOrFail(2);
        $vat = $admin->profile->vat;
        $qrCode = QrCode::size(300)->generate(json_encode($customerFatora));
        $qrCodeFinal = ($qrCode);
        return view('website.pdf.customer_fatora', compact('qrCodeFinal', 'customerFatora', 'services', 'vat', 'products'));
    }

    public function previewFatoraPdf($id)
    {
        $customerFatora = CustomerAppointment::findOrFail($id);
        if (Auth::user()->roles[0]->name == "cashier") {
            $products = Product::where('salon_id', Auth::user()->salon_id)->where('product_type_id', 1)->get();
            $services = SalonService::where('salon_id', Auth::user()->salon_id)->get();
        } else {
            $products = Product::where('salon_id', Auth::user()->id)->where('product_type_id', 1)->get();
            $services = SalonService::where('salon_id', Auth::user()->id)->get();
        }
        $admin = User::FindOrFail(2);
        $vat = $admin->profile->vat;
        // $qrCode = QrCode::size(300)->generate(json_encode($customerFatora));
        // $qrCodeFinal = ($qrCode);
        return view('website.pdf.customer_fatora', compact('customerFatora', 'services', 'vat', 'products'));
    }

    public function dashboard(Request $request)
    {
        $userRole = Auth::user()->roles[0]->name;
        if ($userRole == 'spa_salon') {
            $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
            $current_date = $date->format('Y-m-d');
            $userSubscriptionDueDate = UserSubscription::where('user_id', Auth::id())->orderBy('id', 'DESC')->first()->fatoraPdf->due_date;
            if ($current_date > $userSubscriptionDueDate) {
                return redirect(url('update_subscription/' . Auth::id()));
            } else if (Auth::user()->salon_setting_updated == 1) {
                return redirect('business_dashboard');
            } else {
                return redirect(url('salon_setting/' . Auth::id()));
            }
        } elseif ($userRole == 'cashier') {
            $salon = User::findOrFail(Auth::user()->salon_id);
            $allCategoriesIds = SalonService::where('salon_id', $salon->id)->pluck('category_id')->unique()->toArray();
            $serviceCategories = ServiceCategory::where('salon_id', $salon->id)->orWhereNull('salon_id')->orderBy('id', 'DESC')->get();
            $productCategories = ServiceCategory::whereIn('id', $salon->allEmployeeProductCategoryIds)->orderBy('id', 'DESC')->get();
            $services = SalonService::where('salon_id', Auth::user()->salon_id)->get();
            $products = Product::where('salon_id', Auth::user()->salon_id)->where('product_type_id', 1)->get();
            $all_customers_ids = CustomerAppointment::where('salon_id', Auth::user()->salon_id)->pluck('customer_id');
            $customers = User::whereIn('id', $all_customers_ids)->orderBy('id', 'DESC')->get();
            $userSubscription = UserSubscription::where('user_id', $salon->id)->orderBy('id', 'DESC')->first();
            $allCustomers = CustomerAppointment::where('salon_id', Auth::user()->salon_id)->orderBy('id', 'DESC')->get();
            $currentDate = Carbon::now()->format('m/d/Y');
            $upcomingAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
                ->where('customer_appointment_date', '>', $currentDate)
                ->whereIn('status', ['Pending', 'Approved'])
                ->orderBy('customer_appointment_date', 'asc')
                ->get();
            $currentAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
                ->where('customer_appointment_date', $currentDate)
                ->whereIn('status', ['Pending', 'Approved'])
                ->orderBy('customer_appointment_date', 'asc')
                ->get();
            $completeAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
                ->where('status', 'Complete')
                ->orderBy('customer_appointment_date', 'asc')
                ->get();
            $cancelAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
                ->where('status', 'Cancel')
                ->orderBy('customer_appointment_date', 'asc')
                ->get();
            $activeAppointments = AssignedCustomer::where('salon_id', Auth::user()->salon_id)->orderBy('id', 'DESC')->get();
            $employee = $employee = User::whereHas(
                'roles', function ($q) {
                $q->where('name', 'employee');
            }
            )->where('salon_id', Auth::user()->salon_id)->orderBy('id', 'DESC')->get();
            return view('dashboard.cashierDashboard.index', compact('services', 'products', 'salon', 'customers', 'allCustomers', 'activeAppointments', 'employee', 'serviceCategories', 'productCategories', 'allCategoriesIds', 'userSubscription', 'upcomingAppointments', 'currentAppointments', 'completeAppointments', 'cancelAppointments'));
        } elseif ($userRole == 'employee') {
            return redirect('assigned_appointments');
        } elseif ($userRole == 'customer') {
            return redirect('customer_dashboard');
        } else {
            return redirect('admin_dashboard');
        }
    }
    public function appointmentStatus(Request $request)
    {
        if ($request->val == "Pending") {
            CustomerAppointment::where('id', $request->id)->update(['status' => "Pending"]);
            try {
                $appointments = CustomerAppointment::findOrFail($request->id);
                $customer = $appointments->customer->name;
                $customerEmail = $appointments->customer->email;
                $salon = User::findOrFail($appointments->salon_id);
                $salon_name = $salon->name;
                $salon_picture = $salon->profile->pic;
                $status = ucwords($appointments->status);
                $map_link = $salon->profile->location_link;
                $data = array(
                    'name' => $customer,
                    'email' => $customerEmail,
                    'status' => $status,
                    'salon_name' => $salon_name,
                    'salon_picture' => $salon_picture,
                    'map_link' => $map_link,
                    'welcome_message' => 'Welcome',
                    'information_message' => 'Appointment Successful',
                    'detail' => env('APP_URL'),
                    'review' => env('APP_REVIEW'),
                    'login_url' => env('APP_URL'),
                    'site_url' => env('APP_URL'),
                );
                $custom = CustomNotification::create(
                    [
                        'notifiable_id' => $salon,
                        'notifiable_type' => 'App\Models\User',
                        'type' => 'SalonPendingAppointment',
                        'data' => $data,
                    ]
                );
                Mail::send('website.email_templates.appointment_customer_email', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Aftab Ali')->subject('Salon Appointment');
                });
                return response()->json(['message' => "Status changed", 'title' => "Success", 'type' => 'success',]);
            } catch (\Exception $e) {
                return response()->json(['message' => trans('messages.Status updated successfully, but unable to send email to customer,') . $e->getMessage(), 'type' => 'error', 'title' => 'Fail',]);
            }
        } elseif ($request->val == "Approved") {
            CustomerAppointment::where('id', $request->id)->update(['status' => "Approved"]);
            try {
                $appointments = CustomerAppointment::findOrFail($request->id);
                $customer = $appointments->customer->name;
                $customerEmail = $appointments->customer->email;
                $salon = User::findOrFail($appointments->salon_id);
                $salon_name = $salon->name;
                $salon_picture = $salon->profile->pic;
                $status = ucwords($appointments->status);
                $map_link = $salon->profile->location_link;
                $data = array(
                    'name' => $customer,
                    'email' => $customerEmail,
                    'status' => $status,
                    'salon_name' => $salon_name,
                    'salon_picture' => $salon_picture,
                    'map_link' => $map_link,
                    'welcome_message' => 'Welcome',
                    'information_message' => 'Appointment Successful',
                    'detail' => env('APP_URL'),
                    'review' => env('APP_REVIEW'),
                    'login_url' => env('APP_URL'),
                    'site_url' => env('APP_URL'),
                );
                $custom = CustomNotification::create(
                    [
                        'notifiable_id' => $salon,
                        'notifiable_type' => 'App\Models\User',
                        'type' => 'SalonApprovedAppointment',
                        'data' => $data,
                    ]
                );
                Mail::send('website.email_templates.appointment_customer_email', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Aftab Ali')->subject('Salon Appointment');;
                });
                return response()->json(['message' => trans('messages.Appointment Status Updated Successful'), 'type' => 'success', 'title' => 'Success',]);
            } catch (\Exception $e) {
                return response()->json(['message' => trans('messages.Status updated successfully, but unable to send email to customer,') . $e->getMessage(), 'type' => 'error', 'title' => 'Fail',]);
            }
        } elseif ($request->val == "Cancel") {
            CustomerAppointment::where('id', $request->id)->update(['status' => "Cancel"]);
            try {
                $appointments = CustomerAppointment::findOrFail($request->id);
                $customer = $appointments->customer->name;
                $customerEmail = $appointments->customer->email;
                $salon = User::findOrFail($appointments->salon_id);
                $salon_name = $salon->name;
                $salon_picture = $salon->profile->pic;
                $status = ucwords($appointments->status);
                $map_link = $salon->profile->location_link;
                $data = array(
                    'name' => $customer,
                    'email' => $customerEmail,
                    'status' => $status,
                    'salon_name' => $salon_name,
                    'salon_picture' => $salon_picture,
                    'map_link' => $map_link,
                    'welcome_message' => 'Welcome',
                    'information_message' => 'Appointment Successful',
                    'detail' => env('APP_URL'),
                    'review' => env('APP_REVIEW'),
                    'login_url' => env('APP_URL'),
                    'site_url' => env('APP_URL'),
                );
                $custom = CustomNotification::create(
                    [
                        'notifiable_id' => $salon,
                        'notifiable_type' => 'App\Models\User',
                        'type' => 'SalonCancelAppointment',
                        'data' => $data,
                    ]
                );
                Mail::send('website.email_templates.appointment_customer_email', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Aftab Ali')->subject('Salon Appointment');;
                });
                return response()->json(['message' => trans('messages.Appointment Status Updated Successful'), 'type' => 'success', 'title' => 'Success',]);
            } catch (\Exception $e) {
                return response()->json(['message' => trans('messages.Status updated successfully, but unable to send email to customer,') . $e->getMessage(), 'type' => 'error', 'title' => 'Fail',]);
            }
        } elseif ($request->val == "Complete") {
//            return $request->val;
            CustomerAppointment::where('id', $request->id)->update(['status' => "Complete"]);
            try {
                $invoice_number = rand(111111111,999999999);
                $invoice_issue_date = date('Y-m-d');
                $date_of_supply = date('Y-m-d');
                $requestData = (['appointment_id'=>$request->id,'invoice_number'=>$invoice_number,'invoice_issue_date'=>$invoice_issue_date,'date_of_supply'=>$date_of_supply]);
                $fatora = CustomerFatora::create($requestData);
                $customerFatora = CustomerAppointment::findOrFail($request->id);
                if (Auth::user()->roles[0]->name == "cashier") {
                    $products = Product::where('salon_id', Auth::user()->salon_id)
                        ->where('product_type_id', 1)
                        ->get();
                    $services = SalonService::where('salon_id', Auth::user()->salon_id)->get();
                } else {
                    $products = Product::where('salon_id', Auth::user()->id)
                        ->where('product_type_id', 1)
                        ->get();
                    $services = SalonService::where('salon_id', Auth::user()->id)->get();
                }
                $admin = User::findOrFail(2);
                $vat = $admin->profile->vat;
                $customerFatoraData = json_encode($customerFatora);
                $encodedCustomerFatoraData = urlencode($customerFatoraData);
                $qrCodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?data=' . $encodedCustomerFatoraData . '&size=300x300';
                $qrCodeImageData = file_get_contents($qrCodeUrl);
                $fileName = 'qr_code_image.png';
                Storage::disk('website')->put('qr-codes/' . $customerFatora->id . '/' . $fileName, $qrCodeImageData);
                if (isset($customerFatora->appointmentPurchaseOrder) && $customerFatora->appointmentPurchaseOrder->appointment_id == $customerFatora->id) {
                    $fatoraUrl = url('stockOut_fatora/' . $customerFatora->appointmentPurchaseOrder->id);
                } elseif (isset($customerFatora->customerAppointmentFatora) && $customerFatora->customerAppointmentFatora != null) {
                    $fatoraUrl = url('customer_fatora/' . $customerFatora->id);
                } else {
                    $fatoraUrl = url('customer_fatora/' . $customerFatora->id);
                }
                $data = [
                    'name' => $customerFatora->customer->name,
                    'salon_picture' => $customerFatora->salonId->profile->pic,
                    'email' => $customerFatora->customer->email,
                    'salon_name' => $customerFatora->salonId->name,
                    'salon_email' => $customerFatora->salonId->email,
                    'appointment_date' => date("d/m/y", strtotime($customerFatora->customer_appointment_date)),
                    'id' => $customerFatora->id,
                    'fatora_url' => $fatoraUrl,
                ];
//                $pdf = PDF::loadView('website.email_templates.fatora_email',compact('data','vat','products','services','customerFatora'))->setPaper('a4', 'landscape');
//                $attachments = [
//                    'data' => $pdf->output(),
//                    'name' => 'customer_fatora.pdf',
//                ];
//                Mail::send('website.email_templates.review_customer_fatora', ['data' => $data, 'pdf' => $pdf], function ($message) use ($data, $attachments) {
//                    $message->to($data['email'], $data['email'])
//                        ->bcc('<EMAIL>')
//                        ->subject('Fatoora Invoice');
//                    if (!empty($attachments)) {
//                        $message->attachData($attachments['data'], $attachments['name']);
//                    }
//                });

                $custom = CustomNotification::create(
                    [
                        'notifiable_id'   =>  $customerFatora->salonId->id,
                        'notifiable_type' => 'App\Models\User',
                        'type'            => 'SalonCompleteAppointment',
                        'data'            => $data,
                    ]
                );
                \Log::info('CustomNotification created successfully.:' . $custom);
                Mail::send('website.email_templates.review_customer_fatora', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'], $data['email'])
                        ->bcc('<EMAIL>')
                        ->subject('Fatoora Invoice');
                });
                return response()->json(['message' => trans('messages.AppointmentCompleteSuccessfully'), 'type' => 'success', 'title' => 'Success',]);
            } catch (\Exception $e) {
                return response()->json(['message' => trans('messages.Statusupdatedsuccessfully') . ' ' . trans('messages.butunabletosendemailtocustomer') . ' ' . $e->getMessage(), 'type' => 'error', 'title' => 'Fail',
                ]);
            }
        }

        return response()->json(['message' => "Status changed", 'title' => "Success",'type' => 'success']);
    }

    public function test()
    {

      return   $date = Carbon::createFromFormat('m-d-Y', '02-25-2025')->format('m/d/Y');


        return Carbon::now()->format('Y-m-d H:i:s') . ' ' . Carbon::now()->timezoneName;


        $data = array(
            'name' => 'James lee',
            'email' => '<EMAIL>',
            'welcome_message' => 'Welcome',
            'information_message' => 'Registration Successful',
            'detail' => env('APP_URL'),
            'login_url' => env('APP_URL'),
            'amount' => 5000,
            'description' => 'Donec sollicitudin molestie malesuada. Quisque velit nisi, pretium ut lacinia in, elementum id enim. Cras ultricies ligula sed magna dictum porta. Vivamus magna justo, lacinia eget consectetur sed, convallis at tellus. Vivamus magna justo, lacinia eget consectetur sed, convallis at tellus. Pellentesque in ipsum id orci porta dapibus. Curabitur non nulla sit amet nisl tempus convallis quis ac lectus. Quisque velit nisi, pretium ut lacinia in, elementum id enim. Donec sollicitudin molestie malesuada. Mauris blandit aliquet elit, eget tincidunt nibh pulvinar a.',
            'receipt_url' => 'www.google.com',
            'site_url' => env('APP_URL'),
        );

        $dt = Mail::send('website.email_templates.registration_welcome_email', ['data' => $data], function ($message) use ($data) {
            $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Aftab Ali')->subject('Registration Successful');;
        });
        var_dump($dt);
    }//end testing function.

    public function checkSalonName(Request $request)
    {
        if($request->name != null){
            if (User::where('name', $request->name)->exists()) {
                return 'yes';
            } else {
                return 'no';
            }
        }else{
            return 'no';
        }
    }//end checkEmail function.
    public function checkEmail(Request $request)
    {
        if (User::where('email', $request->email)->exists()) {
            return 'yes';
        } else {
            return 'no';
        }
    }//end checkEmail function.
    public function checkSku(Request $request)
    {
        if (ProductInventory::where('sku_id', $request->skuId)->whereHas('products', function ($query) {$query->where('salon_id', Auth::id());})->where('is_deleted','0')->exists()) {
            return 'yes';
        } else {
            return 'no';
        }
    }//end checkSku function.


    public function checkEmailOrPhone(Request $request)
    {
        if (Profile::where('phone', $request->emailOrPhone)->exists()) {
            $profile = Profile::where('phone', $request->emailOrPhone)->first();
            if($profile->user->roles[0]->name == "spa_salon"){
                if($profile->user->register_status == "Accepted"){
                    return 'yes';
                }else if($profile->user->register_status == "Pending"){
                    trans('messages.Pending_Account_Message');
                }else if($profile->user->register_status == "Hold"){
                    trans('messages.Hold_Account_Message');
                }else{
                    trans('messages.Rejected_Account_Message');
                }
            }else{
                return 'yes';
            }
        } elseif (User::where('email', $request->emailOrPhone)->orWhere('phone', $request->emailOrPhone)->exists()) {
            $user = User::where('email', $request->emailOrPhone)->orWhere('phone', $request->emailOrPhone)->first();
            if($user->roles[0]->name == "spa_salon"){
                if($user->register_status == "Accepted") {
                    return 'yes';
                }else if($user->register_status == "Pending"){
                    return trans('messages.Pending_Account_Message');
                }else if($user->register_status == "Hold"){
                    return trans('messages.Hold_Account_Message');
                }else{
                    return trans('messages.Rejected_Account_Message');
                }
            }else{
                return 'yes';
            }
        } else {
            return 'no';
        }
    }//end checkEmail function.

    public function checkPassword(Request $request)
    {
        if (Profile::where('phone', $request->emailOrPhone)->exists()) {
            $user_id = Profile::where('phone', $request->emailOrPhone)->first()->user_id;
            $user = User::where('id', $user_id)->first();
            if ($user && Hash::check($request->password, $user->password)) {
                return response()->json('yes');
            }
        } elseif (User::where('email', $request->emailOrPhone)->orWhere('phone', $request->emailOrPhone)->exists()) {
            $user = User::where('email', $request->emailOrPhone)->orWhere('phone', $request->emailOrPhone)->first();
            if ($user && Hash::check($request->password, $user->password)) {
                return response()->json('yes');
            }
        }
        return response()->json('no');
    }

    public function redirectToGoogle($subscription_plan_id = null, $salonId = null)
    {
        if ($subscription_plan_id == 111111) {
            Session::put('salon_id_for_google_signup', $salonId);
        } else {
            Session::put('package_id_for_google_signup', $subscription_plan_id);
        }
        return Socialite::driver('google')->redirect();
    }

//    public function handleGoogleCallback(Request $request)
//    {
//        $subscriptionPlanId = Session::get('package_id_for_google_signup');
//        $salonId = Session::get('salon_id_for_google_signup');
//        $googleUser = Socialite::driver('google')->user();
//        if ($subscriptionPlanId == null && $salonId == null) {
//            $findUser = User::where('email', $googleUser->email)->first();
//            Session::forget('package_id_for_google_signup');
//            Session::forget('salon_id_for_google_signup');
//            if (isset($findUser->roles[0] ) && $findUser->roles[0]->name == "spa_salon") {
//                if ($findUser->register_status == "Accepted") {
//                    Auth::login($findUser);
//                    if ($findUser->salon_setting_updated == 0) {
//                        return redirect()->route('salon_setting', ['id' => $findUser->id]);
//                    } else {
//                        return redirect('/dashboard');
//                    }
//                }
//            } elseif($findUser != null) {
//                Auth::login($findUser);
//                return redirect('/dashboard');
//            }else{
//                return redirect(url('packages'));
//            }
//        } else {
//            Session::put('google_registered_user', $googleUser);
//            if ($subscriptionPlanId !== null) {
//                $salonTypes = SalonType::where('status', 1)->get();
//                return redirect('/signup/' . $subscriptionPlanId);
//            } elseif ($salonId !== null) {
////                return  redirect('/steper_customer/' . $salonId);
//                $stepcustomer = Session::put('step_customer_google_singup', $googleUser);
//                return redirect('/appointment/' . $salonId)->with('stepcustomer', $stepcustomer);
//            }
//        }
//    }
    public function handleGoogleCallback(Request $request)
    {
        try {
            $subscriptionPlanId = Session::get('package_id_for_google_signup');
            $salonId = Session::get('salon_id_for_google_signup');
            try {
                $googleUser = Socialite::driver('google')->user();
            } catch (\Exception $e) {
                return redirect()->back()->with('error', trans('messages.Google Service not Response Please Try Again!'));
            }
            if ($subscriptionPlanId == null && $salonId == null) {
                $findUser = User::where('email', $googleUser->email)->first();
                Session::forget('package_id_for_google_signup');
                Session::forget('salon_id_for_google_signup');
                if (isset($findUser->roles[0]) && $findUser->roles[0]->name == "spa_salon") {
                    if ($findUser->register_status == "Accepted") {
                        Auth::login($findUser);
                        $token = Str::random(60);
                        $user = Auth::user();
                        $user->update(['remember_token' => $token]);
                        if ($findUser->salon_setting_updated == 0) {
                            return redirect()->route('salon_setting', ['id' => $findUser->id]);
                        } else {
                            return redirect('/dashboard');
                        }
                    }
                } elseif ($findUser != null) {
                    Auth::login($findUser);
                    $token = Str::random(60);
                    $user = Auth::user();
                    $user->update(['remember_token' => $token]);
                    return redirect('/dashboard');
                } else {
                    return redirect(url('packages'));
                }
            } else {
                Session::put('google_registered_user', $googleUser);
                if ($subscriptionPlanId !== null) {
                    $salonTypes = SalonType::where('status', 1)->get();
                    return redirect('/signup/' . $subscriptionPlanId);
                } elseif ($salonId !== null) {
                    $stepcustomer = Session::put('step_customer_google_singup', $googleUser);
                    return redirect('/appointment/' . $salonId)->with('stepcustomer', $stepcustomer);
                }
            }
        } catch (\Exception $e) {
            return $e->getMessage();
            return redirect()->back()->with('error',trans('messages.Google Service not Response Please Try Again!'));
        }
    }
    public function stepCustomerGoogle()
    {
        $googleUser = Session::get('step_customer_google_singup');
        if ($googleUser != null) {
            if ($findUser = User::where('email', $googleUser->email)->first()) {
                Auth::login($findUser);
                $token = Str::random(60);
                $findUser->update(['remember_token' => $token]);
                $phone   = $findUser->profile->phone;
                $address = $findUser->profile->address;
                $dob     = $findUser->profile->dob;
                Session::forget('step_customer_google_singup');
                return response()->json(['status' => 'login', 'action' => 'step_customer', 'data' => $findUser, 'phone' => $phone, 'address' => $address, 'dob' => $dob]);
            } else {
                Session::forget('step_customer_google_singup');
                return response()->json(['status' => 'success', 'action' => 'step_customer', 'data' => $googleUser]);
            }
        } else {
            return response()->json(['status' => 'fail', 'action' => 'step_customer']);
        }
    }

    public function googleTesting()
    {
        $data = [
//          'email' => '<EMAIL>',
            'email' => '<EMAIL>',
            'name' => 'Hafiz Saad',
        ];
        Mail::send('website.email_templates.test', ['data' => $data], function ($message) use ($data) {
            $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Usman Dev')->subject('LIINK TESTING');;
        });
        return view('website.google_testing');
    }

//    Customer Create "Admin | Cashier" Dashboard
    public function serviceCategoryAjax(Request $request)
    {
        $user     = User::findOrFail($request->salonId);
        $category = SalonService::whereIn('category_id', $request->id)->whereIn('id', $user->allEmployeeServiceIds)->get();
        return $category;
    }

    public function serviceCategoryWebsiteAppointmentAjax(Request $request)
    {
        if ($request->salonId != null && $request->employeeId == null){
            $user = User::findOrFail($request->salonId);
            $category = SalonService::whereIn('category_id', $request->ids)->whereIn('id', $user->allEmployeeServiceIds)->get();
        }elseif($request->salonId != null && $request->employeeId != null){
            $user = User::findOrFail($request->employeeId);
            $category = SalonService::whereIn('category_id', $request->ids)->whereIn('id', $user->employeeServiceIds)->get();
        }
        return view('website.ajax.service_category_website_appointment_ajax', compact('category', 'user'));
    }
    public function ProductCategoryWebsiteAppointmentAjax(Request $request)
    {
        $user = User::findOrFail($request->salonId);
//        $products = Product::whereIn('product_category_id', $request->ids)->whereIn('id', $user->allEmployeeProductCategoryIds)->get();
        $products = Product::whereIn('product_category_id', $request->ids)->get();
        return view('website.ajax.service_category_website_appointment_ajax', compact('products', 'user'));
    }
    public function productCategoryAjax(Request $request)
    {
        $products = Product::where('product_category_id', $request->id)->where('product_type_id', 1)->get();
        return $products;
    }
    public function employeeServiceCategoryAjax(Request $request)
    {
        $category = SalonService::where('is_deleted','1')->whereIn('category_id', $request->id)->where('salon_id', $request->salonId)->get();
        return $category;
    }
    public function sliderServiceCategoryAjax(Request $request)
    {
        $services = SalonService::where('salon_id', Auth::user()->salon_id)->where('category_id', $request->id)->get();
        return view('website.ajax.slider_service_ajax', compact('services'));
    }
    public function servicesCategoryTabAjax(Request $request)
    {
        $user = User::findOrFail($request->salonId);
        $services = SalonService::where('category_id', $request->id)->whereIn('id', $user->allEmployeeServiceIds)->get();
        $vat = Profile::where('user_id', '2')->first()->vat;
        return view('website.ajax.services_category_tab_ajax', compact('services', 'vat'));
    }

    public function productsCategoryTabAjax(Request $request)
    {
        $products = Product::where('product_type_id','2')->where('product_category_id', $request->id)->where('salon_id',Auth::user()->salon_id)->get();
        $vat = Profile::where('user_id', '2')->first()->vat;
        $add_new_customer = $request->add_new_customer;
        return view('website.ajax.products_category_tab_ajax', compact('products', 'vat','add_new_customer'));
    }
    public function sliderProductCategoryAjax(Request $request)
    {
        $products = Product::where('salon_id', Auth::user()->salon_id)->where('product_category_id', $request->id)->get();
        return view('website.ajax.slider_product_ajax', compact('products'));
    }
    public function upcomingAppointmentBoxAjax(Request $request)
    {
//        $upcomingCustomer = User::where('id', $request->id)->first();
        $upcomingCustomer           = CustomerAppointment::where('id', $request->appointment_id)->first();
        $customerServiceCategoryIds = $upcomingCustomer->cutomerServiceCategoryIds;
        $customerServiceCategories  = ServiceCategory::whereIn('id', $customerServiceCategoryIds)->get();
        $customerServiceIds         = $upcomingCustomer->customerServiceIds;
        $customerService            = SalonService::whereIn('id', $customerServiceIds)->get();
        $vat                        = Profile::where('user_id', 2)->first()->vat;
        $appointment                = CustomerAppointment::where('id', $request->appointment_id)->first();
        if (isset($upcomingCustomer) &&  $upcomingCustomer->customerProductIds != null) {
            $products = Product::whereIn('id', $upcomingCustomer->customerProductIds)->get();
        } else {
            $products = [];
        }
        $discount = Discount::where('id', $appointment->discount_id)->first();
        if ($discount == null) {
            $discount = "";
        }
        $assignEmployee = AssignedCustomer::where('appointment_id', $request->appointment_id)->first();
        if ($assignEmployee == null) {
            $assignEmployee = "";
        }
        $employeeIds = EmployeeService::whereIn('salon_service_id', $customerServiceIds)
            ->groupBy('employee_id')
            ->havingRaw('COUNT(DISTINCT salon_service_id) = ?', [count($customerServiceIds)])
            ->pluck('employee_id');
        $allAppointmentsDate     = CustomerAppointment::whereIn('customer_appointment_date', [$appointment->customer_appointment_date])->whereNotIn('status', ['Pending'])->where('customer_slot_id', $appointment->salon_id)->pluck('id');
        $allAssignedAppointments = AssignedCustomer::whereIn('appointment_id', $allAppointmentsDate)->pluck('employee_id');
        $avalibleEmployee        = EmployeeService::whereIn('salon_service_id', $customerServiceIds)->whereNotIn('employee_id', $allAssignedAppointments)->with('getEmployees')->get();
        return view('website.ajax.up_coming_appointment_ajax', compact( 'upcomingCustomer', 'customerService', 'customerServiceCategories', 'vat', 'avalibleEmployee', 'appointment', 'assignEmployee', 'discount','products'));
    }
    public function assignAppointment(Request $request)
    {
        if (!$request->has(['appointment_id', 'assigned_user_id', 'customer_id', 'orderDetailsRadio1', 'salon_id'])) {
            return back()->with(['message' => trans('messages.Missingrequireddata'), 'type' => 'error', 'title' => 'Error']);
        }
        $assignedCustomer = AssignedCustomer::where('appointment_id', $request->appointment_id)->first();
        if ($assignedCustomer) {
            $assigned = $assignedCustomer;
        } else {
            $assigned = AssignedCustomer::create([
                'appointment_id'   => $request->appointment_id,
                'assigned_user_id' => $request->assigned_user_id,
                'customer_id'      => $request->customer_id,
                'employee_id'      => $request->orderDetailsRadio1,
                'salon_id'         => $request->salon_id
            ]);
        }
        if ($request->appointment_id) {
            $customerAppointment = CustomerAppointment::find($request->appointment_id);
            if ($customerAppointment) {
                $customerAppointment->update(['status' => "Approved"]);
                $servicesIds = $customerAppointment->getCustomerServices ?
                    $customerAppointment->getCustomerServices->pluck('salon_service_id')->toArray() : [];
                $services = SalonService::whereIn('id', $servicesIds)->pluck('name')->toArray();
                if ($customerAppointment->appointment_type_id == 1) {
                    try {
                        $data = [
                            'salon_id'         => $request->salon_id,
                            'shopName'         => $customerAppointment->salon->name,
                            'shopPicture'      => $customerAppointment->salon->profile->pic,
                            'shopPhone'        => $customerAppointment->salon->profile->phone,
                            'shopAddress'      => $customerAppointment->salon->profile->address,
                            'shopLocationLink' => $customerAppointment->salon->profile->location_link,
                            'date'             => $customerAppointment->customer_appointment_date ? date('d M Y', strtotime($customerAppointment->customer_appointment_date)) : '',
                            'time'             => ($customerAppointment->startSlot->start_time ?? '') . ' - ' . ($customerAppointment->endSlot->end_time ?? ''),
                            'customerName'     => $assigned->customer->name,
                            'customerEmail'    => $assigned->customer->email,
                            'services'         => implode(', ', $services),
                            'appointmentType'  => $customerAppointment->appointment_type_id,
                        ];
                        $notiyData = [
                            'shopName'         => $customerAppointment->salon->name,
                            'customerName'     => $assigned->customer->name,
                            'customerEmail'    => $assigned->customer->email,
                            'services'         => implode(', ', $services),
                            'appointmentType'  => $customerAppointment->appointment_type_id,
                            'date'             => $customerAppointment->customer_appointment_date ? date('d M Y', strtotime($customerAppointment->customer_appointment_date)) : '',
                            'time'             => ($customerAppointment->startSlot->start_time ?? '') . ' - ' . ($customerAppointment->endSlot->end_time ?? ''),
                            'type'             => 'SalonApproveAppointment',
                            'template'         => 'customer',
                        ];
                        $custom =  CustomNotification::create(
                            [
                                'notifiable_id'   => $request->salon_id,
                                'notifiable_type' => 'App\Models\User',
                                'type'            => 'Appointment',
                                'data'            => $notiyData,
                            ]
                        );
                        Mail::send('website.email_templates.appointment_assign', ['data' => $data], function ($message) use ($data) {
                            $message->to($data['customerEmail'])
                                ->cc('<EMAIL>', 'Dev')
                                ->subject('Your Appointment at [' . $data['shopName'] . '] is confirmed');
                        });
                    } catch (\Exception $e) {
                        return back()->with(['message' => trans('messages.Creationsuccessfulbutunabletosendemail'), 'type' => 'error', 'title' => 'Fail']);
                    }
                    try {
                        $data = array(
                            'customerName'    => $customerAppointment->customer->name,
                            'customerEmail'   => $customerAppointment->customer->email,
                            'customerPhone'   => $customerAppointment->customer->profile->phone,
                            'customerAddress' => $customerAppointment->customer->profile->address,
                            'shopName'        => $customerAppointment->salon->name,
                            'shopPicture'     => $customerAppointment->salon->profile->pic,
                            'employeeName'    => $assigned->employee->name,
                            'employeeEmail'   => $assigned->employee->email,
                            'date'            => $customerAppointment->customer_appointment_date ? date('d M Y', strtotime($customerAppointment->customer_appointment_date)) : '',
                            'time'            => ($customerAppointment->startSlot->start_time ?? '') . ' - ' . ($customerAppointment->endSlot->end_time ?? ''),
                            'services'        => implode(', ', $services),
                            'appointmentType' => $customerAppointment->appointment_type,
                        );
                        $appointmentAssignEmployee = Mail::send('website.email_templates.appointment_assign_Employee', ['data' => $data], function ($message) use ($data) {
                            $message->to($data['employeeEmail'])
                                ->cc('<EMAIL>', 'Dev')
                                ->subject('Salon Appointment Assigned');
                        });
                        if(Auth::user()->hasRole('cashier')){
                            return 'true';
                        }else{
                            return back()->with('flash_message',trans('messages.AppointmentApprovedSuccessfully'));
                        }
                    } catch (\Exception $e) {
                        return back()->with(['message' => trans('messages.YourCreationsuccessfullybutunabletosendemail'), 'type' => 'error', 'title' => 'Fail']);
                    }
                } else if ($customerAppointment->appointment_type_id == 2) {
                    try {
                        $data = [
                            'shopName'        => $customerAppointment->salon->name,
                            'shopPicture'     => $customerAppointment->salon->profile->pic,
                            'shopPhone'       => $customerAppointment->salon->profile->phone,
                            'customerAddress' => $customerAppointment->profile->address,
                            'date'            => $customerAppointment->customer_appointment_date ? date('d M Y', strtotime($customerAppointment->customer_appointment_date)) : '',
                            'time'            => ($customerAppointment->startSlot->start_time ?? '') . ' - ' . ($customerAppointment->endSlot->end_time ?? ''),
                            'customerName'    => $assigned->customer->name,
                            'customerEmail'   => $assigned->customer->email,
                            'services'        => implode(', ', $services),
                            'appointmentType' => $customerAppointment->appointment_type_id,
                        ];
                        $custom = CustomNotification::create(
                            [
                                'notifiable_id'   => $request->salon_id,
                                'notifiable_type' => 'App\Models\User',
                                'type'            => 'CashierApproveAppointment',
                                'data'            => $data,
                            ]
                        );
                        Mail::send('website.email_templates.appointment_assign', ['data' => $data], function ($message) use ($data) {
                            $message->to($data['customerEmail'])
                                ->cc('<EMAIL>', 'Dev')
                                ->subject('Your Home Beauty Service Appointment is confirmed');
                        });
                    } catch (\Exception $e) {
                        return back()->with(['message' => trans('messages.Creationsuccessfulbutunabletosendemail'), 'type' => 'error', 'title' => 'Fail']);
                    }
                    try {
                        $data = array(
                            'customerName'    => $customerAppointment->customer->name,
                            'customerEmail'   => $customerAppointment->customer->email,
                            'customerPhone'   => $customerAppointment->customer->profile->phone,
                            'customerAddress' => $customerAppointment->customer->profile->address,
                            'shopName'        => $customerAppointment->salon->name,
                            'shopPicture'     => $customerAppointment->salon->profile->pic,
                            'employeeName'    => $assigned->employee->name,
                            'employeeEmail'   => $assigned->employee->email,
                            'date'            => $customerAppointment->customer_appointment_date ? date('d M Y', strtotime($customerAppointment->customer_appointment_date)) : '',
                            'time'            => ($customerAppointment->startSlot->start_time ?? '') . ' - ' . ($customerAppointment->endSlot->end_time ?? ''),
                            'services'        => implode(', ', $services),
                            'appointmentType' => $customerAppointment->appointment_type,
                        );
                        $custom = CustomNotification::create(
                            [
                                'notifiable_id'   => $request->salon_id,
                                'notifiable_type' => 'App\Models\User',
                                'type'            => 'AppointmentAssignCashier',
                                'data'            => $data,
                            ]
                        );
                        $appointmentAssignEmployee = Mail::send('website.email_templates.appointment_assign_Employee', ['data' => $data], function ($message) use ($data) {
                            $message->to($data['employeeEmail'])
                                ->cc('<EMAIL>', 'Dev')
                                ->subject('Salon Appointment Assigned');
                        });
                        if(Auth::user()->hasRole('cashier')){
                            return 'true';
                        }else{
                            return back()->with('flash_message',trans('messages.AppointmentApprovedSuccessfully'));
                        }
                    } catch (\Exception $e) {
                        return back()->with(['message' => trans('messages.YourCreationsuccessfullybutunabletosendemail'), 'type' => 'error', 'title' => 'Fail']);
                    }
                }
            }
        }
        return back()->with('flash_message', trans('messages.assignedAppointments'));
    }
    public function reviewAppointment(Request $request)
    {
        return $request->all();
    }
    public function searchSalons(Request $request)
    {
        $latitude  = $request->input('latitude');
        $longitude = $request->input('longitude');

        // Find salons within 40 meters of the user's location
        $salons = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_setting_updated', 1)->where('register_status', "Accepted")->get();
        $nearbySalons = [];
        foreach ($salons as $salon) {
            $distance = $this->haversine($latitude, $longitude, $salon->profile->latitude, $salon->profile->longitude);
            if ($distance <= 0.04) { // 40 meters is 0.04 kilometers
                $nearbySalons[] = $salon;
            }
        }
        Session::put('search_salons', $nearbySalons);
        return redirect('salonlisting');
        // Return the distance value as a float
        return response()->json(['distance' => $distance, 'nearbySalons' => $nearbySalons]);
    }
    private function haversine($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371; // kilometers
        $deltaLat    = deg2rad($lat2 - $lat1);
        $deltaLon    = deg2rad($lon2 - $lon1);
        $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($deltaLon / 2) * sin($deltaLon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = $earthRadius * $c;
        return $distance;
    }
    public function contactUsForm(Request $request)
    {
        $request->validate([
            'first_name'           => 'required|string|max:255',
            'last_name'            => 'required|string|max:255',
            'phone'                => 'required|string',
            'email'                => 'required|email',
            'g-recaptcha-response' => 'required',
        ]);
        $recaptchaResponse = $request->input('g-recaptcha-response');
        $secret = env('RECAPTCHA_SECRET_KEY');
        $client = new Client();
        $response = $client->post('https://www.google.com/recaptcha/api/siteverify', [
            'form_params'  => [
                'secret'   => $secret,
                'response' => $recaptchaResponse,
            ],
        ]);
        $responseBody = json_decode((string)$response->getBody(), true);
        if (!$responseBody['success']) {
            return back()
                ->with(['message' => trans('messages.PleasecompletetheCAPTCHAchallenge'), 'type' => 'error', 'title' => 'Fail'])
                ->withInput();
        } else {
            $requestData = $request->all();
            $contact     = Contact::create($requestData);
            $admin       = User::find(2);
            $data = [
                'name'    => $request->first_name . ' ' . $request->last_name,
                'email'   => $request->email,
                'phone'   => $request->phone,
                'message' => $request->message,
            ];
            $custom = CustomNotification::create(
                [
                    'notifiable_id'   => $admin->id,
                    'notifiable_type' => 'App\User',
                    'type'            => 'ContactUs',
                    'data'            => $data,
                ]
            );


            return back()->with('flash_message',trans('messages.ThanksForQuery'));
        }
    }
    public function walkInCustomerTab(Request $request){
        extract($request->all());
        $requestEmail = $request->input('email');
        $requestPhone = $request->input('phone');
        $validatedData = $request->validate([
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
        ]);
        $user = User::where(function($query) use ($requestEmail, $requestPhone) {
            if ($requestEmail) {
                $query->where('email', $requestEmail);
            }
            if ($requestPhone) {
                $query->orWhere('phone', $requestPhone);
            }
        })->first();
        if ($user == null) {
            $phoneDefault = str_replace(' ', '', $request->phone);
            $random_password = rand('11111111', '99999999');
            $user = User::create(['name' => $name, 'email' => $email, 'password' => bcrypt($random_password), 'customer_type_id' => "1", 'phone' => $phoneDefault,
                'show_password' => $random_password]);
            Profile::create(['user_id' => $user->id, 'address' => $address, 'pic' => 'no_avatar.jpg', 'phone' => $request->phone, 'dob' => $request->dob??Null, 'state' => $state,
                'city' => $city, 'country' => $country, 'latitude' => $lat, 'longitude' => $lng, 'postal' => $zip_code]);
            $user->roles()->attach([1 => ['role_id' => 5, 'user_id' => $user->id]]);
            if ($user != null) {
                if(!empty($orderDetailsBox1)) {
                    $appointment = CustomerAppointment::create(['customer_id' => $user->id, 'customer_appointment_date' => $appointment_date, 'customer_slot_id' => json_encode($customer_slot_id), 'status' => 'Approved', 'salon_id' => $salon_id]);
                    $assigned = AssignedCustomer::create(['salon_id' => $salon_id, 'appointment_id' => $appointment->id, 'customer_id' => $user->id, 'assigned_user_id' => Auth::id(), 'employee_id' => $orderDetailsRadio1]);
                    if (is_array($request->customer_slot_id)) {
                        foreach ($request->customer_slot_id as $value) {
                            $CustomerService = CustomerSlot::create(['date' => $appointment_date, 'salon_id' => $salon_id, 'employee_id' => $orderDetailsRadio1, 'slot_id' => $value,
                                'appointment_id' => $appointment->id, 'status' => 'Pending']);
                        }
                    }
                    if (is_array($request->orderDetailsBox1)) {
                        foreach ($request->orderDetailsBox1 as $value) {
                            $CustomerService = CustomerService::create(['customer_id' => $user->id, 'salon_service_id' => $value, 'appointment_id' => $appointment->id]);
                        }
                    }
                    if (is_array($request->orderDetailsBox1)) {
                        foreach ($request->orderDetailsBox1 as $value) {
                            CustomerServiceCategory::create(['customer_service_id' => $CustomerService->id, 'service_category_id' => $CustomerService->salonService->getServiceCategory->id,
                                'appointment_id' => $appointment->id]);
                        }
                    }
                    $approved = User::findOrFail($salon_id);
                    if (isset($approved->appointment_type_id) && $approved->appointment_type_id == 1) {
                        $customer = CustomerAppointment::where('id', $appointment->id)->update(['status' => "Approved"]);
                    }
                }
                if(!empty($orderDetailsBoxPro)){
                    $productIds = $orderDetailsBoxPro;
                    $products = Product::whereIn('id', $productIds)->get();
//                    $quantity = array_sum($products);
                    $quantity = array_sum($orderDetailsBoxProQuantity);
                    $totalAmountWithVat = 0;
                    $totalAmountWithoutVat = 0;
                    $pricesPerSku = [];
                    foreach ($products as $product) {
                        if ($product->productCurrentInventory) {
                            $skuId = $product->productCurrentInventory->sku_id;
                            $priceWithoutVat = $product->productCurrentInventory->price;
                            $totalAmountWithoutVat += $priceWithoutVat;
                            $pricesPerSku[$skuId] = [
                                'without_vat' => $priceWithoutVat,
                            ];
                        }
                    }
                    $admin = Profile::where('user_id',2)->first();
                    $totalAmountWithVat=$totalAmountWithoutVat+ (($admin->vat/100) * $totalAmountWithoutVat);
                    $purchaseOrder = PurchaseOrder::create([
                        "user_id" => $user->id ?? "",
                        "salon_id" => Auth::user()->salon_id ?? "",
                        "notes" => $notes ?? "",
                        "vat" => $admin->vat ?? "",
                        "total_amount_with_vat" => $totalAmountWithVat ?? 0,
                        "total_amount_without_vat" => $totalAmountWithoutVat ?? 0,
                        'date' => isset($appointment) ? $appointment->customer_appointment_date : date('Y-m-d'),
                        'appointment_id'=> isset($appointment) ? $appointment->id : null,
                        'status' => "sales",
                        'total_quantity' => $quantity
                    ]);
                    if (is_array($productIds)) {
                        foreach ($productIds as $value) {
                            $CustomerProduct = CustomerProduct::create(['customer_id'=>$user->id,'salon_product_id'=>$value, 'appointment_id'=>isset($appointment) ? $appointment->id :null]);
                            CustomerProductCategory::create(['customer_product_id'=>$CustomerProduct->id,'product_category_id'=>$CustomerProduct->salonProduct->product_category_id, 'appointment_id'=>isset($appointment) ? $appointment->id :null]);
                        }
                    }
                    if (!empty($productIds)) {
                        if (is_array($productIds)) {
                            foreach ($productIds as $key => $value) {
                                $requiredQuantity = $orderDetailsBoxProQuantity[$key];
//                                $requiredQuantity = 1;
                                $currentDate = now();
                                $productInventory = ProductInventory::where('product_id', $value)
                                    ->where('is_deleted','0')
                                    ->where('expiry_date', '>=', $currentDate)
                                    ->where('quantity', '>', 0)
                                    ->orderBy('expiry_date', 'asc')
                                    ->first();
                                if ($productInventory) {
                                    $productInventory->consumed_quantity = is_numeric($productInventory->consumed_quantity) ? $productInventory->consumed_quantity : 0;
                                    if ($productInventory->quantity >= $requiredQuantity) {
                                        $productInventory->quantity -= $requiredQuantity;
                                        $productInventory->consumed_quantity += $requiredQuantity;
                                        $productInventory->save();
                                    } else {
                                        $requiredQuantity -= $productInventory->quantity;
                                        $productInventory->consumed_quantity += $productInventory->quantity;
                                        $productInventory->quantity = 0;
                                        $productInventory->save();
                                    }
                                    StockOut::create([
                                        'product_id' => $value,
                                        'quantity' => $requiredQuantity,
                                        'price_per_product' => $productInventory->price,
                                        'total_price_per_product' => $productInventory->price,
                                        'cost_price_per_product' => $productInventory->per_cost_price ?? 0,
                                        'salon_id' =>  Auth::user()->salon_id ?? "",
                                        'purchase_order_id' => $purchaseOrder->id ?? "",
                                        'sku_id' => $productInventory->sku_id,
                                    ]);
                                }
                            }
                        }
                    }
                }
                if(isset($request->discount_number) && $request->discount_number!=null){
                    $live_date = Carbon::now();
                    $formatted_date = $live_date->format('Y-m-d');
                    $discounts = Discount::where('discount_number', $request->discount_number)
                        ->where('status', 1)
                        ->where('salon_id',Auth::user()->salon_id)
                        ->whereRaw('? BETWEEN validity_date_to AND validity_date_from', [$formatted_date])
                        ->first();
                    if($discounts!=null){
                        $qty        = $discounts->quantity;
                        $used_qty   = $discounts->quantity_count;
                        if($qty > $used_qty){
                            if ($used_qty==null){
                                $new_qty =  1;
                            }else{
                                $new_qty = $used_qty + 1;
                            }
                            Discount::where('id',$discounts->id)->update(['quantity_count'=>$new_qty]);
                            if(isset($appointment)) {
                                CustomerAppointment::where('id', $appointment->id)->update(['discount_id' => $discounts->id]);
                            }elseif(isset($purchaseOrder)){
                                PurchaseOrder::where('id', $purchaseOrder->id)->update(['discount_id' => $discounts->id]);
                            }
                        }
                    }
                }
            }
            try {
                $name = $request->name;
                $email = $request->email;
                $password = $random_password;
                $employee = $assigned->employee->name??"";
                $salon = User::findOrFail($salon_id);
                $salon_picture = $salon->profile->pic;
                $salon_name = $salon->name;
                $data = array(
                    'name' => $name,
                    'email' => $email,
                    'password' => $password,
                    'employee' => $employee,
//                    'appointment' => $appointment['customer_appointment_date'] ?? "",
                    'salon_picture' => $salon_picture,
                    'salon_name' => $salon_name,
                    'welcome_message' => 'Welcome',
                    'information_message' => 'Account Registration Successful',
                    'detail' => env('APP_URL'),
                    'login_url' => env('APP_URL'),
                    'site_url' => env('APP_URL'),
                );
                $notifyData = [
                    'name'       => $name,
                    'employee'   => $employee,
                    'salon_name' => $salon_name,
                    'type'       => 'SalonCustomerRegister',
//                    'template'   => 'CustomerByCashier',
                    'template'   => 'viewAppointmentFeedback',

                ];

                $custom = CustomNotification::create(
                    [
                        'notifiable_id' => $salon->id,
                        'notifiable_type' => 'App\Models\User',
                        'type' => 'CustomerRegister',
                        'data' => $notifyData,
                    ]
                );
//                \Log::info('Custom Notification Created:',  $custom->toArray());
                $result = Mail::send('website.email_templates.appointment_walkin_customer_email', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Aftab Ali')->subject('Registration Successful');;
                });
                return back()->with('flash_message', trans('messages.Customeradded'));
            } catch (\Exception $e) {
                return $e->getMessage();
                return back()->with(['message' => trans('messages.YourCreationsuccessfullybutunabletosendemail'), 'type' => 'error', 'title' => 'Fail']);;
            }
        } else {
            if(!empty($orderDetailsBox1)){
                $appointment = CustomerAppointment::create(['customer_id' => $user->id, 'customer_appointment_date' => $appointment_date, 'customer_slot_id' => json_encode($customer_slot_id), 'status' => 'Approved', 'salon_id' => $salon_id]);
                $assigned = AssignedCustomer::create(['salon_id' => $salon_id, 'appointment_id' => $appointment->id, 'customer_id' => $user->id, 'assigned_user_id' => Auth::id(), 'employee_id' => $orderDetailsRadio1]);
                if (is_array($request->customer_slot_id)) {
                    foreach ($request->customer_slot_id as $value) {
                        $CustomerService = CustomerSlot::create(['date' => $appointment_date, 'salon_id' => $salon_id, 'employee_id' => $orderDetailsRadio1, 'slot_id' => $value, 'appointment_id' => $appointment->id, 'status' => 'Pending']);
                    }
                }
                if (is_array($request->orderDetailsBox1)) {
                    foreach ($request->orderDetailsBox1 as $value) {
                        $CustomerService = CustomerService::create(['customer_id' => $user->id, 'salon_service_id' => $value, 'appointment_id' => $appointment->id]);
                    }
                }
                if (is_array($request->orderDetailsBox1)) {
                    foreach ($request->orderDetailsBox1 as $value) {
                        CustomerServiceCategory::create(['customer_service_id' => $CustomerService->id, 'service_category_id' => $CustomerService->salonService->getServiceCategory->id, 'appointment_id' => $appointment->id]);
                    }
                }
                $approved = User::findOrFail($salon_id);
                if (isset($approved->appointment_type_id) && $approved->appointment_type_id == 1) {
                    $customer = CustomerAppointment::where('id', $appointment->id)->update(['status' => "Approved"]);
                }
            }
            if(!empty($orderDetailsBoxPro))
            {
                $productIds = $orderDetailsBoxPro;
                $products = Product::whereIn('id', $productIds)->get();
                $quantity = array_sum($orderDetailsBoxProQuantity);
//                $quantity = array_sum($products);
                $totalAmountWithVat = 0;
                $totalAmountWithoutVat = 0;
                $pricesPerSku = [];
                foreach ($products as $product) {
                    if ($product->productCurrentInventory) {
                        $skuId = $product->productCurrentInventory->sku_id;
                        $priceWithoutVat = $product->productCurrentInventory->price;
                        $totalAmountWithoutVat += $priceWithoutVat;
                        $pricesPerSku[$skuId] = [
                            'without_vat' => $priceWithoutVat,
                        ];
                    }
                }
                $admin = Profile::where('user_id',2)->first();
                $totalAmountWithVat = $totalAmountWithoutVat+ (($admin->vat/100) * $totalAmountWithoutVat);
                $purchaseOrder = PurchaseOrder::create([
                    "user_id" => $user->id ?? "",
                    "salon_id" => Auth::user()->salon_id ?? "",
                    "notes" => $notes ?? "",
                    "vat" => $admin->vat ?? "",
                    "total_amount_with_vat" => $totalAmountWithVat ?? 0,
                    "total_amount_without_vat" => $totalAmountWithoutVat ?? 0,
                    'date' => isset($appointment) ? $appointment->customer_appointment_date : date('Y-m-d'),
                    'appointment_id'=> isset($appointment) ? $appointment->id : null,
                    'status' => "sales",
                    'total_quantity' => $quantity
                ]);
                if (is_array($productIds)) {
                    foreach ($productIds as $value)
                    {
                        $CustomerProduct = CustomerProduct::create(['customer_id'=>$user->id,'salon_product_id'=>$value, 'appointment_id'=>isset($appointment) ? $appointment->id:null]);
                        CustomerProductCategory::create(['customer_product_id'=>$CustomerProduct->id,'product_category_id'=>$CustomerProduct->salonProduct->product_category_id, 'appointment_id'=>isset($appointment) ? $appointment->id:null]);
                    }
                }
                if (!empty($productIds)) {
                    if (is_array($productIds)) {
                        foreach ($productIds as $key => $value) {
                            $requiredQuantity = $orderDetailsBoxProQuantity[$key];
//                            $requiredQuantity = 1;
                            $currentDate = now();
                            $productInventory = ProductInventory::where('product_id', $value)
                                ->where('is_deleted','0')
                                ->where('expiry_date', '>=', $currentDate)
                                ->where('quantity', '>', 0)
                                ->orderBy('expiry_date', 'asc')
                                ->first();
                            if ($productInventory) {
                                $productInventory->consumed_quantity = is_numeric($productInventory->consumed_quantity) ? $productInventory->consumed_quantity : 0;
                                if ($productInventory->quantity >= $requiredQuantity) {
                                    $productInventory->quantity -= $requiredQuantity;
                                    $productInventory->consumed_quantity += $requiredQuantity;
                                    $productInventory->save();
                                } else {
                                    $requiredQuantity -= $productInventory->quantity;
                                    $productInventory->consumed_quantity += $productInventory->quantity;
                                    $productInventory->quantity = 0;
                                    $productInventory->save();
                                }
                                StockOut::create([
                                    'product_id' => $value,
                                    'quantity' => $requiredQuantity,
                                    'price_per_product' => $productInventory->price,
                                    'total_price_per_product' => $productInventory->price,
                                    'cost_price_per_product' => $productInventory->per_cost_price ?? 0,
                                    'salon_id' => Auth::user()->salon_id ?? "",
                                    'purchase_order_id' => $purchaseOrder->id ?? "",
                                    'sku_id' => $productInventory->sku_id,
                                ]);
                            }
                        }
                    }
                }
            }
            if(isset($request->discount_number) && $request->discount_number!=null){
                $live_date = Carbon::now();
                $formatted_date = $live_date->format('Y-m-d');
                $discounts = Discount::where('discount_number', $request->discount_number)
                    ->where('status', 1)
                    ->where('salon_id',Auth::user()->salon_id)
                    ->whereRaw('? BETWEEN validity_date_to AND validity_date_from', [$formatted_date])
                    ->first();
                if($discounts!=null){
                    $qty        = $discounts->quantity;
                    $used_qty   = $discounts->quantity_count;
                    if($qty > $used_qty){
                        if ($used_qty==null){
                            $new_qty =  1;
                        }else{
                            $new_qty = $used_qty + 1;
                        }
                        Discount::where('id',$discounts->id)->update(['quantity_count'=>$new_qty]);
                        if(isset($appointment)) {
                            CustomerAppointment::where('id', $appointment->id)->update(['discount_id' => $discounts->id]);
                        }elseif(isset($purchaseOrder)){
                            PurchaseOrder::where('id', $purchaseOrder->id)->update(['discount_id' => $discounts->id]);
                        }
                    }
                }
            }
            return back()->with('flash_message',trans('messages.Customeradded'));
        }
    }
    public function customerNewAppointment(Request $request){
        extract($request->all());
        $requestEmail = $request->input('email');
        $requestPhone = $request->input('phone');
        $validatedData = $request->validate([
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
        ]);
        $user = User::where(function($query) use ($requestEmail, $requestPhone) {
            if ($requestEmail) {
                $query->where('email', $requestEmail);
            }
            if ($requestPhone) {
                $query->orWhere('phone', $requestPhone);
            }
        })->first();
        if ($user != null){
            if(!empty($customer_service_id)){
                $appointment = CustomerAppointment::create(['customer_id' => $user->id, 'customer_appointment_date' => $customer_appointment_date, 'customer_slot_id' => json_encode($customer_slot_id), 'status' => 'Approved', 'salon_id' => $salon_id]);
                $assigned = AssignedCustomer::create(['salon_id' => $salon_id, 'appointment_id' => $appointment->id, 'customer_id' => $user->id, 'assigned_user_id' => Auth::id(), 'employee_id' => $employee_id]);
                if (is_array($request->customer_slot_id)) {
                    foreach ($request->customer_slot_id as $value) {
                        $CustomerService = CustomerSlot::create(['date' => $customer_appointment_date, 'salon_id' => $salon_id, 'employee_id' => $employee_id, 'slot_id' => $value, 'appointment_id' => $appointment->id, 'status' => 'Pending']);
                    }
                }
                if (is_array($request->customer_service_id)) {
                    foreach ($request->customer_service_id as $value) {
                        $CustomerService = CustomerService::create(['customer_id' => $user->id, 'salon_service_id' => $value, 'appointment_id' => $appointment->id]);
                    }
                }
                if (is_array($request->customer_service_id)) {
                    foreach ($request->customer_service_id as $value) {
                        CustomerServiceCategory::create(['customer_service_id' => $CustomerService->id, 'service_category_id' => $CustomerService->salonService->getServiceCategory->id, 'appointment_id' => $appointment->id]);
                    }
                }
                $approved = User::findOrFail($salon_id);
                if (isset($approved->appointment_type_id) && $approved->appointment_type_id == 1) {
                    $customer = CustomerAppointment::where('id', $appointment->id)->update(['status' => "Approved"]);
                }
            }
            if(!empty($customer_product_id))
            {
                $productIds = $customer_product_id;
                $products = Product::whereIn('id', $productIds)->get();
//                $quantity = array_sum($orderDetailsBoxProQuantity);
                $quantity = $products->count();
                $totalAmountWithVat = 0;
                $totalAmountWithoutVat = 0;
                $pricesPerSku = [];
                foreach ($products as $product) {
                    if ($product->productCurrentInventory) {
                        $skuId = $product->productCurrentInventory->sku_id;
                        $priceWithoutVat = $product->productCurrentInventory->price;
                        $totalAmountWithoutVat += $priceWithoutVat;
                        $pricesPerSku[$skuId] = [
                            'without_vat' => $priceWithoutVat,
                        ];
                    }
                }
                $admin = Profile::where('user_id',2)->first();
                $totalAmountWithVat = $totalAmountWithoutVat + (($admin->vat/100) * $totalAmountWithoutVat);
                $purchaseOrder = PurchaseOrder::create([
                    "user_id" => $user->id ?? "",
                    "salon_id" => Auth::user()->salon_id ?? "",
                    "notes" => $notes ?? "",
                    "vat" => $admin->vat ?? "",
                    "total_amount_with_vat" => $totalAmountWithVat ?? 0,
                    "total_amount_without_vat" => $totalAmountWithoutVat ?? 0,
                    'date' => isset($appointment) ? $appointment->customer_appointment_date : date('Y-m-d'),
                    'appointment_id'=> isset($appointment) ? $appointment->id : null,
                    'status' => "sales",
                    'total_quantity' => $quantity
                ]);
                if (is_array($productIds)) {
                    foreach ($productIds as $value)
                    {
                        $CustomerProduct = CustomerProduct::create(['customer_id'=>$user->id,'salon_product_id'=>$value, 'appointment_id'=>isset($appointment) ? $appointment->id:null]);
                        CustomerProductCategory::create(['customer_product_id'=>$CustomerProduct->id,'product_category_id'=>$CustomerProduct->salonProduct->product_category_id, 'appointment_id'=>isset($appointment) ? $appointment->id:null]);
                    }
                }
                if (!empty($productIds)) {
                    if (is_array($productIds)) {
                        foreach ($productIds as $key => $value) {
//                            $requiredQuantity = $orderDetailsBoxProQuantity[$key];
                            $requiredQuantity = 1;
                            $currentDate = now();
                            $productInventory = ProductInventory::where('product_id', $value)
                                ->where('is_deleted','0')
                                ->where('expiry_date', '>=', $currentDate)
                                ->where('quantity', '>', 0)
                                ->orderBy('expiry_date', 'asc')
                                ->first();
                            if ($productInventory) {
                                $productInventory->consumed_quantity = is_numeric($productInventory->consumed_quantity) ? $productInventory->consumed_quantity : 0;
                                if ($productInventory->quantity >= $requiredQuantity) {
                                    $productInventory->quantity -= $requiredQuantity;
                                    $productInventory->consumed_quantity += $requiredQuantity;
                                    $productInventory->save();
                                } else {
                                    $requiredQuantity -= $productInventory->quantity;
                                    $productInventory->consumed_quantity += $productInventory->quantity;
                                    $productInventory->quantity = 0;
                                    $productInventory->save();
                                }
                                StockOut::create([
                                    'product_id' => $value,
                                    'quantity' => $requiredQuantity,
                                    'price_per_product' => $productInventory->price,
                                    'total_price_per_product' => $productInventory->price,
                                    'cost_price_per_product' => $productInventory->per_cost_price ?? 0,
                                    'salon_id' => Auth::user()->salon_id ?? "",
                                    'purchase_order_id' => $purchaseOrder->id ?? "",
                                    'sku_id' => $productInventory->sku_id,
                                ]);
                            }
                        }
                    }
                }
            }
            if(isset($request->discount_number) && $request->discount_number!=null){
                $live_date = Carbon::now();
                $formatted_date = $live_date->format('Y-m-d');
                $discounts = Discount::where('discount_number', $request->discount_number)
                    ->where('status', 1)
                    ->where('salon_id',Auth::user()->salon_id)
                    ->whereRaw('? BETWEEN validity_date_to AND validity_date_from', [$formatted_date])
                    ->first();
                if($discounts!=null){
                    $qty        = $discounts->quantity;
                    $used_qty   = $discounts->quantity_count;
                    if($qty > $used_qty){
                        if ($used_qty==null){
                            $new_qty =  1;
                        }else{
                            $new_qty = $used_qty + 1;
                        }
                        Discount::where('id',$discounts->id)->update(['quantity_count'=>$new_qty]);
                        if(isset($appointment)) {
                            CustomerAppointment::where('id', $appointment->id)->update(['discount_id' => $discounts->id]);
                        }elseif(isset($purchaseOrder)){
                            PurchaseOrder::where('id', $purchaseOrder->id)->update(['discount_id' => $discounts->id]);
                        }
                    }
                }
            }
            return back()->with('flash_message',trans('messages.Customeradded'));
        }
    }
    public function dashboardCustomerTab(Request $request)
    {
        extract($request->all());
        $user = User::where('email', $email)->first();
        if ($user == null) {
            $phoneDefault = str_replace(' ', '', $request->phone);
            $random_password = rand('11111111', '99999999');
            $user = User::create(['name' => $name, 'email' => $email, 'password' => bcrypt($random_password), 'customer_type_id' => 1, 'phone' => $phoneDefault, 'show_password' => $random_password]);
            Profile::create(['user_id' => $user->id, 'address' => $address, 'pic' => 'no_avatar.jpg', 'phone' => $request->phone, 'dob' => $dob, 'state' => $state, 'city' => $city, 'country' => $country, 'latitude' => $lat, 'longitude' => $lng, 'postal' => $zip_code]);
            if ($user != null) {
                $appointment = CustomerAppointment::create(['customer_id' => $user->id, 'customer_appointment_date' => $appointment_date, 'customer_slot_id' => json_encode($customer_slot_id), 'status' => 'Approved', 'salon_id' => $salon_id]);
                $assigned = AssignedCustomer::create(['salon_id' => $salon_id, 'appointment_id' => $appointment->id, 'customer_id' => $user->id, 'assigned_user_id' => Auth::id(), 'employee_id' => $orderDetailsRadio1]);
                if (is_array($request->customer_slot_id)) {
                    foreach ($request->customer_slot_id as $value) {
                        $CustomerService = CustomerSlot::create(['date' => $appointment_date, 'salon_id' => $salon_id, 'employee_id' => $orderDetailsRadio1, 'slot_id' => $value, 'appointment_id' => $appointment->id, 'status' => 'Pending']);
                    }
                }
                if (is_array($request->orderDetailsBox1)) {
                    foreach ($request->orderDetailsBox1 as $value) {
                        $CustomerService = CustomerService::create(['customer_id' => $user->id, 'salon_service_id' => $value, 'appointment_id' => $appointment->id]);
                    }
                }
                if (is_array($request->orderDetailsBox1)) {
                    foreach ($request->orderDetailsBox1 as $value) {
                        CustomerServiceCategory::create(['customer_service_id' => $CustomerService->id, 'service_category_id' => $CustomerService->salonService->getServiceCategory->id, 'appointment_id' => $appointment->id]);
                    }
                }
                $approved = User::findOrFail($salon_id);
                if (isset($approved->appointment_type_id) && $approved->appointment_type_id == 1) {
                    CustomerAppointment::where('id', $appointment->id)->update(['status' => "Approved"]);
                }

            } else {

            }
            $user->roles()->attach([1 => ['role_id' => 5, 'user_id' => $user->id]]);
            try {
                $name = $request->name;
                $email = $request->email;
                $password = $random_password;
                $employee = $assigned->employee->name;
                $salon = User::findOrFail($salon_id);
                $salon_picture = $salon->profile->pic;
                $salon_name = $salon->name;
                $data = array(
                    'name' => $name,
                    'email' => $email,
                    'password' => $password,
                    'employee' => $employee,
                    'salon_picture' => $salon_picture,
                    'salon_name' => $salon_name,
                    'welcome_message' => 'Welcome',
                    'information_message' => 'Account Registration Successful',
                    'detail' => env('APP_URL'),
                    'login_url' => env('APP_URL'),
                    'site_url' => env('APP_URL'),
                );
                $custom = CustomNotification::create(
                    [
                        'notifiable_id' => $salon,
                        'notifiable_type' => 'App\MOdels\User',
                        'type' => 'SalonWalkinCustomer',
                        'data' => $data,
                    ]
                );
                $result = Mail::send('website.email_templates.appointment_walkin_customer_email', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Aftab Ali')->subject('Registration Successful');;
                });
                return back()->with('flash_message', trans('messages.Customeradded'));
            } catch (\Exception $e) {
                return $e->getMessage();
                return back()->with(['message' => trans('messages.YourCreationsuccessfullybutunabletosendemail'), 'type' => 'error', 'title' => 'Fail']);;
            }
        } else {
            $appointment = CustomerAppointment::create(['customer_id' => $user->id, 'customer_appointment_date' => $appointment_date, 'customer_slot_id' => json_encode($customer_slot_id), 'status' => 'Pending', 'salon_id' => $salon_id]);
            if (is_array($request->customer_slot_id)) {
                foreach ($request->customer_slot_id as $value) {
                    $CustomerService = CustomerSlot::create(['date' => $appointment_date, 'salon_id' => $salon_id, 'employee_id' => $orderDetailsRadio1, 'slot_id' => $value, 'appointment_id' => $appointment->id, 'status' => 'Pending']);
                }
            }
            $assigned = AssignedCustomer::create(['salon_id' => $salon_id, 'appointment_id' => $appointment->id, 'customer_id' => $user->id, 'assigned_user_id' => Auth::id(), 'employee_id' => $orderDetailsRadio1]);
            if (is_array($request->orderDetailsBox1)) {
                foreach ($request->orderDetailsBox1 as $value) {
                    $CustomerService = CustomerService::create(['customer_id' => $user->id, 'salon_service_id' => $value, 'appointment_id' => $appointment->id]);
                }
            }
            if (is_array($request->orderDetailsBox1)) {
                foreach ($request->orderDetailsBox1 as $value) {
                    CustomerServiceCategory::create(['customer_service_id' => $CustomerService->id, 'service_category_id' => $CustomerService->salonService->getServiceCategory->id, 'appointment_id' => $appointment->id]);
                }
            }
            $approved = User::findOrFail($salon_id);
            if (isset($approved->appointment_type_id)) {
                if ($approved->appointment_type_id == 1) {
                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Approved']);
                } elseif ($approved->appointment_type_id == 2) {
                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Pending']);
                }
            }
            // if (is_array($request->customer_product_id)) ${
            //     foreach ($request->customer_product_id as $value) {
            //         $CustomerProduct = CustomerProduct::create(['customer_id'=>$user->id,'salon_product_id'=>$value,'appointment_id'=>$appointment->id]);
            //     }
            // }
            // if (is_array($request->product_category_id)) {
            //     foreach ($request->product_category_id as $value) {
            //         CustomerProductCategory::create(['customer_product_id'=>$CustomerProduct->id,'product_category_id'=>$value, 'appointment_id'=>$appointment->id]);
            //     }
            // }
            return back()->with('flash_message',trans('messages.Customeradded'));
        }
    }
    public function salonSearchName(Request $request)
    {
        $salonName = $request->SalonName;
        $salon = User::where('name', $salonName)->first();
        if (isset($salon) && $salon != null) {
            $salonId = $salon->id;
            return response()->json(['salonId' => $salonId]);
        } else {
            return response()->json(['message' => trans('messages.SalonNotFound')]);
        }
    }

    public function autoFillFeild(Request $request, $number_email)
    {
        if(Profile::where('phone', $number_email)->exists()){
            $user_phone = Profile::with('user')->where('phone', $number_email)->first();
            return response(['result' => 'success', 'user_phone' => $user_phone]);
        }else if(User::where('email', $number_email)->orWhere('phone', $number_email)->exists()){
            $user_email = User::with('profile')->where('email', $number_email)->orWhere('phone', $number_email)->first();
            return response(['result' => 'success', 'user_email' => $user_email]);
        }else{
            return response(['result' => 'fail']);
        }

//        $user_phone = Profile::where('phone', $number_email)->first();
//        $user_email = User::where('email', $number_email)->orWhere('phone', $number_email)->first();
//        if ($user_phone != null && $user_phone->user != null) {
//            return response(['result' => 'success', 'user_phone' => $user_phone]);
//        } elseif ($user_email != null && $user_email->profile != null) {
//            return response(['result' => 'success', 'user_email' => $user_email]);
//        } else {
//            return response(['result' => 'fail']);
//        }
    }
    public function againAppointment($id)
    {
        $user = User::with('profile')->findOrFail($id);
        if ($user != null) {
            return response(['result' => 'success', 'user' => $user]);
        } else {
            return response(['result' => 'fail', 'user' => null]);
        }
    }
    public function againAppoitmentTab(Request $request)
    {
        extract($request->all());
        $user = User::findOrFail($user_id);
        if ($user != null) {
            $appointment = CustomerAppointment::create(['customer_id' => $user->id, 'customer_appointment_date' => $appointment_date, 'customer_slot_id' => json_encode($customer_slot_id), 'status' => 'Approved', 'salon_id' => $salon_id]);
            if (is_array($request->customer_slot_id)) {
                foreach ($request->customer_slot_id as $value) {
                    $CustomerService = CustomerSlot::create(['date' => $appointment_date, 'salon_id' => $salon_id, 'employee_id' => $orderDetailsRadio1, 'slot_id' => $value, 'appointment_id' => $appointment->id, 'status' => 'Pending']);
                }
            }
            $assigned = AssignedCustomer::create(['salon_id' => $salon_id, 'appointment_id' => $appointment->id, 'customer_id' => $user->id, 'assigned_user_id' => Auth::id(), 'employee_id' => $orderDetailsRadio1]);
            if (is_array($request->orderDetailsBox1)) {
                foreach ($request->orderDetailsBox1 as $value) {
                    $CustomerService = CustomerService::create(['customer_id' => $user->id, 'salon_service_id' => $value, 'appointment_id' => $appointment->id]);
                }
            }
            if (is_array($request->orderDetailsBox1)) {
                foreach ($request->orderDetailsBox1 as $value) {
                    CustomerServiceCategory::create(['customer_service_id' => $CustomerService->id, 'service_category_id' => $CustomerService->salonService->getServiceCategory->id, 'appointment_id' => $appointment->id]);
                }
            }
            $approved = User::findOrFail($salon_id);
            if (isset($approved->appointment_type_id)) {
                if ($approved->appointment_type_id == 1) {
                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Approved']);
                } elseif ($approved->appointment_type_id == 2) {
                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Pending']);
                }
            }
            // if (is_array($request->customer_product_id)) ${
            //     foreach ($request->customer_product_id as $value) {
            //         $CustomerProduct = CustomerProduct::create(['customer_id'=>$user->id,'salon_product_id'=>$value,'appointment_id'=>$appointment->id]);
            //     }
            // }
            // if (is_array($request->product_category_id)) {
            //     foreach ($request->product_category_id as $value) {
            //         CustomerProductCategory::create(['customer_product_id'=>$CustomerProduct->id,'product_category_id'=>$value, 'appointment_id'=>$appointment->id]);
            //     }
            // }
            return back()->with('flash_message',trans('messages.Customeradded'));
        }
    }

    public function autocompletePhoneNumber(Request $request)
    {
        $query = $request->input('query');
        $appointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)->pluck('customer_id');
        $users = Profile::whereIn('user_id', $appointments)->where('phone', 'LIKE', $query . '%')->pluck('phone');
        if (sizeof($users) == 0) {
            $users = User::whereHas('roles', function ($query) {
                $query->where('name', 'customer');
            })->orWhere('salon_id',Auth::user()->salon_id)->whereIn('id', $appointments)->where('phone', 'LIKE', $query . '%')->pluck('phone');
        }
        return response()->json($users);
    }
    public function salonSubscription(Request $request, $id = null)
    {
        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })
        ->where('salon_id', Auth::user()->salon_id)
        ->orWhere('id', Auth::user()->salon_id)
        ->orderBy('id', 'ASC')
        ->get();
        $targetBranchId = $request->branch_id ?? $id;
        if ($targetBranchId && !in_array($targetBranchId, $this->getBranchIds())) {
            return redirect()->back()->with([
                'title' => 'Error',
                'message' => 'Please Try Again',
                'type' => 'error'
            ]);
        }
        if ($targetBranchId) {
            $salonSubscriptions = Usersubscription::where('user_id', $targetBranchId)
                ->orderBy('captured_at', 'DESC')
                ->get();
            $latestsalonSubscriptions = $salonSubscriptions->first();
        } else {
            $salonSubscriptions = Usersubscription::whereIn('user_id', $this->getBranchIds())
                ->orderBy('captured_at', 'DESC')
                ->get();
            $latestsalonSubscriptions = $salonSubscriptions->first();
        }
        return view('dashboard.businessDashboard.salon_subscription', compact(
            'salonSubscriptions',
            'latestsalonSubscriptions',
            'allBranches'
        ));
    }
    public function salonPremiumAddonsSubscribe($id = null)
    {
        if ($id == null){
            $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
            $branchIds = $allBranches->pluck('id');
            $premiumAddonSalonCashiers = PremiumAddonSalonCashier::whereIn('primary_salon_id',$branchIds)->get();
            $premiumAddonSalonEmployees = PremiumAddonSalonEmployee::whereIn('primary_salon_id',$branchIds)->get();
            return view('dashboard.businessDashboard.premium_addon_subscribers', compact('premiumAddonSalonCashiers','premiumAddonSalonEmployees'));
        }else{
            $premiumAddonSalonCashiers = PremiumAddonSalonCashier::where('primary_salon_id',$id)->get();
            $premiumAddonSalonEmployees = PremiumAddonSalonEmployee::where('primary_salon_id',$id)->get();
            return view('dashboard.businessDashboard.premium_addon_subscribers', compact('premiumAddonSalonCashiers','premiumAddonSalonEmployees'));
        }
    }
    public function salonInner($id)
    {
        $salon = UserSubscription::findOrFail($id);
        $categories = ServiceCategory::whereIn('id', $salon->user->allEmployeeServiceCategoryIds)->orderBy('id', 'DESC')->get();
        $appointments = CustomerAppointment::where('salon_id', $salon->user_id)->get();
        $pending = $appointments->where('status', 'Pending');
        $cancel = $appointments->where('status', 'Cancel');
        $approved = $appointments->where('status', 'Approved');
        $complete = $appointments->where('status', 'Complete');
        return view('dashboard.businessDashboard.salon_inner', compact('salon', 'categories', 'appointments', 'pending', 'cancel', 'approved', 'complete'));
    }
    // public function ReSubscriptionPayment(){
    //     $users = UserSubscription::get();
    //     foreach ($users as $key => $value) {
    //       if($value->subscription_plan_id=='1'){
    //         if(Carbon::parse(Carbon::now())->format("Y-m-d")>$value->created_at->addMonth()->format('d M Y') ){
    //             $price = $value->subscriptionPlan->price;
    //             try {
    //                 return '1try';
    //                 $stripe = new \Stripe\StripeClient('sk_test_51Mc8mMJD5VcozurofMWQI4Td3GZjqMkG1KAieTL9YIVE3jQ8lkjB0Y3PvCLHg202uMSLcO2E8mDw2rENJrVOFYBO00C5i88rC2');
    //                 $token = $stripe->tokens->create([
    //                     'card' => [
    //                         'number' => $value->number,
    //                         'exp_month' =>(int)$value->exp_month,
    //                         'exp_year' =>(int)$value->exp_year,
    //                         'cvc' => $value->cvv,
    //                     ],
    //                 ]);
    //                 Stripe::setApiKey('sk_test_51Mc8mMJD5VcozurofMWQI4Td3GZjqMkG1KAieTL9YIVE3jQ8lkjB0Y3PvCLHg202uMSLcO2E8mDw2rENJrVOFYBO00C5i88rC2');
    //                 $customer = Customer::create(array(
    //                     'email' => $value->strip_email,
    //                     'source' => $token['id']
    //                 ));
    //                 $stripe_charge = Charge::create(array(
    //                 'customer' => $customer->id,
    //                 'amount' =>  $price*100,
    //                 'currency' => 'usd'
    //                 ));
    //                 if (isset($stripe_charge)) {
    //                         if($price == '100'){
    //                         $sub = 'yearly';
    //                         $startdate = Carbon::parse(Carbon::now())->format("Y-m-d");
    //                         $enddate = Carbon::parse($startdate)->addYear()->format("Y-m-d");
    //                         }elseif($price == '10'){
    //                         $sub = 'monthly';
    //                         $startdate = Carbon::parse(Carbon::now())->format("Y-m-d");
    //                         $enddate = Carbon::parse($startdate)->addMonths()->format("Y-m-d");
    //                         }
    //                     Profile::where('user_id',$value->user_id)->update(['subscription'=>$sub,'startdate'=>$startdate,'enddate'=>$enddate]);
    //                     PaymentDetail::create(['user_id'=>$value->user_id,'amount'=>$price,'receipt_url'=>$stripe_charge->receipt_url]);
    //                     AgentZipcode::where('agent_id',$value->user_id)->update(['sub'=>'p']);
    //                 }else{

    //                 }
    //                 }catch(\Exception $e){
    //                       echo  $e->getMessage().'---'.$value->user->name.'----'.$value->user->id;
    //                       Profile::where('user_id',$value->user->id)->update(['subscription'=>'basic','startdate'=>'0','enddate'=>'0']);
    //                       AgentZipcode::where('agent_id',$value->user->id)->update(['sub'=>'n']);
    //                       $area =  AgentZipcode::where('agent_id',$value->user->id)->get();
    //                       foreach ($area as $key => $value) {
    //                          if($key!=0){
    //                           AgentZipcode::where('id',$value->id)->forceDelete();
    //                          }
    //                       }
    //                       // try {
    //                       //     $user = User::where('id',$value->user->id)->first();
    //                       //      $data = [
    //                       //          'no_reply'      => 'mailto:<EMAIL>',
    //                       //          'email'         => $user->email,
    //                       //          'name'          => $user->name,
    //                       //          'subject'       => 'Sorry to see you leave Referral-Agent.com'
    //                       //       ];
    //                       //       $result = Mail::send('website.email.leave_unsb',['data' => $data], function ($message) use ($data) {
    //                       //          $message->from($data['no_reply'])->to($data['email'])->subject($data['subject']);
    //                       //      });

    //                       //   } catch (\Exception $e) {
    //                       //      // return $e->getMessage();
    //                       //   }
    //                   // die();
    //                 }//end try catch.
    //           }
    //       }elseif($value->subscription_plan_id=='2'){
    //         if(Carbon::parse(Carbon::now())->format("Y-m-d")>$value->created_at->addMonth()->format('d M Y') ){
    //             $price = $value->subscriptionPlan->price;
    //             try {
    //                 return '2try';
    //                 $stripe = new \Stripe\StripeClient('sk_test_51Mc8mMJD5VcozurofMWQI4Td3GZjqMkG1KAieTL9YIVE3jQ8lkjB0Y3PvCLHg202uMSLcO2E8mDw2rENJrVOFYBO00C5i88rC2');
    //                 $token = $stripe->tokens->create([
    //                     'card' => [
    //                         'number' => $value->number,
    //                         'exp_month' =>(int)$value->exp_month,
    //                         'exp_year' =>(int)$value->exp_year,
    //                         'cvc' => $value->cvv,
    //                     ],
    //                 ]);
    //                 Stripe::setApiKey('sk_test_51Mc8mMJD5VcozurofMWQI4Td3GZjqMkG1KAieTL9YIVE3jQ8lkjB0Y3PvCLHg202uMSLcO2E8mDw2rENJrVOFYBO00C5i88rC2');
    //                 $customer = Customer::create(array(
    //                     'email' => $value->strip_email,
    //                     'source' => $token['id']
    //                 ));
    //                 $stripe_charge = Charge::create(array(
    //                 'customer' => $customer->id,
    //                 'amount' =>  $price*100,
    //                 'currency' => 'usd'
    //                 ));
    //                 if (isset($stripe_charge)) {
    //                         if($price == '100'){
    //                         $sub = 'yearly';
    //                         $startdate = Carbon::parse(Carbon::now())->format("Y-m-d");
    //                         $enddate = Carbon::parse($startdate)->addYear()->format("Y-m-d");
    //                         }elseif($price == '10'){
    //                         $sub = 'monthly';
    //                         $startdate = Carbon::parse(Carbon::now())->format("Y-m-d");
    //                         $enddate = Carbon::parse($startdate)->addMonths()->format("Y-m-d");
    //                         }
    //                     Profile::where('user_id',$value->user_id)->update(['subscription'=>$sub,'startdate'=>$startdate,'enddate'=>$enddate]);
    //                     PaymentDetail::create(['user_id'=>$value->user_id,'amount'=>$price,'receipt_url'=>$stripe_charge->receipt_url]);
    //                     AgentZipcode::where('agent_id',$value->user_id)->update(['sub'=>'p']);
    //                 }else{

    //                 }
    //                 }catch(\Exception $e){
    //                       echo  $e->getMessage().'---'.$value->user->name.'----'.$value->user->id;
    //                       Profile::where('user_id',$value->user->id)->update(['subscription'=>'basic','startdate'=>'0','enddate'=>'0']);
    //                       AgentZipcode::where('agent_id',$value->user->id)->update(['sub'=>'n']);
    //                       $area =  AgentZipcode::where('agent_id',$value->user->id)->get();
    //                       foreach ($area as $key => $value) {
    //                          if($key!=0){
    //                           AgentZipcode::where('id',$value->id)->forceDelete();
    //                          }
    //                       }
    //                       // try {
    //                       //     $user = User::where('id',$value->user->id)->first();
    //                       //      $data = [
    //                       //          'no_reply'      => 'mailto:<EMAIL>',
    //                       //          'email'         => $user->email,
    //                       //          'name'          => $user->name,
    //                       //          'subject'       => 'Sorry to see you leave Referral-Agent.com'
    //                       //       ];
    //                       //       $result = Mail::send('website.email.leave_unsb',['data' => $data], function ($message) use ($data) {
    //                       //          $message->from($data['no_reply'])->to($data['email'])->subject($data['subject']);
    //                       //      });

    //                       //   } catch (\Exception $e) {
    //                       //      // return $e->getMessage();
    //                       //   }
    //                   // die();
    //                 }//end try catch.
    //           }
    //       }elseif($value->subscription_plan_id=='3'){
    //         return 'is not avalible';
    //       }elseif($value->subscription_plan_id=='4'){
    //         return 'is not avalible';
    //       }elseif($value->subscription_plan_id=='5'){
    //         return 'is not avalible';
    //       }elseif($value->subscription_plan_id=='6'){
    //         return 'is not avalible';
    //       }
    //     }
    //     return true;
    // }
    public function viewCustomerDetail(Request $request)
    {
        $customer = CustomerAppointment::where('customer_id', $request->id)->latest()->first();
        $services = SalonService::whereIn('id', $customer->customerServiceIds)->get();
        if (isset($customer) && $customer->customerProductIds != null) {
            $products = Product::whereIn('id', $customer->customerProductIds)->get();
        } else {
            $products = [];
        }
        return view('website.ajax.view_customer_detail', compact('customer', 'services', 'products'));
    }

    public function viewAppointmentDetail(Request $request)
    {
        $appointment = CustomerAppointment::findOrFail($request->id);
        $services = SalonService::whereIn('id', $appointment->customerServiceIds)->get();
        if (isset($appointment) && $appointment->customerProductIds != null) {
            $purchaseOrderIds = PurchaseOrder::where('appointment_id',$appointment->id)->pluck('id');
            $products = StockOut::whereIn('purchase_order_id', $purchaseOrderIds)->get();
        } else {
            $products = [];
        }
        return view('website.ajax.view_appointment_detail', compact('appointment', 'services', 'products'));
    }
    public function ownerProfileSetting(Request $request)
    {
        extract($request->all());
        $user = User::where('id', Auth::id())->first();
        if ($request->file('pic_file')) {
            $file = $request->file('pic_file');
            $extension = $file->extension() ?: 'png';
            $destinationPath = public_path() . '/storage/uploads/users/';
            $safeName = str_random(10) . '.' . $extension;
            $file->move($destinationPath, $safeName);
            $profile_picture = $safeName;
        } else {
            $profile_picture = $user->profile->owner_pic;
        }//end if else.
        if ($request->hasFile('trade_certification')) {
            $tradeCertification = Storage::disk('website')->put('trade_certification', $request->trade_certification);
        } else {
            $tradeCertification = $user->profile->trade_certification;
        }
        if ($request->hasFile('vat_certification')) {
            $vatCertification = Storage::disk('website')->put('vat_certification', $request->vat_certification);
        } else {
            $vatCertification = $user->profile->vat_certification;
        }
        $requestData = ['owner_first_name' => $owner_first_name, 'owner_last_name' => $owner_last_name, 'owner_email' => $owner_email, 'owner_address' => $owner_address, 'owner_state' => $owner_state, 'owner_city' => $owner_city, 'owner_latitude' => $owner_latitude, 'owner_longitude' => $owner_longitude, 'owner_country' => $owner_country, 'owner_phone' => $owner_phone, 'owner_facebook' => $owner_facebook, 'owner_instagram' => $owner_instagram, 'owner_twitter' => $owner_twitter, 'owner_whatsapp' => $owner_whatsapp, 'owner_description' => $owner_description, 'owner_pic' => $profile_picture, 'vat_certification' => $vatCertification, 'trade_certification' => $tradeCertification, 'vat_certification_expiry_date' => $vat_certification_expiry_date, 'trade_certification_expiry_date' => $trade_certification_expiry_date];
        Profile::where('user_id', $salon_id)->update($requestData);
        if ($request->register_status == "Pending") {
            User::where('id', Auth::id())->update(['register_status' => "Pending"]);
            Auth::logout();
            return redirect(url('/'))->with(['message' => trans('messages.YourAccountIsDeactive'), 'type' => 'success', 'title' => 'Success']);
        } else {
            return back();
        }
    }

    public function getSalonListingFilter(Request $request)
    {
//        return $request->all();
//        $serviceCategories = ServiceCategory::whereIn('id',$request->checkedValues)->pluck('id');
//        return $services = SalonService::whereIn('category_id', $serviceCategories)->get();
        $services = SalonService::whereIn('category_id', $request->checkedValues)->where('price', '<=', $request->inputValue)->pluck('salon_id');
        $filterSalons = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_setting_updated', 1)->where('register_status', "Accepted")->whereIn('id', $services)->get();
        if (isset($filterSalons) && $filterSalons != null) {
            Session::put('salon_listing_filter', $filterSalons);
        }
    }

    public function viewClients(Request $request)
    {
        $EmployeeAppointmentIds = AssignedCustomer::where('employee_id', $request->id)->pluck('appointment_id');
        $customers = CustomerAppointment::where('status', 'Complete')->whereIn('id', $EmployeeAppointmentIds)->get();
        return view('website.ajax.view_employee_clients', compact('customers'));
    }

    public function editAd($id)
    {
        $ad = Ad::findOrFail($id);
        return view('website.ajax.edit_ad', compact('ad'));
    }

    public function viewSalonCategory(Request $request)
    {
        $salon = User::findOrFail($request->salon_id);
        $category = EmployeeServiceCategory::whereIn('service_category_id', $salon->allEmployeeServiceCategoryIds)->pluck('service_category_id');
        $avalibleCategories = ServiceCategory::whereIn('id', $category)->get();
        return view('website.ajax.view_salon_category_ajax', compact('avalibleCategories'));
    }

    public function getSalonOffDates(Request $request)
    {
        $salon = User::findOrFail($request->salonId);
        $userSubscription = UserSubscription::where('user_id', $salon->id)->orderBy('id', 'DESC')->first();
        if ($salon->salonOffDates != null) {
            $offDates = $salon->salonOffDates;
        } else {
            $offDates = "";
        }
        $responseData = [
            'userSubscription' => $userSubscription,
            'offDates' => $offDates
        ];
        return response()->json($responseData);
    }

    public function updateSupportStatus($id = null, $status = null)
    {
        if (Support::where('id', $id)->update(['status' => $status])) {
            try {
                $support = Support::find($id);
                $support_id  = $support->support_id;
                $description = $support->description;
                $salonName  = $support->salon->name;
                $salonEmail = $support->salon->email;
                $salonPicture = $support->salon->profile->pic;
                $status = ucwords($status);
                $data = array(
                    'shopName' => $salonName,
                    'shopEmail' => $salonEmail,
                    'ticketNumber' => $support_id,
                    'description' => $description,
                    'shopPicture' => $salonPicture,
                    'status' => $status,
                    'welcome_message' => 'Welcome',
                    'information_message' => ' تحديث على طلب الدعم الخاص بك '. $support_id,
                    'detail' => env('APP_URL'),
                    'login_url' => env('APP_URL'),
                    'site_url' => env('APP_URL'),
                    'type' => 'SalonSupportStatus',
                );
                $custom = CustomNotification::create(
                    [
                        'notifiable_id' => $support->salon->id,
                        'notifiable_type' => 'App\User',
                        'type' => 'SalonSupportStatus',
                        'data' => $data,
                    ]
                );
                Mail::send('website.email_templates.support_status_email', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['shopEmail'], $data['shopName'])->bcc('<EMAIL>', 'Dev')->subject('تحديث على طلب الدعم الخاص بك رقم ' . $data['ticketNumber'] . '.');
                });
                return redirect()->back()->with(['message' => trans('messages.Statusupdatedsuccessfully'), 'type' => 'success', 'title' => 'Success']);
            } catch (\Exception $e) {
                return redirect()->back()->with(['message' => trans('messages.Statusupdatedsuccessfully') . ' ' . trans('messages.butunabletosendemailtopatient') . ' ' . $e->getMessage(), 'type' => 'error', 'title' => 'Fail']);
            }
        } else {
            return redirect()->back()->with(['title' => 'Fail', 'message' => 'Status Not Update, try again', 'type' => 'error']);
        }
        return redirect()->back()->with(['title' => 'Done', 'message' => trans('messages.UpdatedStatusInformation'), 'type' => 'success']);
    }

    public function customerAppoitmentReview($id = null)
    {
        try {
            $appointmentId = CustomerAppointment::where('id', $id)->first();
            $salon = User::where('id', $appointmentId->salon_id)->where('salon_setting_updated', 1)->first();
            Session::forget('salon_listing_filter');
            $salonServices = SalonService::where('salon_id', $appointmentId->salon_id)->get();
            $salonProduct = Product::where('salon_id', $appointmentId->salon_id)->where('product_type_id', 1)->get();
            $employees = User::whereHas(
                'roles', function ($q) {
                $q->where('name', 'employee');
            }
            )->where('salon_id', $appointmentId->salon_id)->with('getEmployeeService.getSalonServices')->orderBy('id', 'DESC')->get();
            $categories = ServiceCategory::where('salon_id', $appointmentId->salon_id)->orderBy('id', 'DESC')->get();
            $PageContent = Page::where('slug', 'our_salons_section')->first();
            $feedbackQuestion = Feedback::where('salon_id', $appointmentId->salon_id)->get();
            if(isset($appointmentId->appointmentFeedback) && $appointmentId->appointmentFeedback != null){
                $customerFeedbacks = CustomerFeedback::where('appointment_id', $appointmentId->id)->orderBy('id', 'DESC')->get();
            }else{
                $customerFeedbacks = null;
            }
            if ($salon != null) {
                return view('website.salon_detail', compact('salon', 'salonServices', 'employees', 'categories', 'salonProduct', 'PageContent', 'feedbackQuestion', 'appointmentId','customerFeedbacks'));
            } else {
                return redirect(url('/'))->with(['title' => 'Alert', 'message' => trans('messages.Unabletoprocesstryagain'), 'type' => 'error']);
            }
        } catch (\Exception $e) {
            return redirect(url('/'))->with(['title' => 'Alert', 'message' => trans('messages.Unabletoprocesstryagain'), 'type' => 'error']);
        }
    }

    public function customerAppoitmentReviewForm(Request $request)
    {
        $requestData = $request->all();
        $rating = Rating::create([
            'salon_id' => $requestData['salon_id'],
            'appointment_id' => $requestData['appointment_id'],
            'rating' => $requestData['rating'],
            'review' => $requestData['review']
        ]);
        if (isset($requestData['answer']) != null) {
            foreach ($requestData['answer'] as $questionIndex => $answer) {
                $feedbackAnswerData = [
                    'rating_id' => $rating->id,
                    'feedback_id' => $questionIndex,
                    'answer' => $answer,
                    'appointment_id' => $requestData['appointment_id']
                ];
                CustomerFeedback::create($feedbackAnswerData);
            }
        }
        return redirect()->route('salon_detail', ['id' => $rating->salon_id])->with('flash_message', trans('messages.ThanksForReview'));
    }

    public function appointmentReviewDashboard(Request $request)
    {
        $appointmentId = CustomerAppointment::where('id', $request->id)->first();
        $salon = User::where('id', $appointmentId->salon_id)->where('salon_setting_updated', 1)->first();
        $feedbackQuestion = Feedback::where('salon_id', $appointmentId->salon_id)->get();
        return view('website.ajax.appointment_review_dashboard', compact('appointmentId', 'salon', 'feedbackQuestion'));
    }
//    public function employeeExpiryDate(){
//        $live_date = date('Y-m-d');
//        $expiryNotification = EmployeeExpiryNotification::where('salon_id',Auth::id())->whereIn('one_month_expiry',[$live_date])
//            ->orWhereIn('two_week_expiry', [$live_date])
//            ->orWhereIn('one_week_expiry', [$live_date])
//            ->orWhereIn('last_day_expiry', [$live_date])
//            ->pluck('employee_id')->toArray();
//    }
//    public function employeeBirthDate(){
//        $live_date = date('Y-m-d');
//        $birthdaynotification = EmployeeExpiryNotification::where('salon_id',Auth::id())->whereIn('date_of_birth',[$live_date])
//            ->pluck('employee_id')->toArray();
//    }
    public function viewRemoveOneExpiry($id)
    {
        return EmployeeExpiryNotification::where('employee_id', $id)->update(['one_month_expiry_status' => 1]);
    }

    public function viewRemoveTwoExpiry($id)
    {
        return EmployeeExpiryNotification::where('employee_id', $id)->update(['two_week_expiry_status' => 1]);
    }

    public function viewRemoveThreeExpiry($id)
    {
        return EmployeeExpiryNotification::where('employee_id', $id)->update(['one_week_expiry_status' => 1]);
    }

    public function viewRemoveFourExpiry($id)
    {
        return EmployeeExpiryNotification::where('employee_id', $id)->update(['last_day_expiry_status' => 1]);
    }

    public function viewRemoveDateOfBirth($id)
    {
        $employee = EmployeeExpiryNotification::where('employee_id', $id)->first();
        $live_date = Carbon::now();
        $dob_date = Carbon::parse($employee->date_of_birth);
        $current_year = $live_date->year;
        $next_date_of_birth = Carbon::create($current_year, $dob_date->month, $dob_date->day);
        if ($next_date_of_birth < $live_date) {
            $next_date_of_birth->addYear();
        }
        $employee_next_date_of_birth = $next_date_of_birth->format('Y-m-d');
        $requestData = (['date_of_birth', $employee_next_date_of_birth, 'date_of_birth_status', 1]);
        EmployeeExpiryNotification::where('id', $employee->id)->update($requestData);
    }

    public function offDateDelete(Request $request)
    {
        $offDate = OffDate::where('salon_id', $request->salonId)->where('date', $request->date)->first();
        if ($offDate) {
            $offDate->forceDelete();
            return response()->json(['message' => 'Off date deleted successfully']);
        } else {
            return response()->json(['message' => 'Off date not found'], 404);
        }
    }

    public function editBlog($id)
    {
        $blog = Blog::findOrFail($id);
        return view('website.ajax.edit_blog', compact('blog'));
    }

    public function editDiscount($id)
    {
        $discount = Discount::findOrFail($id);
        return view('website.ajax.edit_discount', compact('discount'));
    }

    public function useageDiscount($id)
    {
        $discount = Discount::findOrFail($id);
        $customerDiscounts = CustomerAppointment::whereIn('discount_id', [$id])->get();
        return view('website.ajax.useage_discount', compact('discount', 'customerDiscounts'));
    }

    public function blogStatus(Request $request)
    {
        if ($request->val == 1) {
            Blog::where('id', $request->id)->update(['status' => 1]);
        } elseif ($request->val == 0) {
            Blog::where('id', $request->id)->update(['status' => 0]);
        }
        return response(['result' => 'success', 'msg' => "Status changed"]);
    }

    public function discountStatus(Request $request)
    {
        if ($request->val == 1) {
            Discount::where('id', $request->id)->update(['status' => 1]);
        } elseif ($request->val == 0) {
            Discount::where('id', $request->id)->update(['status' => 0]);
        }
        return response(['result' => 'success', 'msg' => "Status changed"]);
    }

    public function registerStatus(Request $request)
    {
        if ($request->val == "Accepted") {
            User::where('id', $request->id)->update(['register_status' => "Accepted"]);
        } elseif ($request->val == "Rejected") {
            User::where('id', $request->id)->update(['register_status' => "Rejected"]);
        }
        $user = User::findOrFail($request->id);
        $shopName = $user->name;
        $shopEmail = $user->email;
        $status = $user->register_status;
        $admin = User::findOrFail(2);
        $supportEmail = $admin->email ?? null;
        $supportPhone = $admin->profile->phone ?? null;
        if ($status == "Rejected") {
            $message = 'LIINK Admin Has ' . $status . ' your Account Request!';
        } else if ($status == "Accepted") {
            $message = 'LIINK Admin Has ' . $status . ' your account request, Now you can access your account';
        } else {
            $message = 'LIINK Admin Has ' . $status . ' your Account Request!';
        }
        $data = array(
            'shopName' => $shopName,
            'shopEmail' => $shopEmail,
            'supportEmail' => $supportEmail,
            'supportPhone' => $supportPhone,
            'status' => $status,
            'welcome_message' => 'Welcome',
            'information_message' => $message,
            'detail' => env('APP_URL'),
            'login_url' => env('APP_URL'),
            'site_url' => env('APP_URL'),
        );
        $custom = CustomNotification::create(
            [
                'notifiable_id' => $admin->id,
                'notifiable_type' => 'App\User',
                'type' => 'AccountStatus',
                'data' => $data
            ]
        );
        Mail::send('website.email_templates.approved_account_welcome_email', ['data' => $data], function ($message) use ($data) {
            $message->to($data['shopEmail'], $data['shopName'])->bcc('<EMAIL>', 'Dev')->subject('تحديث هام بخصوص حسابك في LIINK');;
        });
        return response(['result' => 'success', 'msg' => "Status changed"]);
    }
    public function discountCodeChecked(Request $request)
    {
        $live_date = Carbon::now();
        $formatted_date = $live_date->format('Y-m-d');
        $discounts = Discount::where('discount_number', $request->code)
            ->where('status', 1)
            ->where('salon_id', $request->id)
            ->whereRaw('? BETWEEN validity_date_to AND validity_date_from', [$formatted_date])
            ->first();
        if ($discounts != null) {
            $qty = $discounts->quantity;
            $used_qty = $discounts->quantity_count;
            if ($qty > $used_qty) {
                if ($discounts->price != null) {
                    return response()->json([
                        'status' => 'true',
                        'price' => 'SR ' . $discounts->price
                    ]);
                } else {
                    return response()->json([
                        'status' => 'true',
                        'percentage' => $discounts->percentage . ' %'
                    ]);
                }
            } else {
                return response()->json([
                    'status' => 'false',
                    'discount' => 0
                ]);
            }
        } else {
            return response()->json([
                'status' => 'false',
                'discount' => 0
            ]);
        }
    }

    public function discountCodeCheckedWalkin(Request $request)
    {
        $live_date = Carbon::now();
        $formatted_date = $live_date->format('Y-m-d');
        $discounts = Discount::where('discount_number', $request->code)
            ->where('status', 1)
            ->where('salon_id', $request->id)
            ->whereRaw('? BETWEEN validity_date_to AND validity_date_from', [$formatted_date])
            ->first();
        if ($discounts != null) {
            $qty = $discounts->quantity;
            $used_qty = $discounts->quantity_count;
            if ($qty > $used_qty) {
                if ($discounts->price != null) {
                    return response()->json([
                        'status' => 'true',
                        'price' => 'SR ' . $discounts->price
                    ]);
                } else {
                    return response()->json([
                        'status' => 'true',
                        'percentage' => $discounts->percentage . ' %'
                    ]);
                }
            } else {
                return response()->json([
                    'status' => 'false',
                    'discount' => 0
                ]);
            }
        } else {
            return response()->json([
                'status' => 'false',
                'discount' => 0
            ]);
        }
    }

    public function feedbackAnswers($id)
    {
        $feedback = Feedback::where('id', $id)->first();
        $customerFeedBack = CustomerFeedback::where('feedback_id', $feedback->id)->orderBy('id', 'DESC')->get();
        return view('website.ajax.feedback_answers_ajax', compact('customerFeedBack'));
    }

    public function customerFeedback()
    {
        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
        $branchIds = $allBranches->pluck('id');
        $feedback = Feedback::whereIn('salon_id', $branchIds)->orderBy('id', 'DESC')->get();
        $feedbackIds = $feedback->pluck('id');
        $customerAppointmentIds = CustomerFeedback::whereIn('feedback_id', $feedbackIds)->orderBy('id', 'DESC')->pluck('appointment_id')->unique()->toArray();
        $customerFeedBack = CustomerAppointment::whereIn('id', $customerAppointmentIds)->orderBy('id','DESC')->get();
        return view('dashboard.businessDashboard.customer_feedback', compact('customerFeedBack', 'allBranches'));
    }

    public function viewAppointmentFeedback(Request $request)
    {
        $feedback = Feedback::where('salon_id', Auth::id())->orderBy('id', 'DESC')->get();
        $customerFeedbacks = CustomerFeedback::where('appointment_id', $request->id)->orderBy('id', 'DESC')->get();
        return view('website.ajax.customer_feedback_ajax', compact('customerFeedbacks', 'feedback'));
    }

    public function calendarSettingSave(Request $request)
    {
        $offDates = $request->offdates;
        $user = User::findOrFail($request->id);
        if ($user->salonOffDate != null) {
            if ($offDates != null) {
                $index = 0;
                foreach ($user->salonOffDates as $offDateUpdate) {
                    $index++;
                    OffDate::where('id', $offDateUpdate->id)->update([
                        'salon_id' => $user->id,
                        'date' => $offDateUpdate->date,
                        'date_id' => $index,
                    ]);
                }
                $senondIndex = $user->salonOffDatesIds;
                foreach ($offDates as $key => $offDateData) {
                    $senondIndex++;
                    OffDate::create([
                        'salon_id' => $user->id,
                        'date' => $offDateData,
                        'date_id' => $senondIndex,
                    ]);
                }
            }
            return 'true';
        } else {
            if ($offDates != null) {
                $index = 0;
                foreach ($offDates as $key => $offDateData) {
                    $index++;
                    OffDate::create([
                        'salon_id' => $user->id,
                        'date' => $offDateData,
                        'date_id' => $index,
                    ]);
                }
            }
            return 'true';
        }
        return 'false';
    }

    public function signupProcessTrail(Request $request)
    {
        extract($request->all());
        $subscriptionPlan = SubscriptionPlan::find($subscription_plan_id);
        if ($subscriptionPlan->price != null) {
            $amount = $subscriptionPlan->price + $subscriptionPlan->tax;
        } else {
            $amount = "Free Trail";
        }
        $full_name = $request->name;
        $full_name_no_space = str_replace(' ', '', $full_name);
        $name_parts = explode(' ', $full_name);
        $first_name = $name_parts[0];
        if (count($name_parts) > 1) {
            array_shift($name_parts);
            $last_name = implode(' ', $name_parts);
        } else {
            $last_name = "Beauty Center";
        }
//        $first_name = array_shift($name_parts);
//        $last_name = implode(' ', $name_parts);
        $description = $subscriptionPlan->subscriptionType->name . " Subscription of `" . $first_name . " " . $last_name . "`
            Including tax.
                Billing Address:$billing_address,
                Country:$country,
                City:$city,
                State:$state,
                Zip:$zip_code.
        ";
        $company = User::whereHas(
            'roles', function ($q) {
            $q->where('name', 'user');
        }
        )->first();
        $invoice_number = rand('11111111', '99999999');
        $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
        $current_date = $date->format('Y-m-d H:i:s');
        $due_date = $date->modify('+7 days')->format('Y-m-d');
        try {
            if ($request->hasFile('vat_certification')) {
                $vatCertification = Storage::disk('website')->put('vat_certification', $request->vat_certification);
            } else {
                $vatCertification = (NULL);
            }
            if ($request->hasFile('trade_certification')) {
                $tradeCertification = Storage::disk('website')->put('trade_certification', $request->trade_certification);
            } else {
                $tradeCertification = (NULL);
            }
            $phoneDefault = str_replace(' ', '', $request->phone);
            $user = User::create(['name' => $first_name . ' ' . $last_name, 'first_name' => $first_name, 'last_name' => $last_name,
                'email' => $email, 'password' => bcrypt($password), 'salon_type_id' => $salon_type_id, 'appointment_type_id' => 2, 'register_status' => "Accepted",
                'phone' => $phoneDefault, 'show_password' => $password]);
            $link = url('salon_detail', ['id' => $user->id]) . '/' . $full_name_no_space;
            Profile::create(
                ['user_id' => $user->id, 'phone' => $request->phone, 'pic' => 'no_image.png',
                'address' => $billing_address, 'latitude' => $latitude, 'link' => $link, 'longitude' => $longitude,
                'city' => $city, 'state' => $state, 'postal' => $zip_code, 'country' => $country, 'vat_number' => $vat_number,
                'vat_certification' => $vatCertification, 'vat_certification_expiry_date' => $vat_certification_expiry_date,
                'trade_certification' => $tradeCertification, 'trade_certification_expiry_date' => $trade_certification_expiry_date
                ]
            );
            $userSubscription = UserSubscription::create([
                'user_id'              => $user->id,
                'subscription_plan_id' => $subscription_plan_id,
                'amount_captured'      => null,
                'captured_status'      => "Free Trail",
                'captured_at'          => $current_date,
                'currency'             => null,
                'invoice_id'           => null,
                'charge_id'            => null,
                'description'          => $description,
                'customer_id'          => null,
                'product_id'           => null,
                'price_id'             => null,
            ]);
            FatoraInvoice::create(['user_subscription_id' => $userSubscription->id, 'company_name' => $company->name, 'company_address' => $company->profile->address,
                'company_vat_number' => $company->profile->vat_number, 'commercial_registration_number' => 38833738, 'customer_name' => $first_name . ' ' . $last_name,
                'customer_address' => $billing_address, 'customer_vat_number' => $vat_number, 'total_amount' => $amount, 'unit_price' => "Free", 'vat_amount' => 1 * 15,
                'quantity' => 1, 'description' => $subscriptionPlan->description, 'invoice_number' => $invoice_number, 'current_date' => $current_date, 'due_date' => $due_date]);
            $user->roles()->attach([1 => ['role_id' => 3, 'user_id' => $user->id]]);
            $UpgradeSubscriptionPlan = SubscriptionPlan::find(2);

            $admin = User::findOrFail(2);
            $data = array(
                'user_id'             => $user->id,
                'name'                => $first_name . ' ' . $last_name,
                'description'         => $description,
                'vat_certification' => $vatCertification,
                'vat_certification_expiry_date' => $vat_certification_expiry_date,
                'trade_certification' => $tradeCertification,
                'trade_certification_expiry_date' => $trade_certification_expiry_date,
                'email'               => $email,
                'captured_at'         => $current_date,
                'amount'              => $amount,
                'free_package'        => 'Free Package',
                'package_name'        => $UpgradeSubscriptionPlan->name,
                'welcome_message'     => 'Welcome',
                'information_message' => 'Welcome to LIINK! Your Free Trial Awaits',
                'detail'              => env('APP_URL'),
                'login_url'           => env('APP_URL'),
                'description'         => $UpgradeSubscriptionPlan->descriptionDetails,
                'site_url'            => env('APP_URL'),
                'support_phone'       => $admin->phone??'',
                'support_email'       => $admin->email,
                'trial_end_date'      => $due_date,
            );
            $notifyData = [
                'name'                => $first_name . ' ' . $last_name,
                'captured_at'         => $current_date,
                'amount'              => $amount,
                'package_name'        => $UpgradeSubscriptionPlan->name,
                'vat_certification_expiry_date'   => $vat_certification_expiry_date,
                'trade_certification_expiry_date' => $trade_certification_expiry_date,
                'type'                => 'ExpiryDate',
            ];

            $custom = CustomNotification::create([
                'notifiable_id'   => $admin->id,
                'notifiable_type' => 'App\User',
                'type'            => 'AdminPackageRegistrationNotification',
                'data'            => $notifyData,
            ]);

//            \Log::info('Notification created: ' . json_encode($custom));

            Mail::send('website.email_templates.registration_welcome_free_trail_email', ['data' => $data], function ($message) use ($data) {
                $message->to($data['email'], $data['name'])
                    ->bcc('<EMAIL>', 'Dev')
                        ->subject('مرحبًا بك في LIINK! تجربتك المجانية بإنتظارك');
            });
            if (Session::has('google_registered_user')) {
                $google_registered_user = Session::forget('google_registered_user');
                $userSubscriptionSession = Session::forget('package_id_for_google_signup');
            } else {
                $google_registered_user = [];
            }
            Auth::login($user);
            $token = Str::random(60);
            $user->update(['remember_token' => $token]);
            return redirect()->route('dashboard')->with([
                'title' => 'Done',
                'message' => trans('free_trail_subscriber_message'),
                'type' => 'success'
            ]);
//            return redirect()->route('index', ['open_modal' => true]);
        } catch (\Exception $e) {
            return redirect()->back()->with(['title' => 'Oops!', 'message' => $e->getMessage(), 'type' => 'error']);;
        }//end try catch.
    }//end signupProcess
    public function updateUserSubscription(Request $request)
    {
        extract($request->all());
        $salon = User::findOrFail($request->salon_id);
        $subscriptionPlan = SubscriptionPlan::find($subscription_plan_id);
        $description = $subscriptionPlan->subscriptionType->name . " Subscription of `" . $salon->first_name . " " . $salon->last_name . "`
            Including tax.
                Billing Address:$billing_address,
                Country:$country,
                City:$city,
                State:$state,
                Zip:$zip_code.";
        $amount = $subscriptionPlan->price + $subscriptionPlan->tax;
        $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
        $current_date = $date->format('Y-m-d H:i:s');
        if ($subscriptionPlan->package_type_id == 1) {
            $due_date = $date->modify('+30 days')->format('Y-m-d');
        } else if ($subscriptionPlan->package_type_id == 2) {
            $due_date = $date->modify('+1 year')->format('Y-m-d');
        }
        $baseUrl = url('update_sales_payment');
        $cardNumber = str_replace(' ', '', $request->card_number);
        $payload = [
            'payer_country' => 'SA',
            'payer_address' => $request->billing_address,
            'order_amount' => number_format($amount, 2),
            'action' => 'SALE',
            'card_cvv2' => $request->cvc_number,
            'payer_zip' => $request->zip_code??'',
            'payer_ip' => request()->ip(),
            'order_currency' => 'SAR',
            'payer_first_name' => $salon->first_name??'',
            'card_exp_month' => explode('/', $request->expiry_date)[0],
            'payer_city' => $request->city??'',
            'card_exp_year' => explode('/', $request->expiry_date)[1],
            'payer_last_name' => $salon->last_name,
            'payer_phone' => $salon->phone,
            'order_description' => $description,
            'payer_email' => $salon->email,
            'card_number' => $cardNumber,
            'subscription_plan_id' => $subscription_plan_id,
            'salon_id' => $salon->id,
            'due_date' => $due_date,
            'card_last_name' => $card_last_name,
            'card_first_name' => $card_first_name,
            'type' => 'user_subscription_update',
        ];
        $response = Http::withHeaders([
            'Accept' => 'application/json',
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->get($baseUrl, $payload);
        return $response;

        $userSubscription = UserSubscription::where('user_id', $request->salon_id)->orderBy('id', 'DESC')->first();
        $salon = User::findOrFail($request->salon_id);
            $subscriptionPlan = SubscriptionPlan::find($subscription_plan_id);
            $amount = $subscriptionPlan->price + $subscriptionPlan->tax;
            $description = $subscriptionPlan->subscriptionType->name . " Subscription of `" . $salon->first_name . " " . $salon->last_name . "`
            Including tax.
                Billing Address:$billing_address,
                Country:$country,
                City:$city,
                State:$state,
                Zip:$zip_code.";
            $company = User::whereHas(
                'roles', function ($q) {
                $q->where('name', 'user');
            }
            )->first();
            $invoice_number = rand('11111111', '99999999');
            $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
            $current_date = $date->format('Y-m-d H:i:s');
            if ($subscriptionPlan->package_type_id == 1) {
                $due_date = $date->modify('+30 days')->format('Y-m-d');
            } else if ($subscriptionPlan->package_type_id == 2) {
                $due_date = $date->modify('+1 year')->format('Y-m-d');
            }
        if (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 1) {
            $due_date = $date->modify('+30 days')->format('Y-m-d');
            $baseUrl = url('update_sales_payment');
            $cardNumber = str_replace(' ', '', $request->card_number);
            $payload = [
                'payer_country' => 'SA',
                'payer_address' => $request->billing_address,
                'order_amount' => number_format($amount, 2),
                'action' => 'SALE',
                'card_cvv2' => $request->cvc_number,
                'payer_zip' => $request->zip_code??'',
                'payer_ip' => request()->ip(),
                'order_currency' => 'SAR',
                'payer_first_name' => $salon->first_name??'',
                'card_exp_month' => explode('/', $request->expiry_date)[0],
                'payer_city' => $request->city??'',
                'card_exp_year' => explode('/', $request->expiry_date)[1],
                'payer_last_name' => $salon->last_name,
                'payer_phone' => $salon->phone,
                'order_description' => $description,
                'payer_email' => $salon->email,
                'card_number' => $cardNumber,
                'subscription_plan_id' => $subscription_plan_id,
                'due_date' => $due_date,
                'type' => 'user_subscription_update',
            ];
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])->asForm()->get($baseUrl, $payload);
            return $response;
        }elseif (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 2) {
            $due_date = $date->modify('+1 year')->format('Y-m-d');
            $baseUrl = url('update_sales_payment');
            $cardNumber = str_replace(' ', '', $request->card_number);
            $payload = [
                'payer_country' => 'SA',
                'payer_address' => $request->billing_address,
                'order_amount' => number_format($amount, 2),
                'action'       => 'SALE',
                'card_cvv2' => $request->cvc_number,
                'payer_zip' => $request->zip_code??'',
                'payer_ip' => request()->ip(),
                'order_currency' => 'SAR',
                'payer_first_name' => $salon->first_name??'',
                'card_exp_month' => explode('/', $request->expiry_date)[0],
                'payer_city' => $request->city??'',
                'card_exp_year' => explode('/', $request->expiry_date)[1],
                'payer_last_name' => $salon->last_name,
                'payer_phone' => $salon->phone,
                'order_description' => $description,
                'payer_email' => $salon->email,
                'card_number' => $cardNumber,
                'subscription_plan_id' => $subscription_plan_id,
                'due_date' => $due_date,
                'type' => 'user_subscription_update',
            ];
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])->asForm()->get($baseUrl, $payload);
            return $response;
        }

        if ($userSubscription->subscriptionPlan->package_type_id == 3) {
            try {
                if (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 1) {
//                    $stripe = new StripeClient(env('STRIPE_SECRET_KEY_TEST'));
//                    $customer = $stripe->customers->create([
//                        'name'   => $salon->name,
//                        'email'  => $salon->email,
//                        'source' => $stripe_token,
//                    ]);
//                    $product = $stripe->products->create([
//                        'name' => $subscriptionPlan->name,
//                    ]);
//                    $price = $stripe->plans->create([
//                        'amount'   => $amount * 100,
//                        'currency' => 'sar',
//                        'interval' => 'month',
//                        'product'  => $product->id,
//                    ]);
//                    $subscription = $stripe->subscriptions->create([
//                        'customer' => $customer->id,
//                        'items' => [
//                            ['price' => $price->id],
//                        ],
//                    ]);
                } elseif (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 2) {
//                    $stripe = new StripeClient(env('STRIPE_SECRET_KEY_TEST'));
//                    $customer = $stripe->customers->create([
//                        'name'   => $salon->name,
//                        'email'  => $salon->email,
//                        'source' => $stripe_token,
//                    ]);
//                    $product = $stripe->products->create([
//                        'name' => $subscriptionPlan->name,
//                    ]);
//                    $price = $stripe->plans->create([
//                        'amount' => $amount * 100,
//                        'currency' => 'sar',
//                        'interval' => 'year',
//                        'product' => $product->id,
//                    ]);
//                    $subscription = $stripe->subscriptions->create([
//                        'customer' => $customer->id,
//                        'items' => [
//                            ['price' =>                            $price->id],
//                        ],
//                    ]);
                }
                if ($subscription->status === 'active') {
                    $userSubscription = UserSubscription::create([
                        'user_id'              => $request->salon_id,
                        'subscription_plan_id' => $subscription_plan_id,
                        'amount_captured'      => $subscription->plan->amount,
                        'captured_status'      => $subscription->status,
                        'captured_at'          => $subscription->created ? Carbon::createFromTimestamp($subscription->created) : null,
                        'currency'             => $subscription->currency,
                        'invoice_id'           => $subscription->latest_invoice,
                        'charge_id'            => $subscription->id,
                        'description'          => $description,
                        'customer_id'          => $customer->id,
                        'product_id'           => $product->id,
                        'price_id'             => $price->id,
                    ]);
                    $stripe = new StripeClient(env('STRIPE_SECRET_KEY_TEST'));
                    $invoice = $stripe->invoices->retrieve($subscription->latest_invoice);
                    $invoiceUrl = $invoice->hosted_invoice_url;
                    $userSubscription->invoice_url = $invoiceUrl;
                    $userSubscription->save();
                    FatoraInvoice::create(
                        ['user_subscription_id' => $userSubscription->id, 'company_name' => $company->name,
                        'company_address' => $company->profile->address, 'company_vat_number' => $company->profile->vat_number, 'commercial_registration_number' => 38833738,
                        'customer_name' => $salon->first_name . ' ' . $salon->last_name, 'customer_address' => $billing_address, 'customer_vat_number' => $salon->vat_number,
                        'total_amount' => $amount, 'unit_price' => $subscriptionPlan->price, 'vat_amount' => 1 * 15, 'quantity' => 1, 'description' => $subscriptionPlan->description,
                        'invoice_number' => $invoice_number, 'current_date' => $current_date, 'due_date' => $due_date
                        ]
                    );
                    $admin = User::findOrFail(2);
                    $data = array(
                        'name' => ($salon->first_name ?? '') . ' ' . ($salon->last_name ?? ''),
                        'package_name'        => $subscriptionPlan->name,
                        'email'               => $salon->email,
                        'amount'              => $amount,
                        'welcome_message'     => 'Welcome',
                        'information_message' => 'Welcome to LIINK! Your Premium Journey Begins',
                        'detail'              => env('APP_URL'),
                        'login_url'           => env('APP_URL_LOGIN'),
                        'description'         => $description,
                        'receipt_url'         => $invoiceUrl,
                        'site_url'            => env('APP_URL'),
                        'support_phone'       => $admin->profile->phone??'',
                        'support_email'       => $admin->email,
                    );
                    $custom = CustomNotification::create(
                        [
                            'notifiable_id' => $admin->id,
                            'notifiable_type' => 'App\Models\User',
                            'type' => 'user_update_subscription',
                            'data' => $data,

                        ]
                    );
                    Mail::send('website.email_templates.registration_welcome_email', ['data' => $data], function ($message) use ($data) {
                        $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Usman Dev')->subject('Update Subscription Successful');;
                    });
                    return redirect(route('dashboard'))->with(['title' => 'Done', 'message' => trans('messages.Payment successful, account created'), 'type' => 'success']);;
//                return redirect()->route('index', ['open_modal' => true]);
                } else {
                    Auth::logout();
                    return redirect(route('/'))->with(['title' => 'Fail', 'message' => trans('messages.Payment Declined, try again'), 'type' => 'error']);;
                }
            } catch (\Exception $e) {
                return $e->getMessage();
                return redirect()->back()->with(['title' => 'Oops!', 'message' => trans('messages.Something went wrong, try again'), 'type' => 'error']);;
            }//end try catch.
        } else {
            $subscriptionPlan = SubscriptionPlan::find($subscription_plan_id);
            $amount = $subscriptionPlan->price + $subscriptionPlan->tax;
            $description = $subscriptionPlan->subscriptionType->name . " Subscription of `" . $salon->first_name . " " . $salon->last_name . "`
            Including tax.
                Billing Address:$billing_address,
                Country:$country,
                City:$city,
                State:$state,
                Zip:$zip_code.";
            $company = User::whereHas(
                'roles', function ($q) {
                $q->where('name', 'user');
            }
            )->first();
            $invoice_number = rand('11111111', '99999999');
            $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
            $current_date = $date->format('Y-m-d H:i:s');
            try {
                if ($subscriptionPlan->package_type_id == 1) {
                    $due_date = $date->modify('+30 days')->format('Y-m-d');
                } else if ($subscriptionPlan->package_type_id == 2) {
                    $due_date = $date->modify('+1 year')->format('Y-m-d');
                }
                if (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 1) {
                    $stripe = new StripeClient(env('STRIPE_SECRET_KEY_TEST'));
                    $existingCustomer = $stripe->customers->retrieve($userSubscription->customer_id);
                    $updateCustomer = $stripe->customers->update($existingCustomer->id, [
                        'source' => $stripe_token,
                    ]);
                    $existingSubscription = $stripe->subscriptions->retrieve($userSubscription->charge_id);
                    if ($existingSubscription->status === 'canceled') {
                        $product = $stripe->products->create([
                            'name' => $subscriptionPlan->name,
                        ]);
                        $price = $stripe->plans->create([
                            'amount'   => $amount * 100,
                            'currency' => 'sar',
                            'interval' => 'month',
                            'product'  => $product->id,
                        ]);
                        $subscription = $stripe->subscriptions->create([
                            'customer' => $userSubscription->customer_id,
                            'items' => [
                                ['price' => $price->id],
                            ],
                        ]);
                    } else {
                        $cancelledSubscription = $stripe->subscriptions->update($existingSubscription->id, [
                            'cancel_at_period_end' => true,
                        ]);
                        $product = $stripe->products->create([
                            'name' => $subscriptionPlan->name,
                        ]);
                        $price = $stripe->plans->create([
                            'amount' => $amount * 100,
                            'currency' => 'sar',
                            'interval' => 'month',
                            'product' => $product->id,
                        ]);
                        $subscription = $stripe->subscriptions->create([
                            'customer' => $userSubscription->customer_id,
                            'items' => [
                                ['price' => $price->id],
                            ],
                        ]);
                    }
                } else if (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 2) {
                    $stripe = new StripeClient(env('STRIPE_SECRET_KEY_TEST'));
                    $existingCustomer = $stripe->customers->retrieve($userSubscription->customer_id);
                    $updateCustomer = $stripe->customers->update($existingCustomer->id, [
                        'source' => $stripe_token,
                    ]);
                    $existingSubscription = $stripe->subscriptions->retrieve($userSubscription->charge_id);
                    if ($existingSubscription->status === 'canceled') {
                        $product = $stripe->products->create([
                            'name' => $subscriptionPlan->name,
                        ]);
                        $price = $stripe->plans->create([
                            'amount'   => $amount * 100,
                            'currency' => 'sar',
                            'interval' => 'year',
                            'product'  => $product->id,
                        ]);
                        $subscription = $stripe->subscriptions->create([
                            'customer' => $userSubscription->customer_id,
                            'items' => [
                                ['price' => $price->id],
                            ],
                        ]);
                    } else {
                        $cancelledSubscription = $stripe->subscriptions->update($existingSubscription->id, [
                            'cancel_at_period_end' => true,
                        ]);
                        $product = $stripe->products->create([
                            'name' => $subscriptionPlan->name,
                        ]);
                        $price = $stripe->plans->create([
                            'amount' => $amount * 100,
                            'currency' => 'sar',
                            'interval' => 'year',
                            'product' => $product->id,
                        ]);
                        $subscription = $stripe->subscriptions->create([
                            'customer' => $userSubscription->customer_id,
                            'items' => [
                                ['price' => $price->id],
                            ],
                        ]);
                    }
                }
                if ($subscription->status === 'active') {
                    $newUserSubscription = UserSubscription::create([
                        'user_id'              => $request->salon_id,
                        'subscription_plan_id' => $subscription_plan_id,
                        'amount_captured'      => $subscription->plan->amount,
                        'captured_status'      => $subscription->status,
                        'captured_at'          => $subscription->created ? Carbon::createFromTimestamp($subscription->created) : null,
                        'currency'             => $subscription->currency,
                        'invoice_id'           => $subscription->latest_invoice,
                        'charge_id'            => $subscription->id,
                        'description'          => $description,
                        'customer_id'          => $userSubscription->customer_id,
                        'product_id'           => $product->id,
                        'price_id'             => $price->id,
                    ]);
                    $stripe = new StripeClient(env('STRIPE_SECRET_KEY_TEST'));
                    $invoice = $stripe->invoices->retrieve($subscription->latest_invoice);
                    $invoiceUrl = $invoice->hosted_invoice_url;
                    $newUserSubscription->invoice_url = $invoiceUrl;
                    $newUserSubscription->save();
                    FatoraInvoice::create(
                        [
                        'user_subscription_id' => $newUserSubscription->id, 'company_name' => $company->name,
                        'company_address' => $company->profile->address, 'company_vat_number' => $company->profile->vat_number,
                        'commercial_registration_number' => 38833738, 'customer_name' => $salon->first_name . ' ' . $salon->last_name, 'customer_address' => $billing_address,
                        'customer_vat_number' => $salon->vat_number, 'total_amount' => $amount, 'unit_price' => $subscriptionPlan->price, 'vat_amount' => 1 * 15, 'quantity' => 1,
                        'description' => $subscriptionPlan->description, 'invoice_number' => $invoice_number, 'current_date' => $current_date, 'due_date' => $due_date
                        ]
                    );
                    $admin = User::findOrFail(2);
                    $data = array(
                        'name'                => $salon->first_name . ' ' . $salon->last_name,
                        'email'               => $salon->email,
                        'amount'              => $amount,
                        'package_name'        => $subscriptionPlan->name,
                        'welcome_message'     => 'Welcome',
                        'information_message' => 'Shop Registration Successful!',
                        'detail'              => env('APP_URL'),
                        'login_url'           => env('APP_URL'),
                        'description'         => $description,
                        'receipt_url'         => $invoiceUrl,
                        'site_url'            => env('APP_URL'),
                        'support_phone'       => $admin->profile->phone??'',
                        'support_email'       => $admin->email,
                    );
                    $custom = CustomNotification::create([
                        'notifiable_id'   => $admin->id,
                        'notifiable_type' => 'App\Models\User',
                        'type'            => 'AdminPackageUpdateNotification',
                        'data'            => $data,
                    ]);
                    Mail::send('website.email_templates.registration_welcome_email', ['data' => $data], function ($message) use ($data) {
                        $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Usman Dev')->subject('LIINK Subscription Update!');;
                    });
                    return redirect(route('dashboard'))->with(['title' => 'Done', 'message' => trans('messages.Payment successful, account created'), 'type' => 'success']);;
                } else {
                    return redirect(route('dashboard'))->with(['title' => 'Fail', 'message' => trans('messages.Payment Declined, try again'), 'type' => 'error']);;
                }
            } catch (\Exception $e) {
                return $e->getMessage();
                return redirect()->back()->with(['title' => 'Oops!', 'message' => trans('messages.Something went wrong, try again'), 'type' => 'error']);;
            }//end try catch.
        }
    }
//    public function createWebhook()
//    {
//        $stripe = new StripeClient(env('STRIPE_SECRET_KEY_TEST'));
//
//        try {
//            $webhookEndpoint = $stripe->webhookEndpoints->create([
//                'enabled_events' => ['charge.succeeded', 'charge.failed'],
//                'url' => url('webhook/stripe'),
//            ]);
//            $endpointSecret = $webhookEndpoint->secret;
//            return response()->json(['secret' => $endpointSecret]);
//
//        } catch (\Exception $e) {
//            return response()->json(['error' => $e->getMessage()], 500);
//        }
//    }
//
//    public function handleWebhook(Request $request)
//    {
//        $endpointSecret = env('STRIPE_WEBHOOK_SECRET');
//
//        $payload = $request->getContent();
//        $sigHeader = $request->header('Stripe-Signature');
//        $event = null;
//
//        try {
//            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
//        } catch (\UnexpectedValueException $e) {
//            return response('Invalid payload', 400);
//        } catch (\Stripe\Exception\SignatureVerificationException $e) {
//            return response('Invalid signature', 400);
//        }
//        // Handle the event
//        switch ($event->type) {
//            case 'charge.succeeded':
//                $charge = $event->data->object;
//                // Handle successful charge
//                break;
//            case 'charge.failed':
//                $charge = $event->data->object;
//                // Handle failed charge
//                break;
//            default:
//                return response('Event type not handled', 400);
//        }
//
//        return response('Webhook received', 200);
//       }

    public function changedRecurringSubscriptionCard(Request $request, $id=null){
        $userSubscription = UserSubscription::where('user_id', $id)->orderBy('id', 'DESC')->first();
        $stripe = new StripeClient(env('STRIPE_SECRET_KEY_TEST'));
        $existingCustomer = $stripe->customers->retrieve($userSubscription->customer_id);
        $updateCustomer = $stripe->customers->update($existingCustomer->id, [
            'source' => $request->stripe_token,
        ]);
        return redirect()->back()->with(['title' => 'Done', 'message' => trans('messages.Your Recurring Stripe Card is Changed'), 'type' => 'success']);;
    }
    public function cancelRecurringSubscription($id=null){
        $userSubscription = UserSubscription::where('user_id', $id)->orderBy('id', 'DESC')->first();
        $salon = User::where('id',$id)->update(['register_status'=>'Canceled','salon_setting_updated'=>'0']);
//        $stripe = new StripeClient(env('STRIPE_SECRET_KEY_TEST'));
//        $existingSubscription = $stripe->subscriptions->retrieve($userSubscription->charge_id);
//        $cancelledSubscription = $stripe->subscriptions->update($existingSubscription->id, [
//            'cancel_at_period_end' => true,
//        ]);
        return redirect()->back()->with(['title' => 'Done', 'message' => trans('messages.Your Recurring Subscription are Cancelled!'), 'type' => 'success']);;
    }
    public function updateSubscriptionNoti(Request $request){
        $userSubscription     = UserSubscription::where('user_id',Auth::id())->orderBy('id','DESC')->first();
        $subscriptionInterval = $userSubscription->subscriptionPlan->subscriptionType->name;
        if($subscriptionInterval=="Monthly"){
            $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
            $current_date   = $date->format('Y-m-d');
            $formatted_date = $userSubscription->captured_at;
            $captured_at    = date('Y-m-d', strtotime($formatted_date));
            $fatoraInvoiceDueDate = FatoraInvoice::where('user_subscription_id',$userSubscription->id)->orderBy('id','DESC')->first()->due_date;
            if($current_date > $fatoraInvoiceDueDate){
                return response()->json('Monthly true');
            }else{
                return response()->json('Monthly false');
            }
        }elseif ($subscriptionInterval=="Yearly"){
            $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
            $current_date = $date->format('Y-m-d');
            $formatted_date = $userSubscription->captured_at;
            $captured_at = date('Y-m-d', strtotime($formatted_date));
            $fatoraInvoiceDueDate = FatoraInvoice::where('user_subscription_id',$userSubscription->id)->orderBy('id','DESC')->first()->due_date;
            if($current_date > $fatoraInvoiceDueDate){
                return response()->json('Yearly true');
            }else{
                return response()->json('Yearly false');
            }
        }elseif ($subscriptionInterval=="Free"){
            $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
            $current_date = $date->format('Y-m-d');
            $formatted_date = $userSubscription->captured_at;
            $captured_at = date('Y-m-d', strtotime($formatted_date));
            $fatoraInvoiceDueDate = FatoraInvoice::where('user_subscription_id',$userSubscription->id)->orderBy('id','DESC')->first()->due_date;
            if($current_date > $fatoraInvoiceDueDate){
                return response()->json('Free Admin true');
            }else{
                return response()->json('Free Admin false');
            }
        }elseif ($subscriptionInterval=="Free Trail"){
            $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
            $current_date = $date->format('Y-m-d');
            $formatted_date = $userSubscription->captured_at;
            $captured_at = date('Y-m-d', strtotime($formatted_date));
            $fatoraInvoiceDueDate = FatoraInvoice::where('user_subscription_id',$userSubscription->id)->orderBy('id','DESC')->first()->due_date;
            if($current_date > $fatoraInvoiceDueDate){
                return response()->json('free true');
            }else{
                return response()->json('free false');
            }
        }
    }
    public function steperCustomer($id = null, $employeeId = null, Request $request = null) {
        if($id != null && $employeeId == null){
            $salon = User::with('userSubscriptionId')->findOrFail($id);
            $employee = null;
            $userSubscription = UserSubscription::where('user_id',$salon->id)->orderBy('id','DESC')->first();
            $admin = User::FindOrFail(2);
            $vat   = $admin->profile->vat;
            if (Session::has('google_registered_user')) {
                $google_registered_user = Session::get('google_registered_user');
                $salonAppointmentSession = Session::forget('salon_id_for_google_signup');
            }else{
                $google_registered_user = [];
            }
            if (isset($request->appointment_type_id) && $request->appointment_type_id){
                $employeeIds       = EmployeeType::where('salon_id',$id)->where('salon_type_id',$request->appointment_type_id)->pluck('employee_id');
                $employeeService   = EmployeeService::whereIn('employee_id', $employeeIds)->pluck('salon_service_id')->toArray();
                $services          = SalonService::whereIn('id',$employeeService)->get();
                $salonService      = $services->pluck('category_id')->toArray();
                $categories        = ServiceCategory::whereIn('id',$salonService)->orderBy('id', 'DESC')->get();
                $productcategories = ServiceCategory::whereIn('id',$salon->allEmployeeProductCategoryIds)->orderBy('id', 'DESC')->get();
                $appointmentTypeId = $request->appointment_type_id;
            }else{
                $categories = ServiceCategory::whereIn('id',$salon->allEmployeeServiceCategoryIds)->orderBy('id', 'DESC')->get();
                $productcategories = ServiceCategory::whereIn('id',$salon->allEmployeeProductCategoryIds)->orderBy('id', 'DESC')->get();
                $services = SalonService::whereIn('id',$salon->allEmployeeServiceIds)->get();
                $appointmentTypeId = null;
            }
        }elseif($id != null && $employeeId != null){
            $salon            = User::findOrFail($id);
            $employee         = User::findOrFail($employeeId);
            $userSubscription = UserSubscription::where('user_id',$salon->id)->orderBy('id','DESC')->first();
            $admin            = User::FindOrFail(2);
            $vat              = $admin->profile->vat;
            if (Session::has('google_registered_user')) {
                $google_registered_user  = Session::get('google_registered_user');
                $salonAppointmentSession = Session::forget('salon_id_for_google_signup');
            }else{
                $google_registered_user = [];
            }
            $categories        = ServiceCategory::whereIn('id',$employee->employeeServiceCategoryIds)->orderBy('id', 'DESC')->get();
            $productcategories = ServiceCategory::whereIn('id',$salon->allEmployeeProductCategoryIds)->orderBy('id', 'DESC')->get();
            $services          = SalonService::whereIn('id',$employee->employeeServiceIds)->get();
            $appointmentTypeId = null;
        }
        return view('website.steper_appoint',compact('id','employee','services','categories',
                            'salon','google_registered_user','userSubscription','vat','productcategories','appointmentTypeId'));
    }
    public function registerCustomer(Request $request){
        extract($request->all());
        $user = User::where('email', $email)->orWhere('phone',$phone)->first();
        if ($user == null) {
            $phoneDefault = str_replace(' ', '', $request->phone);
            $password     = rand('11111111','99999999');
            $user         = User::create(['first_name'=>$first_name,'last_name'=>$last_name,'name'=>$first_name.' '.$last_name,'email'=>$email,
                                'password'=>bcrypt($password), 'customer_type_id'=>2,'phone'=>$phoneDefault,'show_password'=>$password]);
            $profile = Profile::create([
                'user_id'   => $user->id,
                'dob'       => $dob,
                'phone'     => $request->phone,
                'address'   => $address ?? null,
                'pic'       => 'no_avatar.jpg',
                'state'     => isset($state) ? $state : null,
                'city'      => isset($city) ? $city : null,
                'country'   => isset($country) ? $country : null,
                'latitude'  => isset($lat) ? $lat : null,
                'longitude' => isset($lng) ? $lng : null,
                'postal'    => isset($zip_code) ? $zip_code : null
            ]);
            $user->roles()->attach([1 => ['role_id' =>5,'user_id' => $user->id]]);
        }
        try{
            $salon = User::findOrFail($salon_id);
            $salonPicture   = $salon->profile->pic;
            $salonName      = $salon->name;
            $salonEmail     = $salon->email;
            $salonPhone     = $salon->profile->phone;
            $passwordLength = 8;
            $code = rand(100000, 999999);
            if($code){
                Session::put('customer_verification_code', $code);
                Session::put('customer_id_verified', $user->id);
            }
            $data = array(
                'name' => $first_name.' '.$last_name,
                'email' => $email,
                'password' => $password,
                'salonPicture' =>$salonPicture,
                'salonName' => $salonName,
                'salonEmail' => $salonEmail,
                'salonPhone' =>$salonPhone,
                'code' => $code,
                'welcome_message' => 'Welcome',
                'information_message' => 'Account Verification Successful',
                'detail' => env('APP_URL'),
                'login_url' => env('APP_URL'),
                'site_url' => env('APP_URL'),
            );
            $custom = CustomNotification::create(
                [
                    'notifiable_id' => $salon->id,
                    'notifiable_type' => 'App\Models\User',
                    'type' => 'SalonCustomerCode',
                    'data' => $data
                ]
            );
            Mail::send('website.email_templates.registration_customer_code_email',['data'=>$data],function($message) use($data){
                $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Dev')->subject('Registration Successful');
            });
            if (Session::has('google_registered_user')) {
                $google_registered_user = Session::forget('google_registered_user');
            }else {
                $google_registered_user = [];
            }
            return response()->json(['registration' => 'true']);
        }catch (\Exception $e){
            return response()->json(['registration' => $e->getMessage()]);
        }
    }
    public function verificationCustomer(Request $request){
        if (Session::has('customer_verification_code')) {
            $code  = Session::get('customer_verification_code');
            $verifyCode = $request->verification_code;
            if ($code == $verifyCode){
                if (Session::has('customer_id_verified')){
                    $customerId = Session::get('customer_id_verified');
                    $customer = User::where('id',$customerId)->with('profile')->first();
                    $first_name = $customer->first_name;
                    $last_name = $customer->last_name;
                    $email = $customer->email;
                    $phone = $customer->phone;
                    $address = $customer->profile->address;
                    $dob = $customer->profile->dob;
                    if ($customer) {
                        Auth::login($customer);
                        $token = Str::random(60);
                        $customer->update(['remember_token' => $token]);
                        Session::forget('customer_verification_code');
                        Session::forget('customer_id_verified');
                        return response()->json(['verification' => 'true', 'first_name' => $first_name, 'last_name' => $last_name, 'email' => $email, 'phone'=> $phone, 'address' => $address, 'dob' => $dob]);
                    }
                }
            } else {
                return response()->json(['verification' => 'false']);
            }
        }
    }
//    public function steperAppointmentWebsite(Request $request){
////        return $request->all();
//        extract($request->all());
//        $user = User::where('email', $email)->orWhere('phone',$phone)->first();
//        if(isset($user) == null){
//            $phoneDefault = str_replace(' ', '', $request->phone);
//            $password = rand('11111111','99999999');
//            $user = User::create(['first_name'=>$first_name,'last_name'=>$last_name,'name'=>$first_name.' '.$last_name,'email'=>$email,'password'=>bcrypt($password), 'customer_type_id'=>2,'phone'=>$phoneDefault,'show_password'=>$password]);
//            $profile = Profile::create([
//                'user_id' => $user->id,
//                'dob' => $dob,
//                'phone' => $request->phone,
//                'address' => $address,
//                'pic' => 'no_avatar.jpg',
//                'state' => isset($state) ? $state : null,
//                'city' => isset($city) ? $city : null,
//                'country' => isset($country) ? $country : null,
//                'latitude' => isset($lat) ? $lat : null,
//                'longitude' => isset($lng) ? $lng : null,
//                'postal' => isset($zip_code) ? $zip_code : null
//            ]);
//            $user->roles()->attach([1 => ['role_id' =>5,'user_id' => $user->id]]);
//            Auth::login($user);
//            $appointment = CustomerAppointment::create(['customer_id'=>$user->id,'customer_appointment_date'=>$customer_appointment_date,'customer_slot_id'=>json_encode($customer_slot_id),'status'=>'Pending', 'salon_id'=>$salon_id]);
//            if (is_array($request->customer_slot_id)) {
//                foreach ($request->customer_slot_id as $value) {
//                    $CustomerService = CustomerSlot::create(['date'=>$customer_appointment_date,'salon_id'=>$salon_id,'employee_id'=>$employee_id,'slot_id'=>$value, 'appointment_id'=>$appointment->id,'status'=>'Pending']);
//                }
//            }
//            if (is_array($request->customer_service_id)) {
//                foreach ($request->customer_service_id as $value) {
//                    $CustomerService = CustomerService::create(['customer_id'=>$user->id,'salon_service_id'=>$value, 'appointment_id'=>$appointment->id]);
//                }
//            }
//            if (is_array($request->category_id)) {
//                foreach ($request->category_id as $value) {
//                    CustomerServiceCategory::create(['customer_service_id'=>$CustomerService->id,'service_category_id'=>$value, 'appointment_id'=>$appointment->id]);
//                }
//            }
//            if(!empty($request->employee_id)){
//                $assigned = AssignedCustomer::create(['salon_id'=>$salon_id,'appointment_id'=>$appointment->id,'customer_id'=>$user->id,'assigned_user_id'=>Auth::id(),'employee_id'=>$employee_id]);
//            }
//            $approved = User::findOrFail($salon_id);
//            if (isset($approved->appointment_type_id)) {
//                if ($approved->appointment_type_id == 1) {
//                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Approved']);
//                } elseif ($approved->appointment_type_id == 2) {
//                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Pending']);
//                }
//            }
//            if(isset($request->discount_number) && $request->discount_number!=null){
//                $live_date = Carbon::now();
//                $formatted_date = $live_date->format('Y-m-d');
//                $discounts = Discount::where('discount_number', $request->discount_number)
//                    ->where('status', 1)
//                    ->where('salon_id', $appointment->salon_id)
//                    ->whereRaw('? BETWEEN validity_date_to AND validity_date_from', [$formatted_date])
//                    ->first();
//                if($discounts!=null){
//                    $qty        = $discounts->quantity;
//                    $used_qty   = $discounts->quantity_count;
//                    if($qty > $used_qty){
//                        if ($used_qty==null){
//                            $new_qty =  1;
//                        }else{
//                            $new_qty = $used_qty + 1;
//                        }
//                        Discount::where('id',$discounts->id)->update(['quantity_count'=>$new_qty]);
//                        CustomerAppointment::where('id',$appointment->id)->update(['discount_id'=>$discounts->id]);
//                    }
//                }
//            }
//            $productIds = $customer_product_id;
//            $products = Product::whereIn('id', $productIds)->get();
//            $quantity = count($customer_product_id);
//            $totalAmountWithVat = 0;
//            $totalAmountWithoutVat = 0;
//            $pricesPerSku = [];
//            foreach ($products as $product) {
//                if ($product->productCurrentInventory) {
//                    $skuId = $product->productCurrentInventory->sku_id;
//                    $priceWithoutVat = $product->productCurrentInventory->price;
////                    $priceWithoutVat = $product->productCurrentInventory->per_cost_price;
//                    $totalAmountWithoutVat += $priceWithoutVat;
////                    $totalAmountWithoutVat += $priceWithoutVat;
//                    $pricesPerSku[$skuId] = [
////                        'with_vat' => $priceWithVat,
//                        'without_vat' => $priceWithoutVat,
//                    ];
//                }
//            }
//            $admin=Profile::where('user_id',2)->first();
//            $totalAmountWithVat=$totalAmountWithoutVat+ (($admin->vat/100) * $totalAmountWithoutVat);
//            $purchaseOrder = PurchaseOrder::create([
//                "user_id" => Auth::user()->id ?? "",
//                "salon_id" => $salon_id ?? "",
//                "notes" => $notes ?? "",
//                "vat" => $admin->vat ?? "",
//                "total_amount_with_vat" => $totalAmountWithVat ?? 0,
//                "total_amount_without_vat" => $totalAmountWithoutVat ?? 0,
//                'date' => $appointment->customer_appointment_date ?? "",
//                'appointment_id'=> $appointment->id ?? "",
//                'status' => "automatic sales",
//                'total_quantity' => $quantity
//            ]);
//            if (!empty($productIds)) {
//                if (is_array($productIds)) {
//                    foreach ($productIds as $key => $value) {
//                        $requiredQuantity = 1;
//                        $currentDate = now();
//                        $productInventory = ProductInventory::where('product_id', $value)
//                            ->where('expiry_date', '>=', $currentDate)
//                            ->where('quantity', '>', 0)
//                            ->orderBy('expiry_date', 'asc')
//                            ->first();
//                        if ($productInventory) {
//                            $productInventory->consumed_quantity = is_numeric($productInventory->consumed_quantity) ? $productInventory->consumed_quantity : 0;
//                            if ($productInventory->quantity >= $requiredQuantity) {
//                                $productInventory->quantity -= $requiredQuantity;
//                                $productInventory->consumed_quantity += $requiredQuantity;
//                                $productInventory->save();
//                            } else {
//                                $requiredQuantity -= $productInventory->quantity;
//                                $productInventory->consumed_quantity += $productInventory->quantity;
//                                $productInventory->quantity = 0;
//                                $productInventory->save();
//                            }
//                            StockOut::create([
//                                'product_id' => $value,
//                                'quantity' => 1, // Since you are using $requiredQuantity = 1
//                                'price_per_product' => $productInventory->price,
//                                'total_price_per_product' => $productInventory->price,
//                                'cost_price_per_product' => $productInventory->per_cost_price ?? 0,
//                                'salon_id' => $salon_id ?? "",
//                                'purchase_order_id' => $purchaseOrder->id ?? "",
//                                'sku_id' => $productInventory->sku_id,
//                            ]);
//                        }
//                    }
//                }
//            }
//
//
////            $product=Product::where('id',$customer_product_id)->get();
////            $productAvailablePrice=$product->productCurrentInventory;
////
////            $purchaseOrder=PurchaseOrder::create(["user_id"=>uth::user()->id??"","salon_id"=>$salon_id??"","notes"=>$notes??"","vat"=>$vat??"","total_amount_with_vat"=>$overall_price_with_vat??"","total_amount_without_vat"=>
////                $overall_price_without_vat??"",'date'=>$date??"",'status'=>"sales",'total_quantity'=>$totalQuantity??""]);
////            if (!empty($product_id)) {
////                if (is_array($product_id)) {
////                    foreach ($product_id as $key => $value) {
////                        $requiredQuantity = $quantity[$key] ?? $quantity;
////                        $productInventories = ProductInventory::where('product_id', $value)
////                            ->where('quantity', '>', 0)
////                            ->orderBy('created_at', 'asc')
////                            ->get();
////                        foreach ($productInventories as $inventory) {
////                            $inventory->consumed_quantity = is_numeric($inventory->consumed_quantity) ? $inventory->consumed_quantity : 0;
////                            if ($requiredQuantity <= 0) {
////                                break;
////                            }
////                            if ($inventory->quantity >= $requiredQuantity) {
////                                $inventory->quantity -= $requiredQuantity;
////                                $inventory->consumed_quantity += $requiredQuantity;
////                                $inventory->save();
////                                $requiredQuantity = 0;
////                            } else {
////                                $inventory->consumed_quantity += $inventory->quantity;
////                                $requiredQuantity -= $inventory->quantity;
////                                $inventory->quantity = 0;
////                                $inventory->save();
////                            }
////                        }
////                        $total_cost_price_per_product=$per_cost_price[$key] * $quantity[$key];
////                        StockOut::create([
////                            'product_id' => $value,
////                            'quantity' => $quantity[$key] ?? $quantity,
////                            'price_per_product' => $cost_price[$key] ?? $cost_price,
////                            'total_price_per_product' => $total_price[$key] ?? $total_price,
////                            'cost_price_per_product'=>$total_cost_price_per_product ??"",
////                            'salon_id' => Auth::user()->id??"",
////                            'purchase_order_id' => $purchaseOrder->id??"",
////                            'sku_id'=>$sku_id[$key]??$sku_id,
////                        ]);
////                    }
////                }
////            }
////
//
//
//        }elseif ($user->roles[0]->name == "customer"){
//            $appointment = CustomerAppointment::create(['customer_id'=>$user->id,'customer_appointment_date'=>$customer_appointment_date,'customer_slot_id'=>json_encode($customer_slot_id),'status'=>'Pending', 'salon_id'=>$salon_id]);
//            if (is_array($request->customer_slot_id)) {
//                foreach ($request->customer_slot_id as $value) {
//                    $CustomerService = CustomerSlot::create(['date'=>$customer_appointment_date,'salon_id'=>$salon_id,'employee_id'=>$employee_id,'slot_id'=>$value, 'appointment_id'=>$appointment->id,'status'=>'Pending']);
//                }
//            }
//            if (is_array($request->customer_service_id)) {
//                foreach ($request->customer_service_id as $value) {
//                    $CustomerService = CustomerService::create(['customer_id'=>$user->id,'salon_service_id'=>$value, 'appointment_id'=>$appointment->id]);
//                }
//            }
//            if (is_array($request->category_id)) {
//                foreach ($request->category_id as $value) {
//                    CustomerServiceCategory::create(['customer_service_id'=>$CustomerService->id,'service_category_id'=>$value, 'appointment_id'=>$appointment->id]);
//                }
//            }
//            if(!empty($request->employee_id)){
//                $assigned = AssignedCustomer::create(['salon_id'=>$salon_id,'appointment_id'=>$appointment->id,'customer_id'=>$user->id,'assigned_user_id'=>Auth::id(),'employee_id'=>$employee_id]);
//            }
//            $approved = User::findOrFail($salon_id);
//            if (isset($approved->appointment_type_id)) {
//                if ($approved->appointment_type_id == 1) {
//                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Approved']);
//                } elseif ($approved->appointment_type_id == 2) {
//                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Pending']);
//                }
//            }
//            if(isset($request->discount_number) && $request->discount_number!=null){
//                $live_date = Carbon::now();
//                $formatted_date = $live_date->format('Y-m-d');
//                $discounts = Discount::where('discount_number', $request->discount_number)
//                    ->where('status', 1)
//                    ->where('salon_id', $appointment->salon_id)
//                    ->whereRaw('? BETWEEN validity_date_to AND validity_date_from', [$formatted_date])
//                    ->first();
//                if($discounts!=null){
//                    $qty        = $discounts->quantity;
//                    $used_qty   = $discounts->quantity_count;
//                    if($qty > $used_qty){
//                        if ($used_qty==null){
//                            $new_qty =  1;
//                        }else{
//                            $new_qty = $used_qty + 1;
//                        }
//                        Discount::where('id',$discounts->id)->update(['quantity_count'=>$new_qty]);
//                        CustomerAppointment::where('id',$appointment->id)->update(['discount_id'=>$discounts->id]);
//                    }
//                }
//            }
//
//
//            $productIds = $customer_product_id;
//            $products = Product::whereIn('id', $productIds)->get();
//            $quantity = count($customer_product_id);
//            $totalAmountWithVat = 0;
//            $totalAmountWithoutVat = 0;
//            $pricesPerSku = [];
//
//            foreach ($products as $product) {
//                if ($product->productCurrentInventory) {
//                    $skuId = $product->productCurrentInventory->sku_id;
//                    $priceWithoutVat = $product->productCurrentInventory->price;
////                    $priceWithoutVat = $product->productCurrentInventory->per_cost_price;
//                    $totalAmountWithoutVat += $priceWithoutVat;
////                    $totalAmountWithoutVat += $priceWithoutVat;
//                    $pricesPerSku[$skuId] = [
////                        'with_vat' => $priceWithVat,
//                        'without_vat' => $priceWithoutVat,
//                    ];
//                }
//                $customerProduct= CustomerProduct::create(['customer_id'=>$user->id??"",'salon_product_id'=>$product->id??"",'appointment_id'=> $appointment->id ?? ""]);
//                $customerProductCategory=CustomerProductCategory::create(['customer_product_id'=>$customerProduct->id??"",'product_category_id'=>$product->product_category_id??"",'appointment_id'=> $appointment->id ?? ""]);
//            }
//            $admin=Profile::where('user_id',2)->first();
//            $totalAmountWithVat=$totalAmountWithoutVat+ (($admin->vat/100) * $totalAmountWithoutVat);
//            $purchaseOrder = PurchaseOrder::create([
//                "user_id" => Auth::user()->id ?? "",
//                "salon_id" => $salon_id ?? "",
//                "notes" => $notes ?? "",
//                "vat" => $admin->vat ?? "",
//                "total_amount_with_vat" => $totalAmountWithVat ?? 0,
//                "total_amount_without_vat" => $totalAmountWithoutVat ?? 0,
//                'date' => $appointment->customer_appointment_date ?? "",
//                'appointment_id'=> $appointment->id ?? "",
//                'status' => "automatic sales",
//                'total_quantity' => $quantity
//            ]);
//            if (!empty($productIds)) {
//                if (is_array($productIds)) {
//                    foreach ($productIds as $key => $value) {
//                        $requiredQuantity = 1;
//                        $currentDate = now();
//                        $productInventory = ProductInventory::where('product_id', $value)
//                            ->where('expiry_date', '>=', $currentDate)
//                            ->where('quantity', '>', 0)
//                            ->orderBy('expiry_date', 'asc')
//                            ->first();
//                        if ($productInventory) {
//                            $productInventory->consumed_quantity = is_numeric($productInventory->consumed_quantity) ? $productInventory->consumed_quantity : 0;
//                            if ($productInventory->quantity >= $requiredQuantity) {
//                                $productInventory->quantity -= $requiredQuantity;
//                                $productInventory->consumed_quantity += $requiredQuantity;
//                                $productInventory->save();
//                            } else {
//                                $requiredQuantity -= $productInventory->quantity;
//                                $productInventory->consumed_quantity += $productInventory->quantity;
//                                $productInventory->quantity = 0;
//                                $productInventory->save();
//                            }
//                            StockOut::create([
//                                'product_id' => $value,
//                                'quantity' => 1,
//                                'price_per_product' => $productInventory->price,
//                                'total_price_per_product' => $productInventory->price,
//                                'cost_price_per_product' => $productInventory->per_cost_price ?? 0,
//                                'salon_id' => $salon_id ?? "",
//                                'purchase_order_id' => $purchaseOrder->id ?? "",
//                                'sku_id' => $productInventory->sku_id,
//                            ]);
//                        }
//                    }
//                }
//            }
//
//        }else{
//            return redirect()->route('salon_detail', ['id' => $request->salon_id])->with('flash_message', 'Only Customer Booked Appointment!');
//        }
//        if(isset($request->employee_id)){
//            try{
//                $salon = User::findOrFail($salon_id);
//                $salon_picture = $salon->profile->pic;
//                $salon_name = $salon->name;
//                $employee = $assigned->employee->name;
//                $date = date('d/m/Y', strtotime($appointment->customer_appointment_date));
////                $time = $appointment->customerSlot->start_time;
//                $data = array(
//                    'name' => $first_name.' '.$last_name ,
//                    'email' => $email,
//                    'date' => $date,
////                    'time' => $time,
//                    'employee' => $employee,
//                    'salon_picture' =>$salon_picture,
//                    'salon_name' =>$salon_name,
//                    'welcome_message' => 'Welcome',
//                    'information_message' => 'Appointment Booked!',
//                    'detail' => env('APP_URL'),
//                    'login_url' => env('APP_URL'),
//                    'site_url' => env('APP_URL'),
//                );
//                Mail::send('website.email_templates.appointment_booked_website',['data'=>$data],function($message) use($data){
//                    $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Aftab Ali')->subject('Appointment Booked Successful');;
//                });
//            }catch (\Exception $e){
//                return $e->getMessage();
//            }//end try catch.
//        }
//        if (Session::has('google_registered_user')) {
//            $google_registered_user = Session::forget('google_registered_user'); // destroy session
//        } else {
//            $google_registered_user = [];
//        }
////        return redirect()->route('salon_detail', ['id' => $request->salon_id])->with('flash_message', 'Your Appointment Booked!');
//        return redirect('dashboard')->with('flash_message', 'Your Appointment Booked!');
//    }
    public function steperAppointmentWebsite(Request $request){
//        return $request->all();
        extract($request->all());
        $user = User::where('email', $email)->orWhere('phone',$phone)->first();
        if(isset($user) == null){
            $phoneDefault = str_replace(' ', '', $request->phone);
            $password = rand('11111111','99999999');
            $user = User::create(['first_name'=>$first_name,'last_name'=>$last_name,'name'=>$first_name.' '.$last_name,'email'=>$email,'password'=>bcrypt($password),
                'customer_type_id'=>2,'phone'=>$phoneDefault,'show_password'=>$password]);
            $profile = Profile::create([
                'user_id' => $user->id,
                'dob' => $dob,
                'phone' => $request->phone,
                'address' => $address,
                'pic' => 'no_avatar.jpg',
                'state' => isset($state) ? $state : null,
                'city' => isset($city) ? $city : null,
                'country' => isset($country) ? $country : null,
                'latitude' => isset($lat) ? $lat : null,
                'longitude' => isset($lng) ? $lng : null,
                'postal' => isset($zip_code) ? $zip_code : null
            ]);
            $user->roles()->attach([1 => ['role_id' =>5,'user_id' => $user->id]]);
            Auth::login($user);
            $token = Str::random(60);
            $user->update(['remember_token' => $token]);
            $appointment = CustomerAppointment::create(['customer_id'=>$user->id,'customer_appointment_date'=>$customer_appointment_date,
                'customer_slot_id'=>json_encode($customer_slot_id),'status'=>'Pending', 'salon_id'=>$salon_id,'appointment_type_id'=>$request->appointment_type_id,
                'customer_appointment_type'=>"Online"]);
            if (is_array($request->customer_slot_id)) {
                foreach ($request->customer_slot_id as $value) {
                    $CustomerService = CustomerSlot::create(['date'=>$customer_appointment_date,'salon_id'=>$salon_id,'employee_id'=>$employee_id,
                        'slot_id'=>$value, 'appointment_id'=>$appointment->id,'status'=>'Pending']);
                }
            }
            if (is_array($request->customer_service_id)) {
                foreach ($request->customer_service_id as $value) {
                    $CustomerService = CustomerService::create(['customer_id'=>$user->id,'salon_service_id'=>$value, 'appointment_id'=>$appointment->id]);
                }
            }
            if (is_array($request->category_id)) {
                foreach ($request->category_id as $value) {
                    CustomerServiceCategory::create(['customer_service_id'=>$CustomerService->id,'service_category_id'=>$value, 'appointment_id'=>$appointment->id]);
                }
            }
            if(!empty($request->employee_id)){
                $assigned = AssignedCustomer::create(['salon_id'=>$salon_id,'appointment_id'=>$appointment->id,'customer_id'=>$user->id,
                    'assigned_user_id'=>Auth::id(),'employee_id'=>$employee_id]);
            }
            $approved = User::findOrFail($salon_id);
            if (isset($approved->appointment_type_id)) {
                if ($approved->appointment_type_id == 1) {
                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Approved']);
                } elseif ($approved->appointment_type_id == 2) {
                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Pending']);
                }
            }
            if(isset($request->discount_number) && $request->discount_number!=null){
                $live_date = Carbon::now();
                $formatted_date = $live_date->format('Y-m-d');
                $discounts = Discount::where('discount_number', $request->discount_number)
                            ->where('status', 1)
                            ->where('salon_id', $appointment->salon_id)
                            ->whereRaw('? BETWEEN validity_date_to AND validity_date_from', [$formatted_date])
                            ->first();
                if($discounts!=null){
                    $qty        = $discounts->quantity;
                    $used_qty   = $discounts->quantity_count;
                    if($qty > $used_qty){
                        if ($used_qty==null){
                            $new_qty =  1;
                        }else{
                            $new_qty = $used_qty + 1;
                        }
                        Discount::where('id',$discounts->id)->update(['quantity_count'=>$new_qty]);
                        CustomerAppointment::where('id',$appointment->id)->update(['discount_id'=>$discounts->id]);
                    }
                }
            }
        }elseif ($user->roles[0]->name == "customer"){
//            return $user->id;
            Profile::where('user_id',$user->id)->update([
                'address'   => $address,
                'pic'       => 'no_avatar.jpg',
                'state'     => isset($state) ? $state : null,
                'city'      => isset($city) ? $city : null,
                'country'   => isset($country) ? $country : null,
                'latitude'  => isset($lat) ? $lat : null,
                'longitude' => isset($lng) ? $lng : null,
                'postal'    => isset($zip_code) ? $zip_code : null,
            ]);
            $appointment = CustomerAppointment::create(['customer_id'=>$user->id,'customer_appointment_date'=>$customer_appointment_date,
                'customer_slot_id'=>json_encode($customer_slot_id),'status'=>'Pending', 'salon_id'=>$salon_id,'appointment_type_id'=>$request->appointment_type_id,
                    'customer_appointment_type'=>"Online"]);
            if (is_array($request->customer_slot_id)) {
                foreach ($request->customer_slot_id as $value) {
                    $CustomerService = CustomerSlot::create(['date'=>$customer_appointment_date,'salon_id'=>$salon_id,
                        'employee_id'=>$employee_id,'slot_id'=>$value, 'appointment_id'=>$appointment->id,'status'=>'Pending']);
                }
            }
            if (is_array($request->customer_service_id)) {
                foreach ($request->customer_service_id as $value) {
                    $CustomerService = CustomerService::create(['customer_id'=>$user->id,'salon_service_id'=>$value, 'appointment_id'=>$appointment->id]);
                }
            }
            if (is_array($request->category_id)) {
                foreach ($request->category_id as $value) {
                    CustomerServiceCategory::create(['customer_service_id'=>$CustomerService->id,'service_category_id'=>$value, 'appointment_id'=>$appointment->id]);
                }
            }
            if(!empty($request->employee_id)){
                $assigned = AssignedCustomer::create(['salon_id'=>$salon_id,'appointment_id'=>$appointment->id,'customer_id'=>$user->id,'assigned_user_id'=>Auth::id(),'employee_id'=>$employee_id]);
            }
            $approved = User::findOrFail($salon_id);
            if (isset($approved->appointment_type_id)) {
                if ($approved->appointment_type_id == 1) {
                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Approved']);
                } elseif ($approved->appointment_type_id == 2) {
                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Pending']);
                }
            }
            if(isset($request->discount_number) && $request->discount_number!=null)
            {
                $live_date = Carbon::now();
                $formatted_date = $live_date->format('Y-m-d');
                $discounts = Discount::where('discount_number', $request->discount_number)
                    ->where('status', 1)
                    ->where('salon_id', $appointment->salon_id)
                    ->whereRaw('? BETWEEN validity_date_to AND validity_date_from', [$formatted_date])
                    ->first();
                if($discounts!=null)
                {
                    $qty        = $discounts->quantity;
                    $used_qty   = $discounts->quantity_count;
                    if($qty > $used_qty){
                        if ($used_qty==null){
                            $new_qty =  1;
                        }else{
                            $new_qty = $used_qty + 1;
                        }
                        Discount::where('id',$discounts->id)->update(['quantity_count'=>$new_qty]);
                        CustomerAppointment::where('id',$appointment->id)->update(['discount_id'=>$discounts->id]);
                    }
                }
            }

        }else{
            return redirect()->route('salon_detail', ['id' => $request->salon_id])->with('flash_message', trans('messages.OnlyCustomerBookedAppointment'));
        }
        if(isset($request->employee_id)){
//            try{
//                $salon = User::findOrFail($salon_id);
//                $salon_picture = $salon->profile->pic;
//                $salon_name = $salon->name;
//                $employee = $assigned->employee->name;
//                $date = date('d/m/Y', strtotime($appointment->customer_appointment_date));
////                $time = $appointment->customerSlot->start_time;
//                $data = array(
//                    'name' => $first_name.' '.$last_name ,
//                    'email' => $email,
//                    'date' => $date,
////                    'time' => $time,
//                    'employee' => $employee,
//                    'salon_picture' =>$salon_picture,
//                    'salon_name' =>$salon_name,
//                    'welcome_message' => 'Welcome',
//                    'information_message' => 'Appointment Booked!',
//                    'detail' => env('APP_URL'),
//                    'login_url' => env('APP_URL'),
//                    'site_url' => env('APP_URL'),
//                );
//                Mail::send('website.email_templates.appointment_booked_website',['data'=>$data],function($message) use($data){
//                    $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Aftab Ali')->subject('Appointment Booked Successful');;
//                });
//            }catch (\Exception $e){
//                return $e->getMessage();
//            }
            try {
                $servicesIds = $appointment->getCustomerServices ?
                $appointment->getCustomerServices->pluck('salon_service_id')->toArray() : [];
                $services = SalonService::whereIn('id', $servicesIds)->pluck('name')->toArray();
                $data = [
                    'shopName'         => $appointment->salon->name,
                    'shopPicture'      => $appointment->salon->profile->pic,
                    'shopPhone'        => $appointment->salon->profile->phone,
                    'shopAddress'      => $appointment->salon->profile->address,
                    'shopLocationLink' => $appointment->salon->profile->location_link,
                    'date'             => $appointment->customer_appointment_date ? date('d M Y', strtotime($appointment->customer_appointment_date)) : '',
                    'time'             => ($appointment->startSlot->start_time ?? '') . ' - ' . ($appointment->endSlot->end_time ?? ''),
                    'customerName'     => $appointment->customer->name,
                    'customerEmail'    => $appointment->customer->email,
                    'services'         => implode(', ', $services),
                    'appointmentType'  => $appointment->appointment_type_id,
                ];
                $notify = [
                    'customer_id'      => $user->id,
                    'customer_name'    => $first_name . ' ' . $last_name,
                    'customer_email'   => $email,
                    'customer_phone'   => $phone,
                    'customer_address' => $address,
                    'customer_pic'     => $user->profile->pic,
                    'salon_name'       => $appointment->salon->name,
                    'employee_id'      => $assigned->employee->id,
                    'employee_name'    => $assigned->employee->name,
                    'date'             => $appointment->customer_appointment_date ? date('d M Y', strtotime($appointment->customer_appointment_date)) : '',
                    'type'             => 'SalonNotification'
                ];
                $custom = CustomNotification::create([
                    'notifiable_id'   => $salon_id,
                    'notifiable_type' => 'App\User',
                    'type'            => 'SalonNotification',
                    'data'            => $notify,
                ]);
//                \Log::info('Notification created: ' . json_encode($custom));

                Mail::send('website.email_templates.appointment_assign', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['customerEmail'])
                        ->bcc('<EMAIL>', 'Dev')
                        ->subject('Your Appointment at [' . $data['shopName'] . '] is Booked');
                });
                if (Auth::user()->hasRole('cashier')) {
                    return back();
                }
            } catch (\Exception $e) {
                return back()->with(['message' => 'Creation successful, but unable to send email.', 'type' => 'error', 'title' => 'Fail']);
            }
        }
        if (Session::has('google_registered_user')) {
            $google_registered_user = Session::forget('google_registered_user'); // destroy session
        } else {
            $google_registered_user = [];
        }
        return redirect('dashboard')->with('flash_message',trans('messages.YourAppointmentBooked'));
    }
    public function checkEmployeeServices(Request $request, $date = null, $id = null){
        $date =  Carbon::createFromFormat('m-d-Y', $date)->format('m/d/Y');
        $serviceIds = $request->input('serviceIdsSelected');
        if(isset($serviceIds) && $serviceIds!=null){
            $allAppointmentsDate = CustomerAppointment::whereIn('customer_appointment_date',[$date])
                ->whereNotIn('status',['Cancel'])
                ->where('salon_id',$id)
                ->pluck('id');
           $allAssignedAppointments = AssignedCustomer::whereIn('appointment_id',$allAppointmentsDate)->pluck('employee_id');
           $employeeLeaves = EmployeeLeave::whereIn('salon_id', [$id])
                ->whereRaw('? BETWEEN date_to AND date_from', $date)
                ->pluck('employee_id');
//         return $employee = EmployeeService::whereNotIn('employee_id',$employeeLeaves)->whereIn('salon_service_id',$serviceIds)->whereNotIn('employee_id',$allAssignedAppointments)->with('getEmployees')->get();
           $employee = EmployeeService::whereNotIn('employee_id',$employeeLeaves)->whereIn('salon_service_id',$serviceIds)->with('getEmployees')->get();
           $groupedEmployees = $employee->groupBy('employee_id');
           $result = $groupedEmployees->filter(function ($employeeGroup) use ($serviceIds) {
               $employeeServiceIds = $employeeGroup->pluck('salon_service_id')->unique();
               return $employeeServiceIds->intersect($serviceIds)->count() === count($serviceIds);
           });
           $resultArray = $result->values()->all();
           if ($resultArray != null){
               return response()->json('yes');
           }else{
               return response()->json('no');
           }
        }else{
            return response()->json('no');
        }
    }
    public function updateSubscriptionEmail(){
        $currentDate = now();
        $salons = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->get();
        $salonIds = $salons->pluck('id');
        $userSubscriptions = UserSubscription::whereIn('user_id', $salonIds)
            ->whereIn('id', function ($query) {
                $query->selectRaw('MAX(id)')
                    ->from('user_subscriptions')
                    ->groupBy('user_id');
            })
            ->orderBy('id', 'DESC')
            ->get();
        $subscriptionIds = $userSubscriptions->pluck('id');
        $fatoraInvoiceIds = FatoraInvoice::whereIn('user_subscription_id', $subscriptionIds)
            ->whereDate('due_date', $currentDate->toDateString())
            ->orderBy('id', 'DESC')
            ->pluck('user_subscription_id');
        $userSub = UserSubscription::whereIn('id', $fatoraInvoiceIds)->pluck('user_id');
        $user = User::whereIn('id',$userSub)->get();
        $admin = User::find(2);
        if (sizeof($user) != 0) {
            foreach ($user as $value) {
//                User::where('id', $value->id)->update(['register_status' => "Rejected"]);
                try {
                    $renewalDate = Carbon::parse($value->userSubscriptionIdLatest->fatoraPdf->due_date)->addDay();
                    $data = [
                        'shopID'                  => $value->id,
                        'shopName'                => $value->name,
                        'shopEmail'               => $value->email,
                        'supportEmail'            => $admin->email,
                        'supportPhone'            => $admin->profile->phone,
                        'expiryDate'              => date("d/m/y", strtotime($value->userSubscriptionIdLatest->fatoraPdf->due_date)),
                        'renewalDate'             => date("d/m/y", strtotime($renewalDate)),
                        'welcome_message'         => 'Welcome',
                        'information_message'     => 'Shop Subscription Expired!',
                        'detail'                  => env('APP_URL'),
                        'login_url'               => env('APP_URL'),
                        'update_subscription_url' => env('APP_UPDATE_SUBSCRIPTION_URL'),
                        'site_url'                => env('APP_URL'),
                    ];
//                    foreach($salonId as $salon)
//                    $custom = CustomNotification::create(
//                        [
//                            'notifiable_id' => $
//                        ]
//                    );
                    Mail::send('website.email_templates.subscription_expired_email', ['data' => $data], function ($message) use ($data) {
                        $message->to($data['shopEmail'], $data['shopName'])->cc('<EMAIL>', 'Dev')->subject('Your LIINK Subscription Has Expired');
                    });
                    echo '<pre>';
                    echo $data['shopEmail'];
                    echo '</pre>';
                } catch (\Exception $e) {
                    echo '<pre>';
                        echo $e->getMessage();
                    echo '</pre>';
                }
            }
        }
    }
    public function pastLeaves($id){
        $employeeLeave = EmployeeLeave::where('employee_id',$id)->get();
        $employee = User::findOrFail($id);
        return view('website.ajax.past_leave', compact('employeeLeave','employee'));
    }
    public function pastAppointments($id){
        $assignedAppointments = AssignedCustomer::whereIn('employee_id',[$id])->pluck('appointment_id');
        $appointments = CustomerAppointment::whereIn('id',$assignedAppointments)->where('status','Complete')->get();
        $employee = User::findOrFail($id);
        return view('website.ajax.past_appointments', compact('appointments','employee'));
    }
    // Admin Notification
    public function tradeExpireNoti(Request $request) {
        $tradeUsers = [];
        $vatUsers = [];
        if ($request->tradeIds) {
            $tradeUsers = User::whereHas(
                'profile', function($q){
                $q->where('trade_status', null);
            }
            )->whereIn('id', explode(',', $request->tradeIds))->get();
        }
        if ($request->vatIds) {
            $vatUsers = User::whereHas(
                'profile', function($q){
                $q->where('vat_status', null);
            }
            )->whereIn('id', explode(',', $request->vatIds))->get();
        }
        return view('website.ajax.admin_noti', compact('tradeUsers', 'vatUsers'));
    }
    public function tradeExpireDate() {
        $userIdTradeExpire = [];
        $userIdVatExpire = [];
        $currentDate = Carbon::now();
        $salons = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('register_status', "Accepted")->get();

        foreach ($salons as $salon) {
            $tradeCertExpiryDate = Carbon::parse($salon->profile->trade_certification_expiry_date);
            $vatCertExpiryDate = Carbon::parse($salon->profile->vat_certification_expiry_date);

            // Always include the salon ID if the certificate has expired or if it's the same day
            if ($tradeCertExpiryDate && $tradeCertExpiryDate->lte($currentDate)) {
                $userIdTradeExpire[] = $salon->profile->user_id;
            }

            // Always include the salon ID if the certificate has expired or if it's the same day
            if ($vatCertExpiryDate && $vatCertExpiryDate->lte($currentDate)) {
                $userIdVatExpire[] = $salon->profile->user_id;
            }
        }

        $response = [
            'vatIds' => $userIdVatExpire,
            'tradeIds' => $userIdTradeExpire,
        ];

        return response()->json($response);
    }
    public function viewRemoveVat($id=null){
        if ($id!=null){
            try {
                Profile::where('user_id',$id)->update(['vat_status'=>1]);
            }catch (\Exception $e){
                return $e->getMessage();
            }
        }
    }
    public function viewRemoveTrade($id=null){
        if ($id!=null){
            try {
                Profile::where('user_id',$id)->update(['trade_status'=>1]);
            }catch (\Exception $e){
                return $e->getMessage();
            }
        }
    }
    public function allBranches(){
        $branches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
        $branchIds = $branches->pluck('id');
        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
            ->orderBy('user_id', 'ASC')
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy('user_id')
            ->map(function ($subs) {
                return $subs->first()->fatoraPdf->due_date;
            });
        // return $userSubscription = UserSubscription::whereIn('user_id',$branchIds)->orderBy('id','DESC')->get();
        // date('Y-m-d', strtotime('now')) > $userSubscription->fatoraPdf->due_date
        return view ('dashboard.businessDashboard.all_branches', compact('branches','userSubscriptionsDueDate'));
    }
    public function addNewBranchSubscription(){
        $monthlySubscriptionPlans = SubscriptionPlan::where('status',1)->whereHas('subscriptionType', function($q){
            $q->where('name', 'monthly');
        })->get();
        $yearlySubscriptionPlans = SubscriptionPlan::where('status',1)->whereHas('subscriptionType', function($q){
            $q->where('name', 'yearly');
        })->get();
        return view('dashboard.businessDashboard.new_branch_subscription', compact('monthlySubscriptionPlans','yearlySubscriptionPlans'));
    }
    public function addNewBranchSubscriptionPayment($id){
        $subscriptionPlan = SubscriptionPlan::findOrFail($id);
        $salonTypes = SalonType::where('status', 1)->get();
        return view('dashboard.businessDashboard.new_branch_subscription_payment', compact('subscriptionPlan','salonTypes'));
    }//end subscriptionsPayment function.
    public function addNewBranchRegisteration(Request $request){
        extract($request->all());
        $subscriptionPlan = SubscriptionPlan::findOrFail($subscription_plan_id);
        $amount = $subscriptionPlan->price + $subscriptionPlan->tax;
        $full_name = $request->name;
        $description = $subscriptionPlan->subscriptionType->name . " Subscription of `" . $full_name . "`
        Including tax.
            Billing Address:$billing_address,
            Country:$country,
            City:$city,
            State:$state,
            Zip:$zip_code.
        ";
        $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
        $current_date = $date->format('Y-m-d H:i:s');
        $response = [];
        try {
            if (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 1) {
                $due_date = $date->modify('+30 days')->format('Y-m-d');
                $baseUrl = url('new_branch_sales_payment');
                $cardNumber = str_replace(' ', '', $request->card_number);
                $payload = [
                    'payer_country' => 'SA',
                    'payer_address' => $request->billing_address,
                    'order_amount' => number_format($amount, 2),
                    'action' => 'SALE',
                    'card_cvv2' => $request->cvc_number,
                    'payer_zip' => $request->zip_code,
                    'payer_ip' => request()->ip(),
                    'order_currency' => 'SAR',
                    'payer_first_name' => $request->card_first_name,
                    'card_exp_month' => explode('/', $request->expiry_date)[0],
                    'payer_city' => $request->city,
                    'card_exp_year' => explode('/', $request->expiry_date)[1],
                    'payer_last_name' => $request->card_last_name,
                    'payer_phone' => $request->phone,
                    'order_description' => $description,
                    'payer_email' => $request->email,
                    'card_number' => $cardNumber,
                    'subscription_plan_id' => $subscription_plan_id,
                    'name' => $name,
                    'card_first_name' => $card_first_name,
                    'card_last_name' => $card_last_name,
                    'billing_address' => $billing_address,
                    'country' => $country,
                    'city' => $city,
                    'state' => $state,
                    'zip_code' => $zip_code,
                    'phone' => $phone,
                    'email' => $email,
                    'salon_type_id' => $salon_type_id,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'due_date' => $due_date,
                    'user_id' => Auth::id(),
                    'type' => 'user_subscription_new_branch',
                ];
                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ])->asForm()->get($baseUrl, $payload);
                return $response;
            } elseif (isset($subscriptionPlan->package_type_id) && $subscriptionPlan->package_type_id == 2) {
                $due_date = $date->modify('+1 year')->format('Y-m-d');
                $baseUrl = url('new_branch_sales_payment');
                $cardNumber = str_replace(' ', '', $request->card_number);
                $payload = [
                    'payer_country' => 'SA',
                    'payer_address' => $request->billing_address,
                    'order_amount' => number_format($amount, 2),
                    'action' => 'SALE',
                    'card_cvv2' => $request->cvc_number,
                    'payer_zip' => $request->zip_code,
                    'payer_ip' => request()->ip(),
                    'order_currency' => 'SAR',
                    'payer_first_name' => $request->card_first_name,
                    'card_exp_month' => explode('/', $request->expiry_date)[0],
                    'payer_city' => $request->city,
                    'card_exp_year' => explode('/', $request->expiry_date)[1],
                    'payer_last_name' => $request->card_last_name,
                    'payer_phone' => $request->phone,
                    'order_description' => $description,
                    'payer_email' => $request->email,
                    'card_number' => $cardNumber,
                    'subscription_plan_id' => $subscription_plan_id,
                    'name' => $name,
                    'card_first_name' => $card_first_name,
                    'card_last_name' => $card_last_name,
                    'billing_address' => $billing_address,
                    'country' => $country,
                    'city' => $city,
                    'state' => $state,
                    'zip_code' => $zip_code,
                    'phone' => $phone,
                    'email' => $email,
                    'salon_type_id' => $salon_type_id,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'due_date' => $due_date,
                    'user_id' => Auth::id(),
                    'type' => 'user_subscription_new_branch',
                ];
                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ])->asForm()->get($baseUrl, $payload);
                return $response;
            }
            if ($subscription->status === 'active') {
                $phone = $request->phone;
                $branch = User::create(['name' => $first_name . ' ' . $last_name, 'first_name' => $first_name, 'last_name' => $last_name, 'email' => $email, 'password' => bcrypt($password), 'salon_type_id' => $salon_type_id, 'appointment_type_id' => 2, 'register_status' => "Accepted",'phone'=>$phone,'show_password'=>$password,'salon_id'=>$user->id]);
                $link = url('salon_detail', ['id' => $branch->id]) . '/' . $full_name_no_space;
                Profile::create(['user_id' => $branch->id, 'phone' => $request->phone, 'pic' => 'no_image.png', 'address' => $billing_address, 'latitude' => $latitude, 'link' => $link, 'longitude' => $longitude, 'city' => $city, 'state' => $state, 'postal' => $zip_code, 'country' => $country, 'vat_number' => $user->profile->vat_number, 'owner_first_name' => $card_first_name, 'owner_last_name' => $card_last_name,'owner_email' => $user->profile->owner_email, 'owner_address' =>$user->profile->owner_address , 'owner_state'=>$user->profile->owner_state, 'owner_city' =>$user->profile->owner_city, 'owner_latitude' =>$user->profile->owner_latitude, 'owner_longitude'=>$user->profile->owner_longitude, 'owner_country' =>$user->profile->owner_country, 'owner_phone' =>$user->profile->owner_phone, 'owner_facebook' =>$user->profile->owner_facebook, 'owner_instagram'=>$user->profile->owner_instagram, 'owner_twitter' =>$user->profile->owner_twitter, 'owner_whatsapp' =>$user->profile->owner_whatsapp, 'owner_description' =>$user->profile->owner_description, 'owner_pic'=>$user->profile->owner_pic, 'vat_certification' => $user->profile->vat_certification, 'vat_certification_expiry_date' => $user->profile->vat_certification_expiry_date, 'trade_certification' => $user->profile->trade_certification, 'trade_certification_expiry_date' => $user->profile->trade_certification_expiry_date]);
                $userSubscription = UserSubscription::create([
                    'user_id'              => $branch->id,
                    'subscription_plan_id' => $subscription_plan_id,
                    'amount_captured'      => $subscription->plan->amount,
                    'captured_status' => $subscription->status,
                    'captured_at' => $subscription->created ? Carbon::createFromTimestamp($subscription->created) : null,
                    'currency' => $subscription->currency,
                    'invoice_id' => $subscription->latest_invoice,
                    'charge_id' => $subscription->id,
                    'description' => $description,
                    'customer_id' => $customer->id,
                    'product_id' => $product->id,
                    'price_id' => $price->id,
                ]);
                $stripe = new StripeClient(env('STRIPE_SECRET_KEY_TEST'));
                $invoice = $stripe->invoices->retrieve($subscription->latest_invoice);
                $invoiceUrl = $invoice->hosted_invoice_url;
                $userSubscription->invoice_url = $invoiceUrl;
                $userSubscription->save();
                FatoraInvoice::create(['user_subscription_id' => $userSubscription->id, 'company_name' => $company->name, 'company_address' => $company->profile->address, 'company_vat_number' => $company->profile->vat_number, 'commercial_registration_number' => 38833738, 'customer_name' => $card_first_name . ' ' . $card_last_name, 'customer_address' => $billing_address, 'customer_vat_number' => $branch->profile->vat_number, 'total_amount' => $amount, 'unit_price' => $subscriptionPlan->price, 'vat_amount' => 1 * 15, 'quantity' => 1, 'description' => $subscriptionPlan->description, 'invoice_number' => $invoice_number, 'current_date' => $current_date, 'due_date' => $due_date]);
                $user->roles()->attach([1 => ['role_id' => 3, 'user_id' => $branch->id]]);
                $admin = User::findOrFail(2);
                $data = array(
                    'name' => $card_first_name . ' ' . $card_last_name,
                    'email' => $email,
                    'amount' => $amount,
                    'package_name' => $subscriptionPlan->name,
                    'welcome_message' => 'Welcome',
                    'information_message' => 'New Branch Registration Successful!',
                    'detail' => env('APP_URL'),
                    'login_url' => env('APP_URL'),
                    'description' => $description,
                    'receipt_url' => $userSubscription->invoice_url,
                    'site_url' => env('APP_URL'),
                    'support_phone' => $admin->profile->phone??'',
                    'support_email' => $admin->email,
                );
                Mail::send('website.email_templates.registration_welcome_email', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Usman Dev')->subject('New Branch Added Successful');;
                });
                return redirect(url('all_branches'))->with(['title' => 'Done', 'message' => trans('messages.Payment successful, account created'), 'type' => 'success']);;
            } else {
                return redirect()->back()->with(['title' => 'Fail', 'message' => trans('messages.Payment Declined, try again'), 'type' => 'error']);;
            }//end if else.
        } catch (\Exception $e) {
            return $e->getMessage();
            return redirect()->back()->with(['title' => 'Oops!', 'message' => trans('messages.Something went wrong, try again'), 'type' => 'error']);;
        }//end try catch.
        return $charge;
    }
    public function branchEmployee(Request $request){
        $employee = User::whereHas(
            'roles', function($q){
            $q->where('name', 'employee');
        }
        )->where('salon_id',$request->id)->orderBy('id','DESC')->get();
        return $employee;
    }
    public function branchCategory(Request $request){
          $salonCategory = SalonService::where('salon_id',$request->id)->pluck('category_id');
         $categories = ServiceCategory::whereIn('id',$salonCategory)->orderBy('id', 'DESC')->get();
        return $categories;
    }
    public function branchCategoryAjax(Request $request){
        $salon = User::findOrFail($request->id);
        $categories = ServiceCategory::whereIn('id',$salon->allEmployeeServiceCategoryIds)->get();
        return $categories;
    }
    public function branchType(Request $request){
        $salon = User::findOrFail($request->id);
        return view('website.ajax.branch_type',compact('salon'));
    }
    public function branchCashierCount(Request $request){
        $salon = User::where('id',$request->id)->first();
        $cashierCount = $salon->allCashierCount;
        $premiumAddonSalonCashier = PremiumAddonSalonCashier::where('primary_salon_id',Auth::user()->salon_id)->orderBy('id','DESC')->first();
        if (isset($premiumAddonSalonCashier) && $premiumAddonSalonCashier !=null){
            if ($premiumAddonSalonCashier->premiumAddonHistoryRemaingCashier == 0){
                return response()->json(['success' => false], 200);
            }else{
                $premiumAddonPackage = PremiumAddonPackage::where('id', $premiumAddonSalonCashier->premium_addon_id)->first();
                $admin = User::where('id',2)->first();
                $html = view('website.ajax.purchased_addons', compact('premiumAddonPackage','admin','premiumAddonSalonCashier'))->render();
                return response()->json(['html'=>$html , 'success' => 'Addon Purchased'], 200);
            }
        }elseif($cashierCount >= 1){
            return response()->json(['success' => false], 200);
        }else{
            return response()->json(['success' => true], 200);
        }
    }
    public function branchEmployeeCount(Request $request){
        $salon = User::where('id',$request->id)->first();
        $employeeCount = $salon->allEmployeeCount;
        $premiumAddonSalonEmployee = PremiumAddonSalonEmployee::where('primary_salon_id',Auth::user()->salon_id)->orderBy('id','DESC')->first();
        if($employeeCount < 4){
            return response()->json(['success' => true], 200);
        }else{
            if (isset($premiumAddonSalonEmployee) && $premiumAddonSalonEmployee !=null){
                if ($premiumAddonSalonEmployee->premiumAddonHistoryRemaingEmployee == 0){
                    return response()->json(['success' => false], 200);
                }else{
                    $premiumAddonPackage = PremiumAddonPackage::where('id', $premiumAddonSalonEmployee->premium_addon_id)->first();
                    $admin = User::where('id',2)->first();
                    $html = view('website.ajax.purchased_addons_employee', compact('premiumAddonPackage','admin','premiumAddonSalonEmployee'))->render();
                    return response()->json(['html'=>$html , 'success' => 'Addon Purchased'], 200);
                }
            }elseif($employeeCount >= 4){
                return response()->json(['success' => false], 200);
            }else{
                return response()->json(['success' => true], 200);
            }
        }
    }
    public function changedPrimaryBranch(Request $request){
        $primaryBranch = User::where('id',$request->id)->first();
        $currentprimaryBranch = $primaryBranch->salon_id;
        $secondaryBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->whereIn('salon_id',[$currentprimaryBranch])->whereNotIn('id',[$request->id])->Orwhere('id',[$currentprimaryBranch])->get();
        foreach($secondaryBranches as $key => $branch){
            User::where('id',$branch->id)->update([
                'salon_id' => $request->id,
//                'register_status' => 'Hold',
            ]);
        }
        $primaryBranch->update(['salon_id'=> null]);
        Auth::logout();
        Auth::login($primaryBranch);
        $token = Str::random(60);
        $primaryBranch->update(['remember_token' => $token]);
        $admin = User::find(2);
        $data = array(
            'ShopName' => $primaryBranch->name,
            'ShopEmail' => $primaryBranch->email,
            'ShopPassword' => $primaryBranch->show_password,
            'welcome_message' => 'Welcome',
            'information_message' => 'Primary Branch Changed Successful!',
            'detail' => env('APP_URL'),
            'login_url' => env('APP_URL'),
            'description' => 'Access Your Panel Primary Branch!',
            'site_url' => env('APP_URL'),
            'supportEmail' => $admin->email,
            'supportPhone' => $admin->phone,
        );
        Mail::send('website.email_templates.primary_branch_changed', ['data' => $data], function ($message) use ($data) {
            $message->to($data['ShopEmail'], $data['ShopName'])->cc('<EMAIL>', 'Aftab Ali')->subject('Primary Branch Successful');;
        });
        return redirect('all_branches');
    }
    //CRONJOBS
    //    7 DAYS Show On Front-End
    public function hideOnWebsite() {
        return true;
        $liveDate = now();
        $userSubscriptions = UserSubscription::whereIn('id', function ($query) {
            $query->select(DB::raw('MAX(id)'))
                ->from('user_subscriptions')
                ->whereNull('deleted_at')
                ->groupBy('user_id');
        })->pluck('id');
        if ($userSubscriptions->isNotEmpty()) {
            $fatoraInvoices = FatoraInvoice::whereIn('user_subscription_id', $userSubscriptions)
                ->whereDate('due_date', '=', $liveDate->subDays(7)->format('Y-m-d'))
                ->pluck('user_subscription_id');
            if ($fatoraInvoices->isNotEmpty()) {
                $userIds = UserSubscription::whereIn('id', $fatoraInvoices)->pluck('user_id');
                if ($userIds->isNotEmpty()) {
                    User::whereIn('id', $userIds)->update(['register_status' => "Rejected"]);
                }
            }
        }
    }
    public function recurringExpense() {
        $liveDate = now()->format('Y-m-d');
        $expenses = Expense::where("is_recurring", "=", "1")->get();
        $expenseCreated = false;
        foreach ($expenses as $expense) {
            if ($expense->time_cycle == "weekly") {
                $startDate = Carbon::parse($expense->start_date);
                $endDate = Carbon::parse($expense->end_date);
                $newStartDate = $startDate->addWeek()->format('Y-m-d');
                if ($liveDate == $newStartDate && $liveDate < $endDate) {
                    Expense::create([
                        'name' => $expense->name ?? '', 'date' => $expense->date ?? '', 'expense_category_id' => $expense->expense_category_id ?? '',
                        'amount' => $expense->amount ?? '', 'vendor' => $expense->vendor ?? '', 'tax' => $expense->tax ?? '', 'total_amount' => $expense->total_amount ?? '',
                        'invoice_no' => rand('1111', '8888') ?? '', 'is_recurring' => $expense->is_recurring ?? '', 'time_cycle' => $expense->time_cycle ?? '',
                        'start_date' => $newStartDate ?? '', 'end_date' => $expense->end_date ?? '', 'description' => $expense->description ?? '', 'attachments' => $expense->attachments ?? '', 'saloon_id' => $expense->saloon_id ?? ''
                    ]);
                    $expenseCreated = true;
                }
            }
            if ($expense->time_cycle == "monthly") {
                $startDate = Carbon::parse($expense->start_date);
                $endDate = Carbon::parse($expense->end_date);
                $newStartDate = $startDate->addMonth()->format('Y-m-d');
                if ($liveDate == $newStartDate && $liveDate < $endDate) {
                    Expense::create([
                        'name' => $expense->name ?? '', 'date' => $expense->date ?? '', 'expense_category_id' => $expense->expense_category_id ?? '', 'amount' => $expense->amount ?? '',
                        'vendor' => $expense->vendor ?? '', 'tax' => $expense->tax ?? '', 'total_amount' => $expense->total_amount ?? '', 'invoice_no' => rand('1111', '8888') ?? '',
                        'is_recurring' => $expense->is_recurring ?? '', 'time_cycle' => $expense->time_cycle ?? '', 'start_date' => $newStartDate ?? '', 'end_date' => $expense->end_date ?? '', 'description' => $expense->description ?? '',
                        'attachments' => $expense->attachments ?? '', 'saloon_id' => $expense->saloon_id ?? ''
                    ]);
                    $expenseCreated = true;
                }
            }
        }
        return response()->json(['success' => $expenseCreated]);
    }
    public function recurringRevenue(){
        $liveDate = now()->format('Y-m-d');
//        return $liveDate;
        $revenues = Revenue::where("is_recurring","=","1")->get();
        $revenueCreated = false;
        foreach ($revenues as $revenue){
            if($revenue->time_cycle == "weekly"){
                $startDate = Carbon::parse($revenue->start_date);
                $endDate = Carbon::parse($revenue->end_date);
                $newStartDate = $startDate->addWeek()->format('Y-m-d');
                if($liveDate == $newStartDate && $liveDate < $endDate ){
                   Revenue::Create(['name'=>$revenue->name??'','date'=>$revenue->date??'','client_name'=>$revenue->client_name??'','amount'=>$revenue->amount??'',
                       'vendor'=>$revenue->vendor??"",'tax'=>$revenue->tax??'','total_amount'=>$revenue->total_amount??'','client_type'=>$revenue->client_type??'',
                       'attachments'=>$revenue->attachments??'','is_recurring'=>$revenue->is_recurring??'','time_cycle'=>$revenue->time_cycle??'',
                       'start_date'=>$revenue->start_date??'','end_date'=>$revenue->end_date??'','description'=>$revenue->description??'','saloon_id'=>$revenue->saloon_id??'']);
                    $revenueCreated = true;
                }
            }
            if($revenue->time_cycle == "monthly"){
                $startDate = Carbon::parse($revenue->start_date);
                $newStartDate = $startDate->addMonth()->format('Y-m-d');
                if($liveDate == $newStartDate && $liveDate < $endDate ){
                    Revenue::Create(['name'=>$revenue->name??'','date'=>$revenue->date??'','client_name'=>$revenue->client_name??'','amount'=>$revenue->amount??'',
                        'vendor'=>$revenue->vendor??'','tax'=>$revenue->tax??'','total_amount'=>$revenue->total_amount??'','client_type'=>$revenue->client_type??'',
                        'attachments'=>$revenue->attachments??'','is_recurring'=>$revenue->is_recurring??'','time_cycle'=>$revenue->time_cycle??'',
                        'start_date'=>$revenue->start_date??'','end_date'=>$revenue->end_date??'','description'=>$revenue->description??'','saloon_id'=>$revenue->saloon_id??'']);
                    $revenueCreated = true;
                }
            }
        }
        return response()->json(['success' => $revenueCreated]);
    }
    //expire products
    public function productExpire()
    {
        $liveDate      = now()->format('Y-m-d');
        $products      = ProductInventory::whereNull('status')->where('quantity','>','0')->get();
        $productExpire = false;
        foreach($products as $product)
        {
            $expireDate = $product->expiry_date;
            if ($liveDate >= $expireDate) {
                $totalAmount   = $product->quantity * $product->per_cost_price;
                $purchaseOrder = PurchaseOrder::create(["salon_id"=>$product->products->salon_id,"notes"=>"expire products","total_amount_with_vat"=>$totalAmount,
                                    "total_amount_without_vat"=> $totalAmount,'date'=>$liveDate,'status'=>"expire",'total_quantity'=>$product->quantity]);
                $expense = Expense::create([
                    'name'                => 'Stock-Out Expire',
                    'purchase_order_id'   => $purchaseOrder->id??"",
                    'saloon_id'           => $product->products->salon->id??"",
                    'vendor'              => $product->products->salon->name??"",
                    'expense_category_id' => '6',
                    'amount'              => $totalAmount??"",
                    'total_amount'        => $totalAmount??"",
                    'tax'                 => $purchaseOrder->vat??"",
                    'date'                => $liveDate??"",
                    'description'         => "expire products",
                ]);
                $productInventory                     = ProductInventory::findorfail($product->id);
                $productInventory->quantity          -= $product->quantity;
                $productInventory->consumed_quantity += $product->quantity;
                $productInventory->save();
                StockOut::create(['product_id' => $product->product_id, 'quantity' => $product->quantity, 'price_per_product' => $product->per_cost_price,
                    'total_price_per_product'  => $totalAmount, 'cost_price_per_product' => $totalAmount ??"", 'salon_id' =>$product->products->salon_id,
                    'purchase_order_id' => $purchaseOrder->id, 'sku_id'=>$product->sku_id]);
                $productExpire = true;
                $data = [
                    'productName'           => $product->products->name,
                    'productQuantity'       => $product->quantity,
                    'productExpireDate'     => $product->expiry_date,
                    'productCostPrice'      => $product->per_cost_price,
                    'productTotalCostPrice' => $totalAmount,
                    'vendorName'            => $product->products->salon->name,
                    'liveDate'              => $liveDate,
                ];
                $custom = CustomNotification::create(
                    [
                        'notifiable_id'   => $product->products->salon->id??"",
                        'notifiable_type' => 'App\User',
                        'type'            => 'product_expire',
                        'data'            => $data,
                    ]
                );
            }
        }
        return response()->json(['success' => $productExpire]);
    }

//    public function ReSubscriptionPayment(){
//        \Stripe\Stripe::setApiKey(env('STRIPE_SECRET_KEY_TEST'));
//        $stripe = new \Stripe\StripeClient(env('STRIPE_SECRET_KEY_TEST'));
//        $invoices = \Stripe\Invoice::all(['limit' => 1000]);
//        $users = UserSubscription::get()->all();
//        foreach($users as $user){
//            foreach($invoices as $invoice){
//                if($user->customer_id == $invoice['customer'] && $user->invoice_id != $invoice['id'] && $invoice['paid'] == true){
//                    if(!UserSubscription::where('invoice_id',$invoice['id'])->first()){
//                        if ($invoice['status'] == "paid"){
//                            $amount = $invoice['total']/100;
//                            $invoice_create = Carbon::createFromTimestamp($invoice['created']);
//                            $end_date      = Carbon::createFromTimestamp($invoice['created']);
//                            if($user->subscription_plan_id==1){
//                                $end_date->addYear(1);
//                            }else if($user->subscription_plan_id==2){
//                                $end_date->addMonths(1);
//                            }
//                            $userSubscription = UserSubscription::create([
//                                'user_id' => $user->user_id,
//                                'subscription_plan_id' => $user->subscription_plan_id,
//                                'amount_captured' => $amount * 100,
//                                'captured_status' => $invoice['status'],
//                                'captured_at' => date('d-m-Y', strtotime($invoice_create)),
//                                'currency' => 'SAR',
//                                'invoice_id' => $invoice['id'],
//                                'charge_id' => $invoice['subscription'],
//                                'description' => $user->description,
//                                'customer_id' => $user->customer_id,
//                                'invoice_url' => $invoice['hosted_invoice_url'],
//                            ]);
//                            $company = User::whereHas(
//                                'roles', function($q){
//                                $q->where('name', 'user');
//                            }
//                            )->first();
//                            FatoraInvoice::create(['user_subscription_id'=>$userSubscription->id,'company_name'=>$company->name,'company_address'=>$company->profile->address,'company_vat_number'=>$company->profile->vat_number,'commercial_registration_number'=>38833738,'customer_name'=>$user->user->name,'customer_address'=>$user->user->profile->address,'customer_vat_number'=>$user->user->profile->vat_number,'total_amount'=>$amount * 100,'unit_price'=>$amount,'vat_amount'=>1 * 15,'quantity'=>1,'description'=>$userSubscription->description,'invoice_number'=>$invoice['id'],'current_date'=>date('d-m-Y', strtotime($invoice_create)),'due_date'=>date('d-m-Y', strtotime($end_date))]);
//                            return back();
//                        }else{
//                            $userSubscription = UserSubscription::create([
//                                'user_id' => $user->user_id,
//                                'subscription_plan_id' => $user->subscription_plan_id,
//                                'amount_captured' => $user->amount,
//                                'captured_status' => "fail",
//                                'captured_at' => null,
//                                'currency' => 'SAR',
//                                'invoice_id' => $invoice['id'],
//                                'charge_id' => null,
//                                'description' => $user->description,
//                                'customer_id' => $user->customer_id,
//                                'invoice_url' => null,
//                            ]);
//                        }
//                    }
//                }else{
//                    return 'no fail';
//                }
//            }
//        }
//    }
    public function reSubscribe(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET_KEY_TEST'));
        $payload = $request->getContent();
        $header  = $request->header('Stripe-Signature');
        $secret  = "whsec_C79IUcd6QARh9bcXkcIytnDp4D8oXl3A";
        try {
            $event = Webhook::constructEvent(
                $payload, $header, $secret
            );
        } catch (SignatureVerificationException $e) {
            Log::error('Webhook signature verification failed.');
            return response('Invalid webhook signature.', 400);
        }
        if ($event['type'] === 'customer.subscription.updated') {
            $subscription        = $event['data']['object'];
            $userSubscription    = UserSubscription::where('customer_id', $subscription['customer'])->orderBy('id', 'DESC')->first();
            $newUserSubscription = UserSubscription::create([
                'user_id'              => $userSubscription->user_id,
                'subscription_plan_id' => $userSubscription ? $userSubscription->subscription_plan_id : null,
                'amount_captured'      => $subscription->plan->amount,
                'captured_status'      => $subscription['status'],
                'captured_at'          => now(),
                'currency'             => $subscription['currency'],
                'invoice_id'           => $subscription['latest_invoice'],
                'charge_id'            => $subscription['id'],
                'description'          => $userSubscription ? $userSubscription->description : null,
                'customer_id'          => $subscription['customer'],
                'product_id'           => $subscription['items']['data'][0]['price']['product'],
                'price_id'             => $subscription['items']['data'][0]['price']['id'],
            ]);

            Log::info('UserSubscription record created for subscription ID:', $subscription['id']);
        }
        return response('Webhook received successfully.');
    }
    public function discountCodesInactive(){
        $currentDate = \Carbon\Carbon::now();
        Discount::where('status','1')->where('validity_date_from', '<', $currentDate)
            ->update(['status' => '0']);
        return response('Expire Discount Update');
    }

    public function changeLang(Request $request)
    {

        App::setLocale($request->lang);
        session()->put('locale', $request->lang);

        return redirect()->back();
    }
    public function shuffleModal(Request $request){
        $cashier       = User::findOrFail($request->id);
        $currentBranch = User::where('id',$request->salonId)->first();
        $branches      = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', $currentBranch->salon_id)->orWhere('id', $currentBranch->salon_id)->get();
        return view('website.ajax.shuffle_modal', compact('cashier','currentBranch','branches'));
    }
    public function branchShuffleCashier(Request $request){
        if ($request->new_branch_id != null){
            $salon = User::where('id',$request->new_branch_id)->first();
            $cashierCount = $salon->allCashierCount;
            $premiumAddonSalonCashier = PremiumAddonSalonCashier::where('primary_salon_id',Auth::user()->salon_id)->orderBy('id','DESC')->first();
            if (isset($premiumAddonSalonCashier) && $premiumAddonSalonCashier !=null){
                if ($premiumAddonSalonCashier->premiumAddonHistoryRemaingCashier == 0){
                    return redirect()->back()->with(['title' => 'Try Again', 'message' => trans('messages.Pleaseselectbranch'), 'type' => 'error']);
                }else{
                    $cashierShuffleBranch = CashierShuffleBranch::create(['cashier_id'=>$request->cashier_id,'old_branch_id'=>$request->old_branch_id,'new_branch_id'=>$request->new_branch_id,'status'=>$request->status]);
                    $cashier = User::where('id',$request->cashier_id)->update(['salon_id'=>$request->new_branch_id]);
                    return redirect()->back()->with(['title' => 'Done', 'message' => trans('messages.Cashier Shuffle other Branch'), 'type' => 'success']);
                }
            }elseif($cashierCount >= 1){
                return redirect()->back()->with(['title' => 'Try Again', 'message' => trans('messages.Pleaseselectbranch'), 'type' => 'error']);
            }else{
                $cashierShuffleBranch = CashierShuffleBranch::create(['cashier_id'=>$request->cashier_id,'old_branch_id'=>$request->old_branch_id,'new_branch_id'=>$request->new_branch_id,'status'=>$request->status]);
                $cashier = User::where('id',$request->cashier_id)->update(['salon_id'=>$request->new_branch_id]);
                return redirect()->back()->with(['title' => 'Done', 'message' => trans('messages.Cashier Shuffle other Branch'), 'type' => 'success']);
            }
        }else{
            return redirect()->back()->with(['title' => 'Try Again', 'message' => trans('messages.Pleaseselectbranch'), 'type' => 'error']);
        }
    }
    public function addPaymentCard(Request $request){
        extract($request->all());
        try{
            $stripe = new \Stripe\StripeClient(env('STRIPE_SECRET_KEY_TEST'));
            $date   = explode('/', $card_expiry);
            $token  = $stripe->tokens->create([
                'card' => [
                    'number'    => $card_number,
                    'exp_month' => (int)$date[0],
                    'exp_year'  => (int)$date[1],
                    'cvc'       => $card_cvv_number,
                ],
            ]);
            if ($token){
                if (isset($is_primary) && $is_primary=='on'){
                    PaymentCard::where('salon_id',Auth::user()->salon_id)->update(['is_primary'=>'false']);
                    $paymentCard = PaymentCard::create(['card_number'=>$card_number,'name'=>$card_holder_name,'cvv'=>$card_cvv_number,'expiry_date'=>$card_expiry,'salon_id'=>Auth::user()->salon_id,'type'=>$card_type,'stripe_token'=>$token->id,'client_ip'=>$token->client_ip,'fingerprint'=>$token->card->fingerprint,'last4'=>$token->card->last4,'is_primary'=>'true']);
                }else{
                    $paymentCard = PaymentCard::create(['card_number'=>$card_number,'name'=>$card_holder_name,'cvv'=>$card_cvv_number,'expiry_date'=>$card_expiry,'salon_id'=>Auth::user()->salon_id,'type'=>$card_type,'stripe_token'=>$token->id,'client_ip'=>$token->client_ip,'fingerprint'=>$token->card->fingerprint,'last4'=>$token->card->last4]);
                }
                if ($paymentCard){
                    return redirect()->back()->with(['title'=>'Done','message'=>trans('messages.Carddetailssavedsuccessfully!'),'type'=>'success']);
                }else{
                    return redirect()->back()->with(['title'=>'Fail','message'=>trans('messages.Unable to save card details'),'type'=>'error']);
                }//end if else.
            }//end if.
        }catch(\Exception $e){
            return redirect()->back()->with(['title'=>'Fail','message'=>'Unable to generate token, '.$e->getMessage(),'type'=>'error'])->withInput();;
        }//end try catch.
    }//end addPaymentCard function.
    public function employeeLeaveModal(Request $request){
        $employee = User::findOrFail($request->id);
        $assignedAppointments = AssignedCustomer::where('employee_id',$employee->id)->pluck('appointment_id');
        $customerAppointmentDates = CustomerAppointment::whereIn('id',$assignedAppointments)->whereIn('status',['Pending','Approved'])->pluck('customer_appointment_date')->toArray();
        $html = view('website.ajax.employee_leave_modal', compact('employee','customerAppointmentDates'))->render();
        return response()->json(['customerAppointmentDates'=>$customerAppointmentDates,'html' => $html]);
    }
    public function premiumAddonsPackages(){
        $premiumAddons = PremiumAddonPackage::get();
        return view('dashboard.adminDashboard.preium_addons_packages', compact('premiumAddons'));
    }
    public function addPaymentCardAjax(Request $request){
        extract($request->all());
        try{
            $stripe = new \Stripe\StripeClient(env('STRIPE_SECRET_KEY_TEST'));
            $date = explode('/', $card_expiry);
            $token = $stripe->tokens->create([
                'card' => [
                    'number'    => $card_number,
                    'exp_month' => (int)$date[0],
                    'exp_year'  => (int)$date[1],
                    'cvc'       => $card_cvv_number,
                ],
            ]);
            if ($token){
                if (isset($is_primary) && $is_primary=='on'){
                    PaymentCard::where('salon_id',Auth::user()->salon_id)->update(['is_primary'=>'false']);
                    $paymentCard = PaymentCard::create(['card_number'=>$card_number,'name'=>$card_holder_name,'cvv'=>$card_cvv_number,'expiry_date'=>$card_expiry,'salon_id'=>Auth::user()->salon_id,'type'=>$card_type,'stripe_token'=>$token->id,'client_ip'=>$token->client_ip,'fingerprint'=>$token->card->fingerprint,'last4'=>$token->card->last4,'is_primary'=>'true']);
                }else{
                    $paymentCard = PaymentCard::create(['card_number'=>$card_number,'name'=>$card_holder_name,'cvv'=>$card_cvv_number,'expiry_date'=>$card_expiry,'salon_id'=>Auth::user()->salon_id,'type'=>$card_type,'stripe_token'=>$token->id,'client_ip'=>$token->client_ip,'fingerprint'=>$token->card->fingerprint,'last4'=>$token->card->last4]);
                }
                if ($paymentCard){
                    $paymentCards = PaymentCard::where('salon_id',Auth::user()->salon_id)->orderBy('id','DESC')->limit(3)->get();
                    $html = view('website.ajax.all_new_saved_cards', compact('paymentCards'))->render();
                    $result = 'true';
                    return response()->json(['result'=>$result,'html' => $html]);
                }else{
                    $result = 'false';
                    return response()->json(['result'=>$result]);
                }//end if else.
            }//end if.
        }catch(\Exception $e){
            return response()->json(['msg'=>$e->getMessage()]);
        }//end try catch.
    }//end addPaymentCardAjax function.
    public function changeStatusPremiumAddons(Request $request){
        try {
            $updateAddonStatus = PremiumAddonPackage::where('id', $request->id)->update(['status' => $request->status]);
            return response()->json(['message' => 'Status updated successfully', 'success' => true], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'An error occurred: ' . $e->getMessage(), 'success' => false], 500);
        }
    }
    public function chargePaymentAddonCashier(Request $request){
        $request->validate([
            'card_number' => 'required|string|min:12|max:19',
            'expiry_date' => [
                'required',
                'regex:/^(0[1-9]|1[0-2])\/\d{4}$/', // accepts MM/YYYY
                function ($attribute, $value, $fail) {
                    [$month, $year] = explode('/', $value);
                    $expDate = \DateTime::createFromFormat('m/Y', $value);
                    $now = new \DateTime('first day of this month'); // current month

                    if (!$expDate || $expDate < $now) {
                        $fail('The expiry date must be in the future.');
                    }
                }
            ],
            'cvc_number' => 'required|digits_between:3,5',
            'card_first_name' => 'required|string|max:255',
            'card_last_name' => 'required|string|max:255',
        ]);
        try {
            if ($request->premium_addon_id != null){
                $premiumAddonPackage = PremiumAddonPackage::where('id',$request->premium_addon_id)->first();
                $description = $premiumAddonPackage->descriptionDetails;
                $taxedAmount = $request->taxedprice;
                $baseUrl = url('addons_sales_payment');
                $cardNumber = str_replace(' ', '', $request->card_number);
                $payload = [
                    'payer_country' => 'SA',
                    'payer_address' => Auth::user()->profile->address??'',
                    'order_amount' => number_format($taxedAmount, 2),
                    'action' => 'SALE',
                    'card_cvv2' => $request->cvc_number,
                    'payer_zip' =>  Auth::user()->profile->postal??'',
                    'payer_ip' => request()->ip(),
                    'order_currency' => 'SAR',
                    'payer_first_name' => $request->card_first_name,
                    'card_exp_month' => explode('/', $request->expiry_date)[0],
                    'payer_city' =>  Auth::user()->profile->city??'',
                    'card_exp_year' => explode('/', $request->expiry_date)[1],
                    'payer_last_name' => $request->card_last_name,
                    'payer_phone' => Auth::user()->profile->phone,
                    'order_description' => $description,
                    'payer_email' => Auth::user()->email,
                    'card_number' => $cardNumber,
                    'premium_addon_id' => $request->premium_addon_id,
                    'no_of_users' => $request->no_of_users,
                    'user_id' => Auth::id(),
                    'type' => 'user_addons',
                    'card_first_name' => $request->card_first_name,
                    'card_last_name' => $request->card_last_name,
                    'addon_type' => 'cashier',
                ];
                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ])->asForm()->get($baseUrl, $payload);
                return $response;
            } else {
                return response()->json(['message' => 'Premium Addons ID is required', 'success' => false], 400);
            }
        } catch (\Exception $e) {
            return response()->json(['message' => 'An error occurred: ' . $e->getMessage(), 'success' => false], 500);
        }
    }
    public function chargePaymentAddonEmployee(Request $request){
        try {
            if ($request->premium_addon_id != null){
                $premiumAddonPackage = PremiumAddonPackage::where('id',$request->premium_addon_id)->first();
                $description = $premiumAddonPackage->descriptionDetails;
                $taxedAmount = $request->taxedprice;
                $baseUrl = url('addons_sales_payment');
                $cardNumber = str_replace(' ', '', $request->card_number);
                $payload = [
                    'payer_country' => 'SA',
                    'payer_address' => Auth::user()->profile->address??'',
                    'order_amount' => number_format($taxedAmount, 2),
                    'action' => 'SALE',
                    'card_cvv2' => $request->cvc_number,
                    'payer_zip' =>  Auth::user()->profile->postal??'',
                    'payer_ip' => request()->ip(),
                    'order_currency' => 'SAR',
                    'payer_first_name' => $request->card_first_name,
                    'card_exp_month' => explode('/', $request->expiry_date)[0],
                    'payer_city' =>  Auth::user()->profile->city??'',
                    'card_exp_year' => explode('/', $request->expiry_date)[1],
                    'payer_last_name' => $request->card_last_name,
                    'payer_phone' => Auth::user()->profile->phone,
                    'order_description' => $description,
                    'payer_email' => Auth::user()->email,
                    'card_number' => $cardNumber,
                    'premium_addon_id' => $request->premium_addon_id,
                    'no_of_users' => $request->no_of_users,
                    'user_id' => Auth::id(),
                    'type' => 'user_addons',
                    'card_first_name' => $request->card_first_name,
                    'card_last_name' => $request->card_last_name,
                    'addon_type' => 'employee',
                ];
                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ])->asForm()->get($baseUrl, $payload);
                return $response;
            } else {
                return response()->json(['message' => 'Premium Addons ID is required', 'success' => false], 400);
            }
        } catch (\Exception $e) {
            return response()->json(['message' => 'An error occurred: ' . $e->getMessage(), 'success' => false], 500);
        }
    }
    public function changeOurServiceOrder(Request $request){
        $positions = $request->input('positions');
        foreach ($positions as $index => $serviceId) {
            $service = OurService::find($serviceId);
            if ($service) {
                $service->position = $index + 1;
                $service->save();
            }
        }
        return response()->json(['message' => 'Positions updated successfully']);
    }
    public function mailingListSubmit(Request $request){
        $request->validate([
            'mail_inp' => 'required',
        ]);
        try{
            NewsLetter::updateOrCreate(['email'=>$request->mail_inp]);
            $data = array(
                'email' => $request->mail_inp,
                'detail' => env('APP_URL'),
                'login_url' => env('APP_URL'),
                'site_url' => env('APP_URL'),
            );
            Mail::send('website.email_templates.news_letter', ['data' => $data], function ($message) use ($data) {
                $message->to($data['email'])->cc('<EMAIL>', 'Usman Dev')->subject('');
            });
            return response()->json(['result'=>'Success','msg' => 'Newsletter Successfully Submit']);
        }catch (\Exception $e){
            return ['type'=>'error','msg'=>'Unable to process request try again'];
        }
    }
    public function deletePaymentCard(Request $request){
        extract($request->all());
        $paymentCard = PaymentCard::where('id',$request->card_id)->first();
        if ($paymentCard){
            $paymentCard->delete();
            return response()->json(['result'=>'Success','message' => 'Card deleted successfully']);
        }else{
            return response()->json(['result' => 'An error occurred']);
        }//end if else.
    }//end function deletePaymentCard.
    public function employeeShuffleModal(Request $request){
        $employee      = User::findOrFail($request->id);
        $currentBranch = User::where('id',$request->salonId)->first();
        $branches      = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', $currentBranch->salon_id)->orWhere('id', $currentBranch->salon_id)->get();
        $employeeServices   = SalonService::whereIn('id',$employee->employeeServiceIds)->get();
        $employeeCategories = ServiceCategory::whereIn('id',$employee->employeeServiceCategoryIds)->get();

        return view('website.ajax.employee_shuffle_modal', compact('employee','currentBranch','branches','employeeServices','employeeCategories'));
    }
    public function tourSettingStatus(Request $request){
        if($request->option != null){
            $user = User::where('id',$request->id)->update(['tour_setting_status'=>$request->option]);
            return Auth::user()->tour_setting_status;
        }
    }
    public function closedTourSettingStatus(Request $request){
        if($request->id != null){
            $user = User::where('id',Auth::id())->first();
            if ($user->tour_status != 6){
                $user->update(['tour_setting_status'=>'0']);
                return 'true';
            }else{
                return 'false';
            }
        }
    }
    public function viewStates(Request $request){
        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
        $branchIds = $allBranches->pluck('id');
        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
            ->orderBy('user_id', 'ASC')
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy('user_id');
        $currentDate = now();
        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
            $dueDate = $subs->first()->fatoraPdf->due_date;
            return $dueDate->isPast();
        })->keys();
        $customerIdsFromAppointments = CustomerAppointment::whereIn('salon_id', $branchIds)
            ->pluck('customer_id');
        $customerIdsFromUsers = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        })
            ->whereIn('salon_id', $branchIds)
            ->pluck('id');
        $allCustomerIds = $customerIdsFromAppointments->merge($customerIdsFromUsers)->unique();
        $customer = $allCustomerIds->count();
        $totalservice         = SalonService::whereIn('salon_id',$branchIds)->count();
        $totalproduct         = Product::whereIn('salon_id',$branchIds)->count();
        $currentDate          = Carbon::now()->format('m/d/Y');
        $allAppointments      = CustomerAppointment::whereIn('salon_id', $branchIds)->get();
        $upcomingAppointments = CustomerAppointment::whereIn('salon_id', $branchIds)->where('customer_appointment_date', '>', $currentDate)
                                ->whereIn('status', ['Pending', 'Approved'])->get();
        $currentAppointments  = CustomerAppointment::whereIn('salon_id', $branchIds)
                                ->where('customer_appointment_date', '<=', $currentDate)->whereIn('status', ['Pending', 'Approved'])->get();
        $customerAppointments = CustomerAppointment::whereIn('salon_id', $branchIds)
            ->whereNotIn('salon_id', $expiredUserIds)
            ->where('status', 'Complete')
            ->orderBy('id', 'DESC')
            ->get();
        $revenue                   = Revenue::whereIn('saloon_id',$branchIds)->get();
        $customerAppointmentsItems = $customerAppointments;
        $revenueItems              = $revenue;
        $allRevenue                = $customerAppointmentsItems->merge($revenueItems);
        $totalRevenue              = $allRevenue->map(function ($item) {
            return $item->total_amount ?? $item->appointmentPrice ?? 0;
        })->sum();
        return view('website.ajax.salon_owner_dashboard_states',compact('totalproduct','totalservice','currentAppointments','upcomingAppointments','allAppointments','customer','totalRevenue'));
    }
    public function viewStatesCashier(){
        $allCustomers = CustomerAppointment::where('salon_id', Auth::user()->salon_id)->orderBy('id', 'DESC')->get();
        $currentDate = Carbon::now()->format('m/d/Y');
        $upcomingAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
            ->where('customer_appointment_date', '>', $currentDate)
            ->whereIn('status', ['Pending', 'Approved'])
            ->orderBy('customer_appointment_date', 'asc')
            ->get();
        $currentAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
            ->where('customer_appointment_date', '<=', $currentDate)
            ->whereIn('status', ['Pending', 'Approved'])
            ->orderBy('customer_appointment_date', 'DESC')
            ->get();
        $completeAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
            ->where('status', 'Complete')
            ->orderBy('customer_appointment_date', 'asc')
            ->get();
        $purchaseOrders = PurchaseOrder::where('salon_id', Auth::user()->salon_id)
            ->where('status','sales')
            ->whereNull('appointment_id')
            ->whereNull('employee_id')
            ->orderBy('date','DESC')
            ->get();
        $cancelAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
            ->where('status', 'Cancel')
            ->orderBy('customer_appointment_date', 'asc')
            ->get();
        $html1 = view('website.ajax.cashier_dashboard_states',compact('allCustomers','cancelAppointments','completeAppointments','currentAppointments','upcomingAppointments','purchaseOrders'))->render();
        $html2 = view('website.ajax.cashier_dashboard_states_sidebar',compact('allCustomers','cancelAppointments','completeAppointments','currentAppointments','upcomingAppointments','purchaseOrders'))->render();
        return response()->json(['html1'=>$html1 , 'html2' => $html2], 200);
    }
    public function cashierDashboardData(Request  $request){
        $salon = User::findOrFail(Auth::user()->salon_id);
        $currentDate = Carbon::now()->format('m/d/Y');
        $val = $request->val;
        if ($request->val == "customers_tab_button"){
            $salon = User::findOrFail(Auth::user()->salon_id);
            $allCategoriesIds = SalonService::where('salon_id', $salon->id)->pluck('category_id')->unique()->toArray();
            $serviceCategories = ServiceCategory::where('salon_id', $salon->id)->orWhereNull('salon_id')->orderBy('id', 'DESC')->get();
            $productCategories = ServiceCategory::whereIn('id', $salon->allEmployeeProductCategoryIds)->orderBy('id', 'DESC')->get();
            $services = SalonService::where('salon_id', Auth::user()->salon_id)->get();
            $products = Product::where('salon_id', Auth::user()->salon_id)->with('productImages')->where('product_type_id', 1)->get();
            return view('website.ajax.cashier_dashboard_tabs_ajax', compact('val','salon','serviceCategories', 'productCategories'));
        }else if($request->val == "upcoming_tab_button"){
             $upcomingAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
                ->where('customer_appointment_date', '>', $currentDate)
                ->whereIn('status', ['Pending', 'Approved'])
                ->orderBy('customer_appointment_date', 'ASC')
                ->get();
            return view('website.ajax.cashier_dashboard_tabs_ajax', compact('val','salon','upcomingAppointments'));

        }else if($request->val == "approved_tab_button"){
            $currentAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
                ->where('customer_appointment_date', '<=', $currentDate)
                ->whereIn('status', ['Pending', 'Approved'])
                ->orderBy('customer_appointment_date', 'DESC')
                ->get();
            return view('website.ajax.cashier_dashboard_tabs_ajax', compact('val','salon','currentAppointments'));
        }else if($request->val == "completed_tab_button"){
            $completeAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
                ->where('status', 'Complete')
                ->orderBy('updated_at', 'DESC')
                ->get();
            $purchaseOrders = PurchaseOrder::where('salon_id', Auth::user()->salon_id)
                ->where('status','sales')
                ->whereNull('appointment_id')
                ->whereNull('employee_id')
                ->orderBy('updated_at','DESC')
                ->get();
            return view('website.ajax.cashier_dashboard_tabs_ajax', compact('val','salon','completeAppointments','purchaseOrders'));
        }else if($request->val == "cancel_tab_button"){
           $cancelAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
                ->where('status', 'Cancel')
                ->orderBy('customer_appointment_date', 'DESC')
                ->get();
            return view('website.ajax.cashier_dashboard_tabs_ajax', compact('val','salon','cancelAppointments'));
        }else if($request->val == "history_tab_button"){
            $allCustomers   = CustomerAppointment::where('salon_id', Auth::user()->salon_id)->orderBy('customer_appointment_date', 'DESC')->get();
            $purchaseOrders = PurchaseOrder::where('salon_id', Auth::user()->salon_id)
                ->where('status','sales')
                ->whereNull('appointment_id')
                ->whereNull('employee_id')
                ->orderBy('updated_at','DESC')
                ->get();
            return view('website.ajax.cashier_dashboard_tabs_ajax', compact('val','salon','allCustomers','purchaseOrders'));
        }
    }
    public function viewDetail($id=null){
        $salon = User::findOrFail($id);
        return view('website.ajax.view_detail', compact('salon'));
    }
    public function checkProductQuantityAvailabilityForServices(Request $request){
        if (isset($request->allProducts) && $request->allProducts != null){
            $currentProductId = $request->selectedProducts[0]['id'];
            $currentProductSkuId = $request->selectedProducts[0]['skuId'];
            $currentProductQuantity = $request->selectedProducts[0]['quantity'];
            $currentDate = now();
            $productIds = [];
            $productSkyIds = [];
            foreach ($request->allProducts as $key => $item){
                $productIds[] = $item['productId'];
                $productSkyIds[] = $item['skuId'];
                // Check if both Product ID and SKU ID match
                if ($item['productId'] == $currentProductId && $item['skuId'] == $currentProductSkuId) {
                    // Store the matching entries in a separate variable
                    $matchingEntries[] = $item;
                }
            }
            if(in_array($currentProductId,$productIds)){
                $currentDate = now();
                if (in_array($currentProductSkuId,$productSkyIds)){
                    $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                    $productInventoryQunatity = $productInventory->quantity;
                    $consumneQuantity = 0;
                    foreach ($matchingEntries as $entry) {
                        $consumneQuantity += $entry['quantity'];
                    }
                    $avalible = $productInventoryQunatity - $consumneQuantity;
                    if($avalible >= $currentProductQuantity){
                        $product = Product::find($currentProductId);
                        $perCostPrice = $productInventory->per_cost_price;
                        $totalPrice = $currentProductQuantity * $perCostPrice;
                        $newQuantity = $avalible - $currentProductQuantity;
                        if($newQuantity == 0){
                            $newProductInventory = ProductInventory::where('product_id',$product->id)
                                ->where('is_deleted','0')
                                ->whereNotIn('sku_id',[$productInventory->sku_id])
                                ->where('expiry_date', '>=', $currentDate)
                                ->where('quantity','>',0)
                                ->first();
                            if(isset($newProductInventory) && $newProductInventory != null){
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                    'product_id' => $product->id,
                                    'sku_id' => $newProductInventory->sku_id,
                                ]);
                            }else{
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                    'product_id' => $product->id,
                                ]);
                            }
                        }else{
                            return response([
                                'result' => 'Available',
                                'message' => '',
                                'totalPrice' => $totalPrice,
                            ]);
                        }

                    }else{
                        $currentSku = Product::where('id', $currentProductId)
                            ->first()
                            ->productInventories()
                            ->where('sku_id', $currentProductSkuId)
                            ->first();
                        if ($currentSku) {
                            return response([
                                'result' => 'Not Available',
                                'message' => $avalible . ' units are available for this Product SKU ID, but you requested ' . $currentProductQuantity . ' units.',
                                'totalPrice' => 0,
                            ]);
                        } else {
                            return response([
                                'result' => 'Not Available',
                                'message' => 'This product does not have the SKU you entered.',
                                'totalPrice' => 0,
                            ]);
                        }
                    }
                }else{
                    $product = Product::find($currentProductId);
                    $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                    if ($product && $productInventory) {
                        $currentSku = $productInventory;
                        if ($currentSku->quantity >= $currentProductQuantity) {
                            $avalible = $productInventory->quantity;
                            $newQuantity = $avalible - $currentProductQuantity;
                            if($newQuantity == 0){
                                $perCostPrice = $currentSku->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perCostPrice;
                                $newProductInventory = ProductInventory::where('product_id',$product->id)
                                    ->where('is_deleted','0')
                                    ->whereNotIn('sku_id',[$productInventory->sku_id])
                                    ->where('expiry_date', '>=', $currentDate)
                                    ->where('quantity','>',0)
                                    ->first();
                                if(isset($newProductInventory) && $newProductInventory != null){
                                    return response([
                                        'result' => 'Available',
                                        'message' => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                        'sku_id' => $newProductInventory->sku_id,
                                    ]);
                                }else{
                                    return response([
                                        'result' => 'Available',
                                        'message' => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                    ]);
                                }
                            }else{
                                $perCostPrice = $currentSku->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perCostPrice;
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                ]);
                            }
                        }
                    }
                    $currentSku = Product::where('id', $currentProductId)
                        ->first()
                        ->productInventories()
                        ->where('sku_id', $currentProductSkuId)
                        ->first();
                    if ($currentSku) {
                        $availableQuantity = $currentSku->quantity;
                        return response([
                            'result' => 'Not Available',
                            'message' => $availableQuantity . ' units are available for this Product SKU ID, but you requested ' . $currentProductQuantity . ' units.',
                            'totalPrice' => 0,
                        ]);
                    } else {
                        return response([
                            'result' => 'Not Available',
                            'message' => 'This product does not have the SKU you entered.',
                            'totalPrice' => 0,
                        ]);
                    }
                }
            }else{
                $product = Product::whereHas('productInventories', function ($query) use ($currentProductSkuId, $currentProductQuantity) {
                    $query->where('sku_id', $currentProductSkuId)
                        ->where('quantity', '>=', $currentProductQuantity);
                })->with(['productInventories' => function($query) use ($currentProductSkuId, $currentProductQuantity) {
                    $query->where('sku_id', $currentProductSkuId)
                        ->where('quantity', '>=',$currentProductQuantity);
                }])->find($currentProductId);
                if ($product && $product->productInventories->isNotEmpty()) {
                    $product = Product::find($currentProductId);
                    $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                    if ($product && $productInventory) {
                        $currentSku = $productInventory;
                        if ($currentSku->quantity >= $currentProductQuantity) {
                            $avalible = $productInventory->quantity;
                            $newQuantity = $avalible - $currentProductQuantity;
                            if($newQuantity == 0){
                                $perCostPrice = $currentSku->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perCostPrice;
                                $newProductInventory = ProductInventory::where('product_id',$product->id)
                                    ->where('is_deleted','0')
                                    ->whereNotIn('sku_id',[$productInventory->sku_id])
                                    ->where('expiry_date', '>=', $currentDate)
                                    ->where('quantity','>',0)
                                    ->first();
                                if(isset($newProductInventory) && $newProductInventory != null){
                                    return response([
                                        'result'     => 'Available',
                                        'message'    => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                        'sku_id'     => $newProductInventory->sku_id,
                                    ]);
                                }else{
                                    return response([
                                        'result'     => 'Available',
                                        'message'    => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                    ]);
                                }
                            }else{
                                $perCostPrice = $currentSku->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perCostPrice;
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                ]);
                            }
                        }
                    }
                    $currentSku = $product->productInventories->first();
                    if ($currentSku->quantity >= $currentProductQuantity) {
                        $perCostPrice = $currentSku->per_cost_price;
                        $totalPrice = $currentProductQuantity * $perCostPrice;
                        return response([
                            'result'  => 'Available',
                            'message' => '',
                            'totalPrice' => $totalPrice,
                        ]);
                    }
                }
                $currentSku = Product::where('id', $currentProductId)
                    ->first()
                    ->productInventories()
                    ->where('sku_id', $currentProductSkuId)
                    ->first();
                if ($currentSku) {
                    $availableQuantity = $currentSku->quantity;
                    return response([
                        'result'     => 'Not Available',
                        'message'    => $availableQuantity . ' units are available for this Product SKU ID, but you requested ' . $currentProductQuantity . ' units.',
                        'totalPrice' => 0,
                    ]);
                } else {
                    return response([
                        'result' => 'Not Available',
                        'message' => 'This product does not have the SKU you entered.',
                        'totalPrice' => 0,
                    ]);
                }
            }
        }else{
            $currentProductId       = $request->selectedProducts[0]['id'];
            $currentProductSkuId    = $request->selectedProducts[0]['skuId'];
            $currentProductQuantity = $request->selectedProducts[0]['quantity'];
            $currentDate            = now();
            $product = Product::whereHas('productInventories', function ($query) use ($currentProductSkuId, $currentProductQuantity) {
                $query->where('sku_id', $currentProductSkuId)
                    ->where('quantity', '>=', $currentProductQuantity);
            })->with(['productInventories' => function($query) use ($currentProductSkuId,$currentProductQuantity) {
                $query->where('sku_id', $currentProductSkuId)
                    ->where('quantity', '>=', $currentProductQuantity);
            }])->find($currentProductId);
            if (isset($product->productInventories) && $product->productInventories != null){
                $product = Product::find($currentProductId);
                $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                if ($product && $productInventory) {
                    $currentSku = $productInventory;
                    if ($currentSku->quantity >= $currentProductQuantity) {
                        $avalible = $productInventory->quantity;
                        $newQuantity = $avalible - $currentProductQuantity;
                        if($newQuantity == 0){
                            $perCostPrice        = $currentSku->per_cost_price;
                            $totalPrice          = $currentProductQuantity * $perCostPrice;
                            $newProductInventory = ProductInventory::where('product_id',$product->id)
                                ->where('is_deleted','0')
                                ->whereNotIn('sku_id',[$productInventory->sku_id])
                                ->where('expiry_date', '>=', $currentDate)
                                ->where('quantity','>',0)
                                ->first();
                            if(isset($newProductInventory) && $newProductInventory != null){
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                    'product_id' => $product->id,
                                    'sku_id' => $newProductInventory->sku_id,
                                ]);
                            }else{
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                    'product_id' => $product->id,
                                ]);
                            }
                        }else{
                            $perCostPrice = $currentSku->per_cost_price;
                            $totalPrice = $currentProductQuantity * $perCostPrice;
                            return response([
                                'result' => 'Available',
                                'message' => '',
                                'totalPrice' => $totalPrice,
                            ]);
                        }
                    }
                }

                $currentSku = $product->productInventories->first();
                $perCostPrice = $currentSku->per_cost_price;
                $totalPrice = $currentProductQuantity * $perCostPrice;
                return response(['result'=>'Available','message'=>'','totalPrice'=>$totalPrice,]);
            }else{
                $currentSku = ProductInventory::where('sku_id', $currentProductSkuId)
                    ->whereHas('products', function ($query) use ($currentProductId) {
                        $query->where('id', $currentProductId);
                    })->first();
                $availableQuantity = $currentSku->quantity;
                return response([
                    'result'     => 'Not Available',
                    'message'    => 'Available Quantity: ' . $availableQuantity . '</br> Entered Quantity: ' . $currentProductQuantity,
                    'totalPrice' => 0
                ]);
            }
        }
        return response()->json($product);
    }
    public function checkProductQuantityAvailabilityForSales(Request $request){
        if (isset($request->allProducts) && $request->allProducts != null){
            $currentProductId = $request->selectedProducts[0]['id'];
            $currentProductSkuId = $request->selectedProducts[0]['skuId'];
            $currentProductQuantity = $request->selectedProducts[0]['quantity'];
            $currentDate = now();
            $productIds = [];
            $productSkyIds = [];
            foreach ($request->allProducts as $key => $item){
                $productIds[] = $item['productId'];
                $productSkyIds[] = $item['skuId'];
                if ($item['productId'] == $currentProductId && $item['skuId'] == $currentProductSkuId) {
                    $matchingEntries[] = $item;
                }
            }
            if(in_array($currentProductId,$productIds)){
                $currentDate = now();
                if (in_array($currentProductSkuId,$productSkyIds)){
                    $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                    $productInventoryQunatity = $productInventory->quantity;
                    $consumneQuantity = 0;
                    foreach ($matchingEntries as $entry) {
                        $consumneQuantity += $entry['quantity'];
                    }
                    $avalible = $productInventoryQunatity - $consumneQuantity;
                    if($avalible >= $currentProductQuantity){
                        $product = Product::find($currentProductId);
                        $perCostPrice = $productInventory->price;
                        $perunitCostPrice = $productInventory->per_cost_price;
                        $totalPrice = $currentProductQuantity * $perCostPrice;
                        $newQuantity = $avalible - $currentProductQuantity;
                        if($newQuantity == 0){
                            $newProductInventory = ProductInventory::where('product_id',$product->id)
                                ->where('is_deleted','0')
                                ->whereNotIn('sku_id',[$productInventory->sku_id])
                                ->where('expiry_date', '>=', $currentDate)
                                ->where('quantity','>',0)
                                ->orderBy('expiry_date', 'asc')
                                ->first();
                            if(isset($newProductInventory) && $newProductInventory != null){
                                return response([
                                    'result'     => 'Available',
                                    'message'    => '',
                                    'totalPrice'       => $totalPrice,
                                    'product_id'       => $product->id,
                                    'sku_id'           => $newProductInventory->sku_id,
                                    'perCostPrice'     => $perCostPrice,
                                    'perunitCostPrice' => $perunitCostPrice,
                                ]);
                            }else{
                                return response([
                                    'result'          => 'Available',
                                    'message'         => '',
                                    'totalPrice'      => $totalPrice,
                                    'product_id'      => $product->id,
                                    'perCostPrice'    => $perCostPrice,
                                    'perunitCostPrice'=> $perunitCostPrice,
                                ]);
                            }
                        }else{
                            return response([
                                'result' => 'Available',
                                'message' => '',
                                'totalPrice' => $totalPrice,
                                'perCostPrice'=>$perCostPrice,
                                'perunitCostPrice'=>$perunitCostPrice,
                            ]);
                        }
                    }else{
                        $currentSku = Product::where('id', $currentProductId)
                            ->first()
                            ->productInventories()
                            ->where('sku_id', $currentProductSkuId)
                            ->first();
                        if ($currentSku) {
                            return response([
                                'result' => 'Not Available',
                                'message' => $avalible . ' units are available for this Product SKU ID, but you requested ' . $currentProductQuantity . ' units.',
                                'totalPrice' => 0,
                            ]);
                        } else {
                            return response([
                                'result' => 'Not Available',
                                'message' => 'This product does not have the SKU you entered.',
                                'totalPrice' => 0,
                            ]);
                        }
                    }
                }else{
                    $product = Product::find($currentProductId);
                    $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                    if ($product && $productInventory) {
                        $currentSku = $productInventory;
                        if ($currentSku->quantity >= $currentProductQuantity) {
                            $avalible = $productInventory->quantity;
                            $newQuantity = $avalible - $currentProductQuantity;
                            if($newQuantity == 0){
                                $perCostPrice = $currentSku->price;
                                $perunitCostPrice = $productInventory->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perCostPrice;
                                $newProductInventory = ProductInventory::where('product_id',$product->id)
                                    ->where('is_deleted','0')
                                    ->whereNotIn('sku_id',[$productInventory->sku_id])
                                    ->where('expiry_date', '>=', $currentDate)
                                    ->where('quantity','>',0)
                                    ->orderBy('expiry_date', 'asc')
                                    ->first();
                                if(isset($newProductInventory) && $newProductInventory != null){
                                    return response([
                                        'result' => 'Available',
                                        'message' => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                        'sku_id' => $newProductInventory->sku_id,
                                        'perCostPrice'=>$perCostPrice,
                                        'perunitCostPrice'=>$perunitCostPrice,
                                    ]);
                                }else{
                                    return response([
                                        'result' => 'Available',
                                        'message' => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                        'perCostPrice'=>$perCostPrice,
                                        'perunitCostPrice'=>$perunitCostPrice,
                                    ]);
                                }
                            }else{
                                $perCostPrice = $currentSku->price;
                                $perunitCostPrice = $productInventory->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perCostPrice;
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                    'perCostPrice'=>$perCostPrice,
                                    'perunitCostPrice'=>$perunitCostPrice,
                                ]);
                            }
                        }
                    }
                    $currentSku = Product::where('id', $currentProductId)
                        ->first()
                        ->productInventories()
                        ->where('sku_id', $currentProductSkuId)
                        ->first();
                    if ($currentSku) {
                        $availableQuantity = $currentSku->quantity;
                        return response([
                            'result' => 'Not Available',
                            'message' => $availableQuantity . ' units are available for this Product SKU ID, but you requested ' . $currentProductQuantity . ' units.',
                            'totalPrice' => 0,
                        ]);
                    } else {
                        return response([
                            'result' => 'Not Available',
                            'message' => 'This product does not have the SKU you entered.',
                            'totalPrice' => 0,
                        ]);
                    }
                }
            }else{
                $product = Product::whereHas('productInventories', function ($query) use ($currentProductSkuId, $currentProductQuantity) {
                    $query->where('sku_id', $currentProductSkuId)
                        ->where('quantity', '>=', $currentProductQuantity);
                })->with(['productInventories' => function($query) use ($currentProductSkuId, $currentProductQuantity) {
                    $query->where('sku_id', $currentProductSkuId)
                        ->where('quantity', '>=',$currentProductQuantity);
                }])->find($currentProductId);
                if ($product && $product->productInventories->isNotEmpty()) {
                    $product = Product::find($currentProductId);
                    $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                    if ($product && $productInventory) {
                        $currentSku = $productInventory;
                        if ($currentSku->quantity >= $currentProductQuantity) {
                            $avalible = $productInventory->quantity;
                            $newQuantity = $avalible - $currentProductQuantity;
                            if($newQuantity == 0){
                                $perCostPrice = $currentSku->price;
                                $perunitCostPrice = $productInventory->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perCostPrice;
                                $newProductInventory = ProductInventory::where('product_id',$product->id)
                                    ->where('is_deleted','0')
                                    ->whereNotIn('sku_id',[$productInventory->sku_id])
                                    ->where('expiry_date', '>=', $currentDate)
                                    ->where('quantity','>',0)
                                    ->orderBy('expiry_date', 'asc')
                                    ->first();
                                if(isset($newProductInventory) && $newProductInventory != null){
                                    return response([
                                        'result' => 'Available',
                                        'message' => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                        'sku_id' => $newProductInventory->sku_id,
                                        'perCostPrice'=>$perCostPrice,
                                        'perunitCostPrice'=>$perunitCostPrice,
                                    ]);
                                }else{
                                    return response([
                                        'result' => 'Available',
                                        'message' => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                        'perCostPrice'=>$perCostPrice,
                                        'perunitCostPrice'=>$perunitCostPrice,
                                    ]);
                                }
                            }else{
                                $perCostPrice = $currentSku->price;
                                $perunitCostPrice = $productInventory->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perCostPrice;
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                    'perCostPrice'=>$perCostPrice,
                                    'perunitCostPrice'=>$perunitCostPrice,
                                ]);
                            }
                        }
                    }
                    $currentSku = $product->productInventories->first();
                    if ($currentSku->quantity >= $currentProductQuantity) {
                        $perCostPrice = $currentSku->price;
                        $perunitCostPrice = $productInventory->per_cost_price;
                        $totalPrice = $currentProductQuantity * $perCostPrice;
                        return response([
                            'result' => 'Available',
                            'message' => '',
                            'totalPrice' => $totalPrice,
                            'perCostPrice'=>$perCostPrice,
                            'perunitCostPrice'=>$perunitCostPrice,
                        ]);
                    }
                }
                $currentSku = Product::where('id', $currentProductId)
                    ->first()
                    ->productInventories()
                    ->where('sku_id', $currentProductSkuId)
                    ->first();
                if ($currentSku) {
                    $availableQuantity = $currentSku->quantity;
                    return response([
                        'result' => 'Not Available',
                        'message' => $availableQuantity . ' units are available for this Product SKU ID, but you requested ' . $currentProductQuantity . ' units.',
                        'totalPrice' => 0,
                    ]);
                } else {
                    return response([
                        'result' => 'Not Available',
                        'message' => 'This product does not have the SKU you entered.',
                        'totalPrice' => 0,
                    ]);
                }
            }
        }else{
            $currentProductId       = $request->selectedProducts[0]['id'];
            $currentProductSkuId    = $request->selectedProducts[0]['skuId'];
            $currentProductQuantity = $request->selectedProducts[0]['quantity'];
            $currentDate = now();
            $product = Product::whereHas('productInventories', function ($query) use ($currentProductSkuId, $currentProductQuantity) {
                $query->where('sku_id', $currentProductSkuId)
                    ->where('quantity', '>=', $currentProductQuantity);
            })->with(['productInventories' => function($query) use ($currentProductSkuId,$currentProductQuantity) {
                $query->where('sku_id', $currentProductSkuId)
                    ->where('quantity', '>=', $currentProductQuantity);
            }])->find($currentProductId);
            if (isset($product->productInventories) && $product->productInventories != null){
                $product = Product::find($currentProductId);
                $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                if ($product && $productInventory) {
                    $currentSku = $productInventory;
                    if ($currentSku->quantity >= $currentProductQuantity) {
                        $avalible = $productInventory->quantity;
                        $newQuantity = $avalible - $currentProductQuantity;
                        if($newQuantity == 0){
                            $perCostPrice = $currentSku->price;
                            $perunitCostPrice = $productInventory->per_cost_price;
                            $totalPrice = $currentProductQuantity * $perCostPrice;
                            $newProductInventory = ProductInventory::where('product_id',$product->id)
                                ->where('is_deleted','0')
                                ->whereNotIn('sku_id',[$productInventory->sku_id])
                                ->where('expiry_date', '>=', $currentDate)
                                ->where('quantity','>',0)
                                ->orderBy('expiry_date', 'asc')
                                ->first();
                            if(isset($newProductInventory) && $newProductInventory != null){
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                    'product_id' => $product->id,
                                    'sku_id' => $newProductInventory->sku_id,
                                    'perCostPrice'=>$perCostPrice,
                                    'perunitCostPrice'=>$perunitCostPrice,
                                ]);
                            }else{
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                    'product_id' => $product->id,
                                    'perCostPrice'=>$perCostPrice,
                                    'perunitCostPrice'=>$perunitCostPrice,
                                ]);
                            }
                        }else{
                            $perCostPrice = $currentSku->price;
                            $perunitCostPrice = $productInventory->per_cost_price;
                            $totalPrice = $currentProductQuantity * $perCostPrice;
                            return response([
                                'result' => 'Available',
                                'message' => '',
                                'totalPrice' => $totalPrice,
                                'perCostPrice'=>$perCostPrice,
                                'perunitCostPrice'=>$perunitCostPrice,
                            ]);
                        }
                    }
                }
                $currentSku = $product->productInventories->first();
                $perCostPrice = $currentSku->price;
                $perunitCostPrice = $productInventory->per_cost_price;
                $totalPrice = $currentProductQuantity * $perCostPrice;
                return response(['result'=>'Available','message'=>'','totalPrice'=>$totalPrice,'perCostPrice'=>$perCostPrice,
                    'perunitCostPrice'=>$perunitCostPrice,]);
            }else{
                $currentSku = Product::where('id',$currentProductId)->first()->productCurrentInventory;
                $availableQuantity = $currentSku->quantity;
//                return response(['result'=>'Not Available','message'=>$availableQuantity . ' this is Available in this Product SKU ID Your Entered Quantity is  Not Available ' . $currentProductQuantity,'totalPrice'=>0]);
                return response([
                    'result' => 'Not Available',
                    'message' => 'Available Quantity: ' . $availableQuantity . '</br> Entered Quantity: ' . $currentProductQuantity,
                    'totalPrice' => 0
                ]);
            }
        }
        return response()->json($product);
    }
    public function checkProductQuantityAvailabilityForRetain(Request $request){
        if (isset($request->allProducts) && $request->allProducts != null){
            $currentProductId = $request->selectedProducts[0]['id'];
            $currentProductSkuId = $request->selectedProducts[0]['skuId'];
            $currentProductQuantity = $request->selectedProducts[0]['quantity'];
            $currentDate = now();
            $productIds = [];
            $productSkyIds = [];
            foreach ($request->allProducts as $key => $item){
                $productIds[] = $item['productId'];
                $productSkyIds[] = $item['skuId'];
                if ($item['productId'] == $currentProductId && $item['skuId'] == $currentProductSkuId) {
                    $matchingEntries[] = $item;
                }
            }
            if(in_array($currentProductId,$productIds)){
                $currentDate = now();
                if (in_array($currentProductSkuId,$productSkyIds)){
                    $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                    $productInventoryQunatity = $productInventory->quantity;
                    $consumneQuantity = 0;
                    foreach ($matchingEntries as $entry) {
                        $consumneQuantity += $entry['quantity'];
                    }
                    $avalible = $productInventoryQunatity - $consumneQuantity;
                    if($avalible >= $currentProductQuantity){
                        $product = Product::find($currentProductId);
                        $perCostPrice = $productInventory->price;
                        $perunitCostPrice = $productInventory->per_cost_price;
                        $totalPrice = $currentProductQuantity * $perunitCostPrice;
                        $newQuantity = $avalible - $currentProductQuantity;
                        if($newQuantity == 0){
                            $newProductInventory = ProductInventory::where('product_id',$product->id)
                                ->where('is_deleted','0')
                                ->whereNotIn('sku_id',[$productInventory->sku_id])
                                ->where('expiry_date', '>=', $currentDate)
                                ->where('quantity','>',0)
                                ->orderBy('expiry_date', 'asc')
                                ->first();
                            if(isset($newProductInventory) && $newProductInventory != null){
                                return response([
                                    'result'     => 'Available',
                                    'message'    => '',
                                    'totalPrice' => $totalPrice,
                                    'product_id' => $product->id,
                                    'sku_id'     => $newProductInventory->sku_id,
                                ]);
                            }else{
                                return response([
                                    'result'     => 'Available',
                                    'message'    => '',
                                    'totalPrice' => $totalPrice,
                                    'product_id' => $product->id,
                                ]);
                            }
                        }else{
                            return response([
                                'result'     => 'Available',
                                'message'    => '',
                                'totalPrice' => $totalPrice,
                            ]);
                        }
                    }else{
                        $currentSku = Product::where('id', $currentProductId)
                            ->first()
                            ->productInventories()
                            ->where('sku_id', $currentProductSkuId)
                            ->first();
                        if ($currentSku) {
                            return response([
                                'result'     => 'Not Available',
                                'message'    => $avalible . ' units are available for this Product SKU ID, but you requested ' . $currentProductQuantity . ' units.',
                                'totalPrice' => 0,
                            ]);
                        } else {
                            return response([
                                'result' => 'Not Available',
                                'message' => 'This product does not have the SKU you entered.',
                                'totalPrice' => 0,
                            ]);
                        }
                    }
                }else{
                    $product = Product::find($currentProductId);
                    $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                    if ($product && $productInventory) {
                        $currentSku = $productInventory;
                        if ($currentSku->quantity >= $currentProductQuantity) {
                            $avalible = $productInventory->quantity;
                            $newQuantity = $avalible - $currentProductQuantity;
                            if($newQuantity == 0){
                                $perCostPrice = $currentSku->price;
                                $perunitCostPrice = $productInventory->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perunitCostPrice;
                                $newProductInventory = ProductInventory::where('product_id',$product->id)
                                    ->where('is_deleted','0')
                                    ->whereNotIn('sku_id',[$productInventory->sku_id])
                                    ->where('expiry_date', '>=', $currentDate)
                                    ->where('quantity','>',0)
                                    ->orderBy('expiry_date', 'asc')
                                    ->first();
                                if(isset($newProductInventory) && $newProductInventory != null){
                                    return response([
                                        'result' => 'Available',
                                        'message' => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                        'sku_id' => $newProductInventory->sku_id,
                                    ]);
                                }else{
                                    return response([
                                        'result' => 'Available',
                                        'message' => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                    ]);
                                }
                            }else{
                                $perCostPrice = $currentSku->price;
                                $perunitCostPrice = $productInventory->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perCostPrice;
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                ]);
                            }
                        }
                    }
                    $currentSku = Product::where('id', $currentProductId)
                        ->first()
                        ->productInventories()
                        ->where('sku_id', $currentProductSkuId)
                        ->first();
                    if ($currentSku) {
                        $availableQuantity = $currentSku->quantity;
                        return response([
                            'result' => 'Not Available',
                            'message' => $availableQuantity . ' units are available for this Product SKU ID, but you requested ' . $currentProductQuantity . ' units.',
                            'totalPrice' => 0,
                        ]);
                    } else {
                        return response([
                            'result' => 'Not Available',
                            'message' => 'This product does not have the SKU you entered.',
                            'totalPrice' => 0,
                        ]);
                    }
                }
            }else{
                $product = Product::whereHas('productInventories', function ($query) use ($currentProductSkuId, $currentProductQuantity) {
                    $query->where('sku_id', $currentProductSkuId)
                        ->where('quantity', '>=', $currentProductQuantity);
                })->with(['productInventories' => function($query) use ($currentProductSkuId, $currentProductQuantity) {
                    $query->where('sku_id', $currentProductSkuId)
                        ->where('quantity', '>=',$currentProductQuantity);
                }])->find($currentProductId);
                if ($product && $product->productInventories->isNotEmpty()) {
                    $product = Product::find($currentProductId);
                    $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                    if ($product && $productInventory) {
                        $currentSku = $productInventory;
                        if ($currentSku->quantity >= $currentProductQuantity) {
                            $avalible = $productInventory->quantity;
                            $newQuantity = $avalible - $currentProductQuantity;
                            if($newQuantity == 0){
                                $perCostPrice = $currentSku->price;
                                $perunitCostPrice = $productInventory->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perunitCostPrice;
                                $newProductInventory = ProductInventory::where('product_id',$product->id)
                                    ->where('is_deleted','0')
                                    ->whereNotIn('sku_id',[$productInventory->sku_id])
                                    ->where('expiry_date', '>=', $currentDate)
                                    ->where('quantity','>',0)
                                    ->orderBy('expiry_date', 'asc')
                                    ->first();
                                if(isset($newProductInventory) && $newProductInventory != null){
                                    return response([
                                        'result' => 'Available',
                                        'message' => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                        'sku_id' => $newProductInventory->sku_id,
                                    ]);
                                }else{
                                    return response([
                                        'result' => 'Available',
                                        'message' => '',
                                        'totalPrice' => $totalPrice,
                                        'product_id' => $product->id,
                                    ]);
                                }
                            }else{
                                $perCostPrice = $currentSku->price;
                                $perunitCostPrice = $productInventory->per_cost_price;
                                $totalPrice = $currentProductQuantity * $perunitCostPrice;
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                ]);
                            }
                        }
                    }
                    $currentSku = $product->productInventories->first();
                    if ($currentSku->quantity >= $currentProductQuantity) {
                        $perCostPrice = $currentSku->price;
                        $perunitCostPrice = $productInventory->per_cost_price;
                        $totalPrice = $currentProductQuantity * $perunitCostPrice;
                        return response([
                            'result' => 'Available',
                            'message' => '',
                            'totalPrice' => $totalPrice,
                        ]);
                    }
                }
                $currentSku = Product::where('id', $currentProductId)
                    ->first()
                    ->productInventories()
                    ->where('sku_id', $currentProductSkuId)
                    ->first();
                if ($currentSku) {
                    $availableQuantity = $currentSku->quantity;
                    return response([
                        'result' => 'Not Available',
                        'message' => $availableQuantity . ' units are available for this Product SKU ID, but you requested ' . $currentProductQuantity . ' units.',
                        'totalPrice' => 0,
                    ]);
                } else {
                    return response([
                        'result' => 'Not Available',
                        'message' => 'This product does not have the SKU you entered.',
                        'totalPrice' => 0,
                    ]);
                }
            }
        }else{
            $currentProductId       = $request->selectedProducts[0]['id'];
            $currentProductSkuId    = $request->selectedProducts[0]['skuId'];
            $currentProductQuantity = $request->selectedProducts[0]['quantity'];
            $currentDate            = now();
            $product = Product::whereHas('productInventories', function ($query) use ($currentProductSkuId, $currentProductQuantity) {
                $query->where('sku_id', $currentProductSkuId)
                    ->where('quantity', '>=', $currentProductQuantity);
            })->with(['productInventories' => function($query) use ($currentProductSkuId,$currentProductQuantity) {
                $query->where('sku_id', $currentProductSkuId)
                    ->where('quantity', '>=', $currentProductQuantity);
            }])->find($currentProductId);
            if (isset($product->productInventories) && $product->productInventories != null){
                $product = Product::find($currentProductId);
                $productInventory = ProductInventory::where('sku_id',$currentProductSkuId)->where('is_deleted','0')->first();
                if ($product && $productInventory) {
                    $currentSku = $productInventory;
                    if ($currentSku->quantity >= $currentProductQuantity) {
                        $avalible = $productInventory->quantity;
                        $newQuantity = $avalible - $currentProductQuantity;
                        if($newQuantity == 0){
                            $perCostPrice = $currentSku->price;
                            $perunitCostPrice = $productInventory->per_cost_price;
                            $totalPrice = $currentProductQuantity * $perunitCostPrice;
                            $newProductInventory = ProductInventory::where('product_id',$product->id)
                                ->where('is_deleted','0')
                                ->whereNotIn('sku_id',[$productInventory->sku_id])
                                ->where('expiry_date', '>=', $currentDate)
                                ->where('quantity','>',0)
                                ->orderBy('expiry_date', 'asc')
                                ->first();
                            if(isset($newProductInventory) && $newProductInventory != null){
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                    'product_id' => $product->id,
                                    'sku_id' => $newProductInventory->sku_id,
                                ]);
                            }else{
                                return response([
                                    'result' => 'Available',
                                    'message' => '',
                                    'totalPrice' => $totalPrice,
                                    'product_id' => $product->id,
                                ]);
                            }
                        }else{
                            $perCostPrice = $currentSku->price;
                            $perunitCostPrice = $productInventory->per_cost_price;
                            $totalPrice = $currentProductQuantity * $perunitCostPrice;
                            return response([
                                'result' => 'Available',
                                'message' => '',
                                'totalPrice' => $totalPrice,
                            ]);
                        }
                    }
                }
                $currentSku = $product->productInventories->first();
                $perCostPrice = $currentSku->price;
                $perunitCostPrice = $productInventory->per_cost_price;
                $totalPrice = $currentProductQuantity * $perunitCostPrice;
                return response(['result'=>'Available','message'=>'','totalPrice'=>$totalPrice]);
            }else{
                $currentSku = Product::where('id',$currentProductId)->first()->productCurrentInventory;
                $availableQuantity = $currentSku->quantity;
//                return response(['result'=>'Not Available','message'=>$availableQuantity . ' this is Available in this Product SKU ID Your Entered Quantity is  Not Available ' . $currentProductQuantity,'totalPrice'=>0]);
                return response([
                    'result' => 'Not Available',
                    'message' => 'Available Quantity: ' . $availableQuantity . '</br> Entered Quantity: ' . $currentProductQuantity,
                    'totalPrice' => 0
                ]);
            }
        }
        return response()->json($product);
    }
//    Useless Function
    public function onlineAppointment(Request $request)
    {
        extract($request->all());
        $user = User::where('email', $email)->orWhere('phone', $phone)->first();
        if ($user != null) {
            $profile = Profile::where('user_id', $user->id)->orWhere('phone', $phone)->first();
        }
        $profile = "";
        $salonLink = "";
        if ($user == null) {
            if ($profile == null) {
                $phone    = $request->phone;
                $password = rand('11111111', '99999999');
                $user     = User::create(['first_name' => $first_name, 'last_name' => $last_name, 'name' => $first_name . ' ' . $last_name, 'email' => $email,
                            'password' => bcrypt($password), 'customer_type_id' => 2, 'phone' => $phone, 'show_password' => $password]);
                $profile  = Profile::create([
                    'user_id'   => $user->id,
                    'dob'       => $dob,
                    'phone'     => $request->phone,
                    'address'   => $address,
                    'pic'       => 'no_avatar.jpg',
                    'state'     => isset($state) ? $state : null,
                    'city'      => isset($city) ? $city : null,
                    'country'   => isset($country) ? $country : null,
                    'latitude'  => isset($lat) ? $lat : null,
                    'longitude' => isset($lng) ? $lng : null,
                    'postal'    => isset($zip_code) ? $zip_code : null
                ]);
                $user->roles()->attach([1 => ['role_id' => 5, 'user_id' => $user->id]]);
                if ($user != null) {
                    $appointment = CustomerAppointment::create(['customer_id' => $user->id, 'customer_appointment_date' => $customer_appointment_date, 'customer_slot_id' => json_encode($customer_slot_id), 'status' => 'Pending', 'salon_id' => $salon_id]);
                    if (is_array($request->customer_service_id)) {
                        foreach ($request->customer_service_id as $value) {
                            $CustomerService = CustomerService::create(['customer_id' => $user->id, 'salon_service_id' => $value, 'appointment_id' => $appointment->id]);
                        }
                    }
                    if (is_array($request->category_id)) {
                        foreach ($request->category_id as $value) {
                            CustomerServiceCategory::create(['customer_service_id' => $CustomerService->id, 'service_category_id' => $value, 'appointment_id' => $appointment->id]);
                        }
                    }
//                    if (is_array($request->customer_product_id)) {
//                        foreach ($request->customer_product_id as $value) {
//                            $customerProduct = CustomerProduct::create(['customer_id' => $user->id, 'salon_product_id' => $value, 'appointment_id' => $appointment->id]);
//                        }
//                    }
//                    if (is_array($request->product_category_id)) {
//                        foreach ($request->product_category_id as $value) {
//                            CustomerProductCategory::create(['customer_product_id' => $customerProduct->id, 'product_category_id' => $value, 'appointment_id' => $appointment->id]);
//                        }
//                    }
                    if (!empty($request->employee_id)) {
                        $assigned = AssignedCustomer::create(['salon_id' => $salon_id, 'appointment_id' => $appointment->id, 'customer_id' => $user->id, 'assigned_user_id' => Auth::id(), 'employee_id' => $employee_id]);
                    }
                    $approved = User::findOrFail($salon_id);
                    if (isset($approved->appointment_type_id) && $approved->appointment_type_id == 1) {
                        CustomerAppointment::where('id', $appointment->id)->update(['status' => "Approved"]);
                    }
                    if (isset($request->discount_number) && $request->discount_number != null) {
                        $live_date = Carbon::now();
                        $formatted_date = $live_date->format('Y-m-d');
                        $discounts = Discount::where('discount_number', $request->discount_number)
                            ->where('status', 1)
                            ->where('salon_id', $appointment->salon_id)
                            ->whereRaw('? BETWEEN validity_date_to AND validity_date_from', [$formatted_date])
                            ->first();
                        if ($discounts != null) {
                            $qty = $discounts->quantity;
                            $used_qty = $discounts->quantity_count;
                            if ($qty > $used_qty) {
                                if ($used_qty == null) {
                                    $new_qty = 1;
                                } else {
                                    $new_qty = $used_qty + 1;
                                }

                                Discount::where('id', $discounts->id)->update(['quantity_count' => $new_qty]);
                                CustomerAppointment::where('id', $appointment->id)->update(['discount_id' => $discounts->id]);
                            } else {

                            }
                        }
                    }
                } else {

                }
            } else {
            }
        } else {
            $appointment = CustomerAppointment::create(['customer_id' => $user->id, 'customer_appointment_date' => $customer_appointment_date, 'customer_slot_id' => json_encode($customer_slot_id), 'status' => 'Pending', 'salon_id' => $salon_id]);
            if (is_array($request->customer_service_id)) {
                foreach ($request->customer_service_id as $value) {
                    $CustomerService = CustomerService::create(['customer_id' => $user->id, 'salon_service_id' => $value, 'appointment_id' => $appointment->id]);
                }
            }
            if (is_array($request->category_id)) {
                foreach ($request->category_id as $value) {
                    CustomerServiceCategory::create(['customer_service_id' => $CustomerService->id, 'service_category_id' => $value, 'appointment_id' => $appointment->id]);
                }
            }
//            if (is_array($request->customer_product_id)) {
//                foreach ($request->customer_product_id as $value) {
//                    $CustomerProduct = CustomerProduct::create(['customer_id' => $user->id, 'salon_product_id' => $value, 'appointment_id' => $appointment->id]);
//                }
//            }
//            if (is_array($request->product_category_id)) {
//                foreach ($request->product_category_id as $value) {
//                    CustomerProductCategory::create(['customer_product_id' => $CustomerProduct->id, 'product_category_id' => $value, 'appointment_id' => $appointment->id]);
//                }
//            }
            if (isset($request->employee_id)) {
                $assigned = AssignedCustomer::create(['salon_id' => $salon_id, 'appointment_id' => $appointment->id, 'customer_id' => $user->id, 'assigned_user_id' => Auth::id(), 'employee_id' => $employee_id]);
            }
            $approved = User::findOrFail($salon_id);
            if (isset($approved->appointment_type_id)) {
                if ($approved->appointment_type_id == 1) {
                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Approved']);
                } elseif ($approved->appointment_type_id == 2) {
                    CustomerAppointment::where('id', $appointment->id)->update(['status' => 'Pending']);
                }
            }
            if (isset($request->discount_number) && $request->discount_number != null) {
                $live_date = Carbon::now();
                $formatted_date = $live_date->format('Y-m-d');
                $discounts = Discount::where('discount_number', $request->discount_number)
                    ->where('status', 1)
                    ->where('salon_id', $appointment->salon_id)
                    ->whereRaw('? BETWEEN validity_date_to AND validity_date_from', [$formatted_date])
                    ->first();
                if ($discounts != null) {
                    $qty = $discounts->quantity;
                    $used_qty = $discounts->quantity_count;
                    if ($qty > $used_qty) {
                        if ($used_qty == null) {
                            $new_qty = 1;
                        } else {
                            $new_qty = $used_qty + 1;
                        }
                        Discount::where('id', $discounts->id)->update(['quantity_count' => $new_qty]);
                        CustomerAppointment::where('id', $appointment->id)->update(['discount_id' => $discounts->id]);
                    } else {

                    }
                }
            }
        }
        if (isset($request->employee_id)) {
            try {
                $salon = User::findOrFail($salon_id);
                $salon_picture = $salon->profile->pic;
                $salon_name = $salon->name;
                $employee = $assigned->employee->name;
                $data = array(
                    'name'                => $first_name . ' ' . $last_name,
                    'email'               => $email,
                    'password'            => $password,
                    'employee'            => $employee,
                    'salon_picture'       => $salon_picture,
                    'salon_name'          => $salon_name,
                    'welcome_message'     => 'Welcome',
                    'information_message' => 'Account Registration Successful',
                    'detail'              => env('APP_URL'),
                    'login_url'           => env('APP_URL'),
                    'site_url'            => env('APP_URL'),
                );
                Mail::send('website.email_templates.appointment_walkin_customer_email', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Aftab Ali')->subject('Registration Successful');;
                });
            } catch (\Exception $e) {}     //end try catch.
        } else {
            try {
                $salon = User::findOrFail($salon_id);
                $salon_picture = $salon->profile->pic;
                $salon_name = $salon->name;
                $salonLink = $salon->profile->link;
                $data = array(
                    'name'  => $first_name . ' ' . $last_name,
                    'email' => $email,
                    'password' => $password,
                    'salon_picture' => $salon_picture,
                    'salon_name' => $salon_name,
                    'welcome_message' => 'Welcome',
                    'information_message' => 'Account Registration Successful',
                    'detail' => env('APP_URL'),
                    'login_url' => env('APP_URL'),
                    'site_url' => env('APP_URL'),
                );
                Mail::send('website.email_templates.registration_employee_email', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Aftab Ali')->subject('Registration Successful');;
                });
            } catch (\Exception $e) {} //end try catch.
        }
        if (Session::has('google_registered_user')) {
            $google_registered_user = Session::forget('google_registered_user'); // destroy session
        } else {
            $google_registered_user = [];
        }
        return redirect(url($salonLink))->with('flash_message',trans('messages.YourAppointmentBooked'));
    }
    public function updateStatus($id = null, $status = null)
    {
        if (CustomerAppointment::where('id', $id)->update(['status' => $status])) {
            try {
                $appointments  = CustomerAppointment::find($id);
                $customer      = $appointments->customer->name;
                $customerEmail = $appointments->customer->email;
                $status        = ucwords($status);
                $result        = Mail::raw("Hello $customer,
                Your appointments request has been $status.
                Thanks.
            ", function ($message) use ($customerEmail) {
                    $message->to($customerEmail)->cc('<EMAIL>')->subject('Appointment Status!');
                });
                return redirect()->back()->with(['message' => trans('messages.Statusupdatedsuccessfully'), 'type' => 'success', 'title' => 'Success']);
            } catch (\Exception $e) {
                return redirect()->back()->with(['message' => trans('messages.Status updated successfully, but unable to send email to patient,') . $e->getMessage(), 'type' => 'error', 'title' => 'Fail']);
            }
        } else {
            return redirect()->back()->with(['title' => 'Fail', 'message' => trans('messages.Status Not Update, try again'), 'type' => 'error']);
        }
        return redirect()->back()->with(['title' => 'Done', 'message' => trans('messages.UpdatedStatusInformation'), 'type' => 'success']);
    }
    public function customerAppointmentComplete(Request $request){
        $customerAppointment = CustomerAppointment::where('id',$request->id)->first();
        $customerAppointment->update(['payment_preference'=>$request->payment_preference,'status'=>'Complete']);
        try {
            $invoice_number = rand(111111111,999999999);
            $invoice_issue_date = date('Y-m-d');
            $date_of_supply = date('Y-m-d');
            $requestData = (['appointment_id'=>$customerAppointment->id,'invoice_number'=>$invoice_number,'invoice_issue_date'=>$invoice_issue_date,'date_of_supply'=> $date_of_supply]);
            $fatora = CustomerFatora::create($requestData);
            $customerFatora = $customerAppointment;
            if (Auth::user()->roles[0]->name == "cashier") {
                $products = Product::where('salon_id', Auth::user()->salon_id)
                    ->where('product_type_id', 1)
                    ->get();
                $services = SalonService::where('salon_id', Auth::user()->salon_id)->get();
            } else {
                $products = Product::where('salon_id', Auth::user()->salon_id)
                    ->where('product_type_id', 1)
                    ->get();
                $services = SalonService::where('salon_id', Auth::user()->salon_id)->get();
            }
            $admin = User::findOrFail(2);
            $vat = $admin->profile->vat;
            $customerFatoraData = json_encode($customerFatora);
            $encodedCustomerFatoraData = urlencode($customerFatoraData);
            $qrCodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?data=' . $encodedCustomerFatoraData . '&size=300x300';
            $qrCodeImageData = file_get_contents($qrCodeUrl);
            $fileName = 'qr_code_image.png';
            Storage::disk('website')->put('qr-codes/' . $customerFatora->id . '/' . $fileName, $qrCodeImageData);
            if (isset($customerFatora->appointmentPurchaseOrder) && $customerFatora->appointmentPurchaseOrder->appointment_id == $customerFatora->id) {
                $fatoraUrl = url('stockOut_fatora/' . $customerFatora->appointmentPurchaseOrder->id);
            } elseif (isset($customerFatora->customerAppointmentFatora) && $customerFatora->customerAppointmentFatora != null) {
                $fatoraUrl = url('customer_fatora/' . $customerFatora->id);
            } else {
                $fatoraUrl = url('customer_fatora/' . $customerFatora->id);
            }
            $data = [
                'customerName'     => $customerFatora->customer->name,
                'salonPicture'     => $customerFatora->salonId->profile->pic,
                'customerEmail'    => $customerFatora->customer->email,
                'salonName'        => $customerFatora->salonId->name,
                'salonEmail'       => $customerFatora->salonId->email,
                'salonPhone'       => $customerFatora->salonId->profile->phone,
                'appointment_date' => date("d/m/y", strtotime($customerFatora->customer_appointment_date)),
                'id'               => $customerFatora->id,
                'fatoraUrl'        => $fatoraUrl,
            ];
            $notifyData = [
                'customerName' => $customerFatora->customer->name,
                'salonName'    => $customerFatora->salonId->name,
                'fatoraUrl'    => $fatoraUrl,
                'type'         => 'appointmentComplete'
            ];
            Mail::send('website.email_templates.review_customer_fatora', ['data' => $data], function ($message) use ($data) {
                $message->to($data['customerEmail'], $data['customerEmail'])
                    ->bcc('<EMAIL>')
                    ->subject('Thank You for Choosing ' . $data['salonName'] . '!');
            });
            $custom = CustomNotification::create(
                [
                    'notifiable_id'   =>  $customerAppointment->salon_id,
                    'notifiable_type' => 'App\User',
                    'type'            => 'Appointment',
                    'data'            => $notifyData,
                ]
            );
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'message' => trans('messages.Status updated successfully, but unable to send email to customer,') . $e->getMessage(),
                'type'    => 'error',
                'title'   => 'Fail'
            ]);
        }
        if (Auth::user()->roles[0]->name == "cashier") {
            return redirect(url('dashboard#completed_tab_button'))->with([
                'invoice_flash_message' => trans('messages.Appointment completed. Would you like to issue the invoice now?'),
                'invoice_url' => url('').'/customer_fatora/' . $customerAppointment->id,
            ]);
        }else{
            return redirect(url('customerAppointment/customer-appointment?status=Completed'))->with([
                'invoice_flash_message' => trans('messages.Appointment completed. Would you like to issue the invoice now?'),
                'invoice_url' => url('').'/customer_fatora/' . $customerAppointment->id,
            ]);
        }
    }
    public function retainSupplierProducts(Request $request){
        $currentDate = now();
        $supplierProductInventories = ProductInventory::where('supplier_id',$request->id)
            ->where('is_deleted','0')
            ->where('expiry_date', '>=', $currentDate)
            ->where('quantity','>',0);
        $porductIds   = $supplierProductInventories->pluck('product_id')->unique();
        if (!empty($porductIds)){
            $products = Product::whereIn('id',$porductIds)->get();
            $html     = view('website.ajax.retain_supplier_products', compact('products'))->render();
            return response(['status'=>true,'result'=>$html]);
        }else{
            return response(['status'=>false,'result'=>'']);
        }
    }
    public function retainSupplierProductsSkuIds(Request $request){
        $currentDate = now();
        $supplierProductInventories = ProductInventory::where('supplier_id',$request->supplierId)
            ->where('is_deleted','0')
            ->where('product_id',$request->id)
            ->where('expiry_date', '>=', $currentDate)
            ->where('quantity','>',0)
            ->get();
        if (!empty($supplierProductInventories)){
            $porductSkuIds = $supplierProductInventories;
            $html = view('website.ajax.retain_supplier_products', compact('porductSkuIds'))->render();
            return response(['status'=>true,'result'=>$html]);
        }else{
            return response(['status'=>false,'result'=>'']);
        }
    }
    public function addNewCustomerTab(Request $request){
        $salon = User::findOrFail($request->salonId);
        $services = SalonService::where('category_id', $request->serviceCategoryid)->whereIn('id', $salon->allEmployeeServiceIds)->get();
        $products = Product::where('product_type_id', '2')->where('product_category_id', $request->productCategoryid)->where('salon_id',Auth::user()->salon_id)->get();
        $vat = Profile::where('user_id', '2')->first()->vat;
        $userSubscription = UserSubscription::where('user_id', $salon->id)->orderBy('id', 'DESC')->first();
        return view('website.ajax.add_new_customer_tab_ajax', compact('services', 'vat','products','salon','userSubscription'));
    }
    public function selectedServiceCategoryTab(Request $request){
        $salon = User::findOrFail($request->salonId);
        $services = SalonService::where('category_id', $request->serviceCategoryid)->whereIn('id', $salon->allEmployeeServiceIds)->get();
        $vat = Profile::where('user_id', '2')->first()->vat;
        $userSubscription = UserSubscription::where('user_id', $salon->id)->orderBy('id', 'DESC')->first();
        return view('website.ajax.selected_service_category_tab_ajax', compact('services', 'vat','salon','userSubscription'));
    }
    public function productAssignEmployee(Request $request){
        $product   = Product::where('product_type_id','1')->where('salon_id',Auth::user()->salon_id)->where('id',$request->id)->first();
        $salon     = User::findOrFail($product->salon_id);
        $employees = User::whereHas('roles', function ($query) {
            $query->where('name', 'employee');
        })->where('salon_id',Auth::user()->id)->get();
        $productInventories = ProductInventory::where('product_id', $product->id)
            ->where('is_deleted','0')
            ->where(function ($query) {
                $query->whereNull('expiry_date')
                ->orWhere('expiry_date', '>=', now());
            })
            ->where(function ($query) {
                $query->whereNull('depreciation_date')
                ->orWhere('depreciation_date', '>=', now());
            })
            ->where('quantity', '>', 0)
            ->get();
        return view('website.ajax.product_assign_employee_modal', compact('product', 'salon','employees','productInventories'));
    }
    public function checkSkuAssignEquipmentQuantity(Request $request)
    {
        $productInventory = ProductInventory::where('product_id', $request->product_id)
            ->where('is_deleted','0')
            ->where('id', $request->id)
            ->where('sku_id', $request->val)
            ->where(function ($query) {
                $query->whereNull('expiry_date')
                    ->orWhere('expiry_date', '>=', now());
            })
            ->where(function ($query) {
                $query->whereNull('depreciation_date')
                    ->orWhere('depreciation_date', '>=', now());
            })
            ->where('quantity', '>', 0)
            ->first();
        if (!$productInventory) {
            return response()->json(['message' => 'No SKU found matching the criteria'], 404);
        }
        return response()->json([
            'message' => 'This SKU EXISTS with ' . $productInventory->quantity . ' Units'
        ]);
    }
    public function selectedProductCategoryTab(Request $request){
        $salon = User::findOrFail($request->salonId);
        $products = Product::where('product_type_id','2')->where('product_category_id', $request->productCategoryid)->where('salon_id',Auth::user()->salon_id)->get();
        $vat = Profile::where('user_id', '2')->first()->vat;
        $userSubscription = UserSubscription::where('user_id', $salon->id)->orderBy('id', 'DESC')->first();
        return view('website.ajax.selected_product_category_tab_ajax', compact('products', 'vat','salon','userSubscription'));
    }
    public function automaticCancelUnapprovedAppointment(Request $request)
    {
        $liveDate = now()->format('Y-m-d');
        $currentTime = now();
        $currentTimeFormatted = $currentTime->format('h:i A');
        $timeLimit = $currentTime->addMinutes(15);
        $todayAppointments = CustomerAppointment::with('customerSlot')
            ->whereRaw("STR_TO_DATE(customer_appointment_date, '%m/%d/%Y') <= ?", [now()->format('Y-m-d')])
            ->where('status', 'Pending')
            ->get();
        $dd = [];
        foreach ($todayAppointments as $appointment) {
            try {
                $firstCustomerSlotId = $appointment->firstCustomerSlotId;
                if ($firstCustomerSlotId) {
                    $slot = Slot::find($firstCustomerSlotId);
                    $scheduledTime = Carbon::createFromFormat('h:i A', $slot->start_time, 'UTC')
                        ->setTimezone('Asia/Riyadh');
                    if ($scheduledTime->lessThan($timeLimit) && $scheduledTime->greaterThan($currentTime)) {
                        $appointment->update(['status' => 'Cancel']);
                        $servicesIds = $appointment->getCustomerServices ? $appointment->getCustomerServices->pluck('salon_service_id')->toArray() : [];
                        $services = SalonService::whereIn('id', $servicesIds)->pluck('name')->toArray();
                        $data = [
                            'status' => 'Cancel',
                            'appointmentId' => $appointment->id,
                            'customerName' => $appointment->customer->name,
                            'customerEmail' => $appointment->customer->email,
                            'salonId' => $appointment->salon_id,
                            'salonName' => $appointment->salon->name,
                            'salonEmail' => $appointment->salon->email,
                            'salonPic' => $appointment->salon->profile->pic,
                            'salonPhone' => $appointment->salon->profile->phone,
                            'description' => $appointment->cancelation_description,
                            'date' => $appointment->customer_appointment_date ? date('d M Y', strtotime($appointment->customer_appointment_date)) : '',
                            'time' => ($appointment->startSlot->start_time ?? '') . ' - ' . ($appointment->endSlot->end_time ?? ''),
                            'site_url' => env('APP_URL'),
                            'site_contact' => env('APP_URL_CONTACT'),
                            'services' => implode(', ', $services),
                        ];
                        Mail::send('website.email_templates.appointment_cancel', ['data' => $data], function ($message) use ($data) {
                            $message->to($data['customerEmail'], $data['customerName'])
                                ->cc('<EMAIL>', 'Dev')
                                ->subject('Appointment Cancellation Notice.');
                        });
                    }
                }
            } catch (\Exception $e) {
                echo '<pre>';
                echo 'Error while processing appointment ID ' . $appointment->id . ': ' . $e->getMessage();
                echo '</pre>';
                continue;
            }
        }
        return response()->json(['message' => 'Appointments auto-canceled if unapproved.']);
    }
    public function appointmentSoftReminder(Request $request)
    {
        $now = Carbon::now();
        $todayAppointments = CustomerAppointment::with('customerSlot')
            ->whereRaw("STR_TO_DATE(customer_appointment_date, '%m/%d/%Y') = ?", [$now->format('m/d/Y')])
            ->where('status', 'Approved')
            ->where('is_reminder', '0')
            ->get();
        foreach ($todayAppointments as $appointment) {
            try {
                $firstCustomerSlotId = $appointment->firstCustomerSlotId;
                if ($firstCustomerSlotId) {
                    $slot = Slot::find($firstCustomerSlotId);
                    if ($slot) {
                        $scheduledTime = Carbon::createFromFormat('h:i A', $slot->start_time);
                        $reminderTime = $scheduledTime->copy()->subHour();
                        if ($now->gte($reminderTime) && $now->lt($scheduledTime)) {
                            $appointment->update(['is_reminder' => 1]);
                            $servicesIds = $appointment->getCustomerServices ? $appointment->getCustomerServices->pluck('salon_service_id')->toArray() : [];
                            $services = SalonService::whereIn('id', $servicesIds)->pluck('name')->toArray();
                            $data = [
                                'status' => $appointment->status,
                                'appointmentId' => $appointment->id,
                                'customerName' => $appointment->customer->name,
                                'customerEmail' => $appointment->customer->email,
                                'salonId' => $appointment->salon_id,
                                'salonName' => $appointment->salon->name,
                                'salonEmail' => $appointment->salon->email,
                                'salonPic' => $appointment->salon->profile->pic,
                                'salonPhone' => $appointment->salon->profile->phone,
                                'description' => $appointment->cancelation_description,
                                'date' => $appointment->customer_appointment_date ? date('d M Y', strtotime($appointment->customer_appointment_date)) : '',
                                'time' => ($appointment->startSlot->start_time ?? '') . ' - ' . ($appointment->endSlot->end_time ?? ''),
                                'site_url' => env('APP_URL'),
                                'site_contact' => env('APP_URL_CONTACT'),
                                'services' => implode(', ', $services),
                            ];
                            Mail::queue('website.email_templates.appointment_reminder', ['data' => $data], function ($message) use ($data) {
                                $message->to($data['customerEmail'], $data['customerName'])
                                    ->cc('<EMAIL>', 'Dev')
                                    ->subject('Appointment Reminder.');
                            });
                        }
                    }
                }
            } catch (\Exception $e) {
                \Log::error('Error while processing appointment ID ' . $appointment->id . ': ' . $e->getMessage());
            }
        }
        return response()->json(['message' => 'Appointments Reminder Sent.']);
    }
    public function adminDailyPendingAppointmentEmail(Request $request)
    {
        $liveDate = now()->format('m/d/Y');
        $admin = User::findOrFail(2);
        $salons = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_setting_updated', 1)
            ->where('register_status', "Accepted")
            ->get();
        foreach ($salons as $item) {
            try {
                $customerAppointment = CustomerAppointment::where('salon_id', $item->id)
                    ->where('customer_appointment_date', $liveDate)
                    ->whereIn('status', ['Pending','Cancel'])
                    ->get();
                $data = [
                    'shopName'    => $item->name,
                    'shopEmail'    => $item->email,
                    'shopPicture'  => $item->profile->pic,
                    'appointments' => $customerAppointment,
                    'dashboardUrl' => env('APP_URL') . '/customerAppointment/customer-appointment?status=Current',
                ];
                $custom = CustomNotification(
                    [
                        'notifiable_id' => $admin->id,
                        'notifiable_type' => 'App\Models\User',
                        'type' => 'customerAppointment',
                        'data' => $data,
                    ]
                );
                Mail::send('website.email_templates.admin_pending_appointment_list', ['data' => $data], function ($message) use ($data) {
                    $message->to($data['shopEmail'], $data['shopName'])
                        ->cc('<EMAIL>', 'Dev')
                        ->subject('Daily Pending Appointments.');
                });
            } catch (\Exception $e) {
                echo '<pre>';
                echo 'Error sending daily pending appointments email for salon: ' . $item->name . '<br>';
                echo 'Salon ID: ' . $item->id . '<br>';
                echo 'Error Message: ' . $e->getMessage() . '<br>';
                echo 'Stack Trace: ' . $e->getTraceAsString() . '<br>';
                echo '</pre>';
                continue;
            }
        }
        return response()->json(['message' => 'Daily Pending Appointments Emails Sent.']);
    }
    public function viewProductInventoryData(Request $request){
        $productInventory = ProductInventory::findOrFail($request->id);
        return view('website.ajax.view_product_inventory_detail', compact('productInventory'));
    }
    public function ajaxSearchForProducts(Request $request){
        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
        $branchIds = $allBranches->pluck('id');
        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
            ->orderBy('user_id', 'ASC')
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy('user_id');
        $currentDate = now();
        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
            $dueDate = $subs->first()->fatoraPdf->due_date;
            return $dueDate->isPast();
        })->keys();
        $query = $request->input('query');
        $products = Product::where('product_type_id', '2')
            ->whereIn('salon_id', $branchIds)
            ->whereNotIn('salon_id', $expiredUserIds)
            ->where('title', 'LIKE', '%' . $query . '%')
            ->get()
            ->filter(function ($product) {
                // Check if the product has a valid current inventory using the getter
                return $product->productCurrentInventory !== null;
            });
        return response()->json($products);
    }
    public function supplierCreateProductScreen(Request $request){
        extract($request->all());
        $supplier = Supplier::create([
            "name"     => $name??"",
            "email"    => $email??"",
            "address"  => $address??"",
            "phone"    => $phone??"",
            "salon_id" => $request->salon_id,
            "web_link" => $web_link??"",
        ]);
        return response()->json(['status'=>'true','name'=>$supplier->name,'id'=>$supplier->id]);
    }
    public function brandCreateProductScreen(Request $request){
        extract($request->all());
        $product = ProductBrand::create([
            "name" => $name??""
        ]);
        return response()->json(['status'=>'true','name'=>$product->name,'id'=> $product->id]);
    }

    public function changeOrder(Request $request)
    {
        $positions = $request->get('positions');

        if (!empty($positions)) {
            foreach ($positions as $position => $id) {
                $index = $position + 1;
                Testimonial::where('id', $id)
                    ->update(['position' => $index]);
            }
            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false]);
    }


//    public function markTypeAsRead(Request $request, $type) // dont remove its for notifications
//    {
//        $count = CustomNotification::where('notifiable_id', auth()->id())
//            ->where('type', $type)
//            ->whereNull('read_at')
//            ->count();
//        CustomNotification::where('notifiable_id', auth()->id())
//            ->where('type', $type)
//            ->whereNull('read_at')
//            ->update(['read_at' => now()]);
//
//        return response()->json([
//            'success' => true,
//            'message' => 'All notifications of type ' . $type . ' marked as read',
//            'count' => $count
//        ]);
//    }
//
//    public function markAllAsRead(Request $request)
//    {
//        CustomNotification::where('notifiable_id', auth()->id())
//            ->whereNull('read_at')
//            ->update(['read_at' => now()]);
//
//        return response()->json([
//            'success' => true,
//            'message' => 'All notifications marked as read'
//        ]);
//    }



        public function checkCertificateExpiry()
    {
        $currentDate       = Carbon::now()->format('Y-m-d');
        $userIdVatExpire   = [];
        $userIdTradeExpire = [];
        $admin             = User::findOrFail(2);


        // Get all salons with profiles
        $salons = User::whereNull('salon_id')
            ->whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })
            ->whereHas('profile')
            ->get();

        foreach ($salons as $salon) {
            // Check VAT certificate expiry
            if ($salon->profile->vat_certification_expiry_date) {
                $vatExpiry = Carbon::parse($salon->profile->vat_certification_expiry_date);
                if ($vatExpiry->format('Y-m-d') === $currentDate) {
                    $userIdVatExpire[] = $salon->id;

                    // Create notification for VAT expiry
                    CustomNotification::create([
                        'notifiable_id'   => $admin->id,
                        'notifiable_type' => 'App\User',
                        'type'            => 'vat_certificate_expiry',
                        'data' => [
                            'name'                            => $salon->name,
                            'vat_certification_expiry_date'   => $salon->profile->vat_certification_expiry_date,
                            'trade_certification_expiry_date' => $salon->profile->trade_certification_expiry_date,
                            'type'                            => 'ExpiryDate',
                        ],
                    ]);
                }
            }

            // Check Trade certificate expiry
            if ($salon->profile->trade_certification_expiry_date) {
                $tradeExpiry = Carbon::parse($salon->profile->trade_certification_expiry_date);
                if ($tradeExpiry->format('Y-m-d') === $currentDate) {
                    $userIdTradeExpire[] = $salon->id;

                    // Create notification for Trade expiry
                    CustomNotification::create([
                        'notifiable_id'   => $admin->id,
                        'notifiable_type' => 'App\User',
                        'type'            => 'trade_certificate_expiry',
                        'data' => [
                            'name'                            => $salon->name,
                            'vat_certification_expiry_date'   => $salon->profile->vat_certification_expiry_date,
                            'trade_certification_expiry_date' => $salon->profile->trade_certification_expiry_date,
                            'type'                            => 'ExpiryDate',
                        ],
                    ]);
                }
            }
        }

        return response()->json([
            'success'  => true,
            'vatIds'   => $userIdVatExpire,
            'tradeIds' => $userIdTradeExpire,
        ]);
    }
    public function customerBanned($id){
        $user = User::where('id',$id)->first();
        if ($user->register_status == 'Banned'){
            $user->update(['register_status'=> Null]);
            return redirect()->back()->with(['title' => 'Done', 'message' => trans('messages.customerUnbannedSuccessfully'), 'type' => 'success']);
        }else{
            $user->update(['register_status'=> 'Banned']);
            return redirect()->back()->with(['title' => 'Done', 'message' => trans('messages.customerBannedSuccessfully'), 'type' => 'success']);
        }
    }


    public function markAsRead(Request $request)
    {
        $id = $request->input('id');
        $success = false;

        if ($id) {
            $user = Auth::user();
            $customNotification = $user->customNotifications()->where('id', $id)->first();

            if ($customNotification) {
                $customNotification->markAsRead();
                $success = true;
            }
        }

        return response()->json(['success' => $success]);
    }
}
