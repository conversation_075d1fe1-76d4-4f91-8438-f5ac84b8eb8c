<?php

namespace App\Http\Controllers\Feedback;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Feedback;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FeedbackController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('feedback','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 250000000;

            if (!empty($keyword)) {
                $feedback = Feedback::where('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('question', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $feedback = Feedback::where('salon_id',Auth::id())->paginate($perPage);
                $salonFeedback = $feedback->pluck('salon_id')->count();
            }
            return view('feedback.feedback.index', compact('feedback','salonFeedback'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('feedback','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('feedback.feedback.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        extract($request->all());
        $model = str_slug('feedback','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            $branches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
            if (isset($branches)){
                foreach ($branches as $key => $branch){
                    Feedback::create(['salon_id'=>$branch->id,'question'=>$question,]);
                }
            }

            return redirect('feedback/feedback')->with('flash_message', 'Feedback added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('feedback','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $feedback = Feedback::where('id',$id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
            return view('feedback.feedback.show', compact('feedback'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('feedback','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $feedback = Feedback::where('id',$id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
            return view('feedback.feedback.edit', compact('feedback'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        extract($request->all());
        $model = str_slug('feedback','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $branches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)
                ->orWhere('id', Auth::user()->salon_id)
                ->orderBy('id','ASC')
                ->get();
            $feedback = Feedback::findOrFail($id);
            $feedbacks = Feedback::where('question', $feedback->question)->where('created_at',$feedback->created_at)->get();
            if ($branches->isNotEmpty()) {
                foreach ($branches as $branch) {
                    $feedbacksToUpdate = $feedbacks->where('salon_id', $branch->id);
                    if ($feedbacksToUpdate->isNotEmpty()) {
                        foreach ($feedbacksToUpdate as $feedbackToUpdate) {
                            $feedbackToUpdate->update(['question' => $question]);
                        }
                    }
                }
            }

            return redirect('feedback/feedback')->with('flash_message', 'Feedback updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('feedback','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Feedback::destroy($id);

            return redirect('feedback/feedback')->with('flash_message', 'Feedback deleted!');
        }
        return response(view('403'), 403);

    }
}
