<?php

namespace App\Http\Controllers\CustomerFeedback;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\CustomerFeedback;
use Illuminate\Http\Request;

class CustomerFeedbackController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('customerfeedback','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $customerfeedback = CustomerFeedback::where('appointment_id', 'LIKE', "%$keyword%")
                ->orWhere('rating_id', 'LIKE', "%$keyword%")
                ->orWhere('feedback_id', 'LIKE', "%$keyword%")
                ->orWhere('answer', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $customerfeedback = CustomerFeedback::paginate($perPage);
            }

            return view('customerFeedback.customer-feedback.index', compact('customerfeedback'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('customerfeedback','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('customerFeedback.customer-feedback.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('customerfeedback','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            CustomerFeedback::create($requestData);
            return redirect('customerFeedback/customer-feedback')->with('flash_message', 'CustomerFeedback added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('customerfeedback','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $customerfeedback = CustomerFeedback::findOrFail($id);
            return view('customerFeedback.customer-feedback.show', compact('customerfeedback'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('customerfeedback','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $customerfeedback = CustomerFeedback::findOrFail($id);
            return view('customerFeedback.customer-feedback.edit', compact('customerfeedback'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('customerfeedback','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $customerfeedback = CustomerFeedback::findOrFail($id);
             $customerfeedback->update($requestData);

             return redirect('customerFeedback/customer-feedback')->with('flash_message', 'CustomerFeedback updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('customerfeedback','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            CustomerFeedback::destroy($id);

            return redirect('customerFeedback/customer-feedback')->with('flash_message', 'CustomerFeedback deleted!');
        }
        return response(view('403'), 403);

    }
}
