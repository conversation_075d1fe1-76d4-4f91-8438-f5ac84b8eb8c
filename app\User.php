<?php

namespace App;

use App\CustomNotification;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use App\HasRoles;
use Auth;
use Illuminate\Support\Collection;

use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    use Notifiable;
    use HasRoles;
    use SoftDeletes;
    protected $appends = ['discount_price', 'salonOffDatesIds','employeeServiceIds', 'allEmployeeProductCategoryIds', 'salonRating', 'allEmployeeServiceCategoryIds', 'allEmployeeServiceIds','employeeServiceCategoryIds', 'cutomerCountComplete', 'cutomerPayments','cutomerProductCategoryIds', 'cutomerServiceCategoryIds', 'customerServiceIds', 'customerProductIds','customerServicesPrice','allSalonServiceCategory','allCashierCount','allEmployeeCount','employeeAssignedCutomerAppointments','employeeAssignedLastBookingDate','employeeUpcomingAppointments','employeeCompletedAppointments','shopCustomerAppointments','employeeWithMaxServices','employeeAssignedProducts','shopBranches','allShopCustomers','shopBranchesProducts'];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = ['address','dob','gender','profile_picture'];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
         'remember_token',
    ];


    public function profile(){
        return $this->hasOne(Profile::class);
    }

    public function customNotifications()
    {
        return $this->hasMany(CustomNotification::class,'notifiable_id', 'id');
    }

    public function permissionsList(){
        $roles = $this->roles;
        $permissions = [];
        foreach ($roles as $role){
            $permissions[] = $role->permissions()->pluck('name')->implode(',');
        }
       return collect($permissions);
    }

    public function permissions(){
        $permissions = [];
        $role = $this->roles->first();
        $permissions = $role->permissions()->get();
        return $permissions;
    }

    public function isAdmin(){
       $is_admin =$this->roles()->where('name','admin')->first();
       if($is_admin != null){
           $is_admin = true;
       }else{
           $is_admin = false;
       }
       return $is_admin;
    }

    public function getdiscountPriceAttribute(){
        if ($this->base_price>0 && $this->discount_percentage>0) {
            $originalPrice = $this->base_price;
            $discountPercentage = $this->discount_percentage;
            $discountAmount = $originalPrice * ($discountPercentage / 100);
            return $discountedPrice = $originalPrice - $discountAmount;
        }else{
            return 0;
        }
    }
    public function salon(){
        return $this->belongsTo(User::class,'salon_id');
    }
    public function salonType(){
        return $this->hasOne(SalonType::class,'id','salon_type_id');
    }
    public function customerType(){
        return $this->belongsTo(CustomerType::class,'customer_type_id');
    }
    public function appointmentType(){
        return $this->belongsTo(AppointmentType::class,'appointment_type_id');
    }
    public function employeeType(){
        return $this->hasOne(EmployeeType::class,'employee_id','id');
    }
    public function suppilerType(){
        return $this->hasOne(ProductSupplierType::class,'supplier_id','id');
    }
    public function salonServicePrice(){
        return $this->hasOne(SalonService::class,'salon_id','id');
    }
    public function customerAppointment(){
        return $this->hasOne(CustomerAppointment::class,'customer_id','id');
    }
    public function customerAppointments(){
        return $this->hasMany(CustomerAppointment::class,'customer_id','id');
    }
    public function customerAppointmentSalon(){
        return $this->hasMany(CustomerAppointment::class,'salon_id','id');
    }
    public function getEmployeeService(){
        return $this->hasMany(EmployeeService::class,'employee_id', 'id');
    }
    public function getEmployeeServices(){
        return $this->hasOne(EmployeeService::class,'employee_id', 'id');
    }
    public function getemployeeServiceIdsAttribute(){
        if (!$this->hasRole('customer')) {
            return EmployeeService::where('employee_id',$this->id)->pluck('salon_service_id')->toArray();;
        }
        return [];
    }
    public function getallEmployeeServiceIdsAttribute(){
        if (!$this->hasRole('customer')) {
            $employee = User::where('salon_id',$this->id)->pluck('id')->toArray();;
            $employeeService = EmployeeService::whereIn('employee_id', $employee)->pluck('salon_service_id')->toArray();;
            return $salonService = SalonService::where('is_deleted','1')->whereIn('id',$employeeService)->pluck('id')->toArray();;
        }
        return [];
    }
    public function getallEmployeeServiceCategoryIdsAttribute(){
        if (!$this->hasRole('customer')) {
            $employee = User::where('salon_id',$this->id)->pluck('id')->toArray();;
            $employeeService = EmployeeService::whereIn('employee_id', $employee)->pluck('salon_service_id')->toArray();;
            $salonService = SalonService::where('is_deleted','1')->whereIn('id',$employeeService)->pluck('category_id')->toArray();;
            return $salonCategory = ServiceCategory::whereIn('id',$salonService)->pluck('id')->toArray();;
        }
        return [];
    }
    public function getallEmployeeProductCategoryIdsAttribute(){
        if (!$this->hasRole('customer')) {
            $salonProducts = Product::where('is_deleted','1')->where('salon_id',$this->id)->pluck('product_category_id')->toArray();;
            return $salonCategory = ServiceCategory::whereIn('id',$salonProducts)->pluck('id')->toArray();;
        }
        return [];
    }
    public function getemployeeServiceCategoryIdsAttribute(){
        if (!$this->hasRole('customer')) {
            $employeeServicesIds = EmployeeService::where('employee_id',$this->id)->pluck('id')->toArray();
            return $salonServiceCategory = EmployeeServiceCategory::whereIn('employee_service_id',$employeeServicesIds)->pluck('service_category_id')->toArray();
        }
        return [];
    }
    public function getcutomerServiceCategoryIdsAttribute(){
        $customerServicesIds = CustomerService::where('customer_id',$this->id)->pluck('salon_service_id')->toArray();
        return $salonServiceCategory = SalonService::whereIn('id',$customerServicesIds)->pluck('category_id')->toArray();
    }
    public function getcutomerProductCategoryIdsAttribute(){
        $customerProductsIds = CustomerProduct::where('customer_id',$this->id)->pluck('salon_product_id')->toArray();
        return $salonProductCategory = Product::whereIn('id',$customerProductsIds)->pluck('product_category_id')->toArray();
    }
    public function getcustomerServiceIdsAttribute(){
        return CustomerService::where('customer_id',$this->id)->pluck('salon_service_id')->toArray();;
    }
    public function getcustomerProductIdsAttribute(){
        return CustomerProduct::where('customer_id',$this->id)->pluck('salon_product_id')->toArray();;
    }
    public function getCustomerServices(){
        return $this->hasMany(CustomerService::class,'customer_id', 'id');
    }
    public function getcustomerServicesPriceAttribute(){
        $customerServiceIds = CustomerService::where('customer_id', $this->id)->pluck('salon_service_id');
        if($customerServiceIds->count()!=0){
            $customerProductIds = CustomerProduct::where('customer_id', $this->id)->get('salon_product_id');
            $salonServicePrice = SalonService::whereIn('id',$customerServiceIds)->sum('price');
            $productPrice = ProductInventory::whereIn('product_id', $customerProductIds)->sum('price');
            return $salonServicePrice+$productPrice;
        }else{
            return 0;
        }
    }
    public function getcutomerCountCompleteAttribute(){
        if (!$this->hasRole('customer')) {
            $appointmentIds = AssignedCustomer::where('employee_id',$this->id)->pluck('appointment_id');
            $assigned = CustomerAppointment::where('status','Complete')->whereIn('id',$appointmentIds)->count();
            return $assigned;
        }
        return [];
    }
    public function getcutomerPaymentsAttribute(){
        if (!$this->hasRole('customer')) {
            $assignedEmployee = AssignedCustomer::where('employee_id',$this->id)->pluck('appointment_id')->toArray();;
            $appointmentpayments = CustomerAppointment::where('status','Complete')->whereIn('id', $assignedEmployee)->pluck('id')->toArray();
            $CustomerService = CustomerService::whereIn('appointment_id',$appointmentpayments)->pluck('salon_service_id')->toArray();
            $prices = SalonService::whereIn('id', $CustomerService)
                ->pluck('price', 'id')
                ->toArray();
            $total = 0;
            foreach ($CustomerService as $id) {
                $total += $prices[$id] ?? 0;
            }
            return $total;
        }
        return '0';
    }
    public function getCustomerService(){
        return $this->hasOne(CustomerService::class,'customer_id', 'id');
    }
    public function getCustomerProducts(){
        return $this->hasMany(CustomerProduct::class,'customer_id', 'id');
    }
    public function getCustomerProduct(){
        return $this->hasOne(CustomerProduct::class,'customer_id', 'id');
    }
    public function userSubscription(){
        return $this->hasOne(FatoraInvoice::class,'user_subscription_id', 'id');
    }
    public function userSubscriptionId(){
        return $this->hasOne(UserSubscription::class,'user_id', 'id');
    }
    public function userSubscriptionIdLatest(){
        return $this->hasOne(UserSubscription::class,'user_id', 'id')->orderBy('id', 'desc');
    }
    public function userSubscriptionIds(){
        return $this->hasMany(UserSubscription::class,'user_id', 'id');
    }
    public function employeeExpiryNotification(){
        return $this->hasOne(EmployeeExpiryNotification::class,'employee_id', 'id');
    }
    public function salonAmenities(){
        return $this->hasMany(SalonAmenity::class,'salon_id', 'id');
    }
    public function employeeLeaves(){
        return $this->hasMany(EmployeeLeave::class,'employee_id', 'id');
    }
    public function getsalonRatingAttribute(){
        if (!$this->hasRole('customer')) {
            $totalRatings = Rating::where('salon_id', $this->id)->count();
            if ($totalRatings === 0) {
                return 0;
            }
            $sumRatings = Rating::where('salon_id', $this->id)->sum('rating');
            $averageRating = $sumRatings / $totalRatings;
            return $averageRating;
        }
        return 0;
    }
    public function salonOffDates(){
        return $this->hasMany(OffDate::class,'salon_id','id');
    }
    public function getsalonOffDatesIdsAttribute(){
        $offDates = OffDate::where('salon_id',$this->id)->pluck('date_id')->toArray();;
        return end($offDates);
    }
    public function salonOffDate(){
        return $this->hasOne(OffDate::class,'salon_id','id');
    }
    public function getallSalonServiceCategoryAttribute(){
        return $salonServiceCategory = ServiceCategory::where('salon_id',$this->id)->pluck('id')->toArray();;
    }
    public function getallCashierCountAttribute(){
        return $cashier = User::whereHas(
            'roles', function($q){
            $q->where('name', 'cashier');
        }
        )->where('salon_id',$this->id)->orderBy('id','DESC')->count();
    }
    public function getallEmployeeCountAttribute(){
        return $cashier = User::whereHas(
            'roles', function($q){
            $q->where('name', 'employee');
        }
        )->where('salon_id',$this->id)->orderBy('id','DESC')->count();
    }
    public function getemployeeAssignedCutomerAppointmentsAttribute(){
        $appointmentIds = AssignedCustomer::where('employee_id', $this->id)
            ->pluck('appointment_id')->toArray();
        $assigned = CustomerAppointment::whereIn('status', ['Approved', 'Pending'])
            ->where('salon_id',$this->salon_id)
            ->whereIn('id', $appointmentIds)
            ->get();
        return $assigned;
    }
    public function getemployeeAssignedLastBookingDateAttribute() {
        $appointments = CustomerAppointment::whereIn('status', ['Approved', 'Pending'])
            ->where('salon_id', $this->salon_id)
            ->whereIn('id', AssignedCustomer::where('employee_id', $this->id)
                ->pluck('appointment_id')->toArray())
            ->get(['customer_appointment_date']);
        if ($appointments->isNotEmpty()) {
            $convertedDates = $appointments->map(function ($appointment) {
                return \DateTime::createFromFormat('m/d/Y', $appointment->customer_appointment_date)->format('Y-m-d');
            })->all();
            $latestDate = max($convertedDates);
            $latestDateFormatted = \DateTime::createFromFormat('Y-m-d', $latestDate)->format('m/d/Y');
            return $latestDateFormatted;
        }
        return null;
    }
    public function getAssignedAppointmentIdsAttribute() {
        return AssignedCustomer::where('employee_id', $this->id)
            ->pluck('appointment_id')
            ->toArray();
    }
    public function getemployeeUpcomingAppointmentsAttribute() {
        $today = now()->format('m/d/Y');
        $assignedAppointmentIds = $this->assignedAppointmentIds;
        $appointments = CustomerAppointment::whereIn('status', ['Approved', 'Pending'])
            ->where('salon_id', $this->salon_id)
            ->whereIn('id', $assignedAppointmentIds)
            ->where('customer_appointment_date', "<", $today)
            ->get();
        return $appointments;
    }
    public function getemployeeCompletedAppointmentsAttribute() {
        $assignedAppointmentIds = $this->assignedAppointmentIds;
        $appointments = CustomerAppointment::where('salon_id', $this->salon_id)
            ->whereIn('id', $assignedAppointmentIds)
            ->where('status','Complete')
            ->get();
        return $appointments;
    }
    public function getSalonIdAttribute($salon_id){
        if ($salon_id==""){
            return $this->id;
        }else{
            return $salon_id;
        }
    }
    public function getshopBranchesAttribute(){
        if (!$this->hasRole('customer')) {
            return User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', $this->id)->orWhere('id',$this->id)
                ->get();
        }
        return [];
    }
    public function getshopBranchesProductsAttribute(){
        if (!$this->hasRole('customer')) {
            $salons = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', $this->id)->orWhere('id',$this->id)
                ->get();
            $salonIds = $salons->pluck('id')->toArray();
            return $totalproduct = Product::whereIn('salon_id',$salonIds)->get();
        }
        return [];
    }
    public function getallShopCustomersAttribute(){
        if (!$this->hasRole('customer')) {
            $ids = $this->getshopBranchesAttribute()->pluck('id')->toArray();
            return CustomerAppointment::whereIn('salon_id', $ids)
                ->get()
                ->unique('customer_id')
                ->values();
        }
        return [];
    }
    public function getshopCustomerAppointmentsAttribute(){
        return $this->customerAppointments()->where('salon_id',Auth::id())->orderBy('id','DESC')->get();
    }
    public function getemployeeWithMaxServicesAttribute()
    {
        if (!$this->hasRole('customer')) {
            $employees = User::where('salon_id', $this->id)
                ->whereHas('roles', function ($q) {
                    $q->where('name', 'employee');
                })
                ->withCount('getEmployeeService')
                ->orderBy('get_employee_service_count', 'desc')
                ->first();
            if(isset($employees->getEmployeeService) && $employees->getEmployeeService != null){
                return $employees->getEmployeeService->count();
            }else{
                return null;
            }
        }
        return null;
    }
    public function getemployeeAssignedProductsAttribute()
    {
        if (!$this->hasRole('customer')) {
            $stockOut = StockOut::whereHas('purchaseOrder', function ($query) {
                $query->where('employee_id', $this->id);
            })->get();
            $assignedProducts = $stockOut->unique('product_id')->pluck('product_id');
            $totalProducts = $stockOut->sum('quantity');
            return [
                'assignedProducts' => $assignedProducts,
                'totalProducts' => $totalProducts
            ];
        }
        return [
            'assignedProducts' => [],
            'totalProducts' => []
        ];
    }

}
