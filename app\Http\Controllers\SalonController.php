<?php

namespace App\Http\Controllers;

use App\UserSubscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Auth;
use App\SalonType;
use App\User;
use Storage;
use App\OffDate;
use App\Slot;
use App\Profile;
use Carbon\Carbon;
use DateTime;
use DateInterval;

class SalonController extends Controller{
    public function salonSetting($id=null){
        if (Auth::user()->shopBranches->pluck('id')->contains($id)) {
            $user = User::findOrFail($id);
            $lastAppointments = $user->customerAppointmentSalon()
                ->whereIn('status',['Approved','Pending'])
                ->selectRaw('MAX(STR_TO_DATE(customer_appointment_date, "%m/%d/%Y")) as max_date')
                ->first()
                ->max_date;
            $lastAppointments = $lastAppointments > now() ? $lastAppointments : null;
            $salonTypes = SalonType::wherestatus(1)->get();
            $employee = User::whereHas(
                'roles', function($q){
                $q->where('name', 'employee');
            }
            )->where('salon_id',$user->id)->orderBy('id','DESC')->get();
            $userSubscrption = UserSubscription::where('user_id',$user->id)->orderBy('id','DESC')->first();
            return view('dashboard.businessDashboard.salon_setting',compact('user','salonTypes','employee','lastAppointments','userSubscrption'));
        }else{
            return redirect()->back()->with(['title'=>'Error','message'=>'Please Try Again','type'=>'error']);
        }
    }//end salonSetting function.
	public function saveSalonSetting(Request $request){
        try{
			extract($request->all());
			$user = User::findOrFail($user_id);
            if ($password!="") {
				$user_password = bcrypt($password);
			}else{
				$user_password = $user->password;
			}//end if else
            $appointmentTypeId = $request->has('appointment_type_id') ? $request->input('appointment_type_id') : 2;
            $names = explode(' ', $name, 2);
            $first_name = $names[0];
            $last_name = count($names) > 1 ? $names[1] : '';
            if(Auth::user()->salon_setting_updated == 0){
                $tour_status = 2;
            }else{
                $tour_status = Auth::user()->tour_status;
            }
			User::where('id',$user->id)->update(['first_name'=>$first_name,'last_name'=>$last_name,'name'=>$name,'email'=>$email,'facebook'=>$facebook,'instagram'=>$instagram,'twitter'=>$twitter,'whatsapp'=>$whatsapp,'password'=>$user_password,'salon_type_id'=>$salon_type_id, 'appointment_type_id'=>$appointmentTypeId,'title'=>$title,'phone'=>$phone,'tour_status'=>$tour_status]);
            if ($request->has('profile_picture')) {
                $base64Image = $request->input('profile_picture');
                $extension = 'png';
                $imageData = explode(',', $base64Image);
                $imageData = $imageData[1];
                $image = base64_decode($imageData);
	            $destinationPath = public_path() . '/storage/uploads/users/';
	            $safeName = str_random(10) . '.' . $extension;
                file_put_contents($destinationPath . $safeName, $image);
	            $profil_image= $safeName;
				$salon_setting_updated = 1;
			}else{
			    if($user->profile->pic!=null){
                    $profil_image = $user->profile->pic;
                }else{
                    $profil_image = 'no_image.png';
                }

				$salon_setting_updated = 1;
			}
            if($request->hasFile('reception_area_one')){
                $reception_area_one = Storage::disk('website')->put('salon_reception_area', $request->reception_area_one);
            }else{
                $reception_area_one = $user->profile->reception_area_one;
            }
            if($request->hasFile('reception_area_two')){
                $reception_area_two = Storage::disk('website')->put('salon_reception_area', $request->reception_area_two);
            }else{
                $reception_area_two = $user->profile->reception_area_two;
            }
            if($request->hasFile('reception_area_three')){
                $reception_area_three = Storage::disk('website')->put('salon_reception_area', $request->reception_area_three);
            }else{
                $reception_area_three = $user->profile->reception_area_three;
            }
            if($request->hasFile('reception_area_four')){
                $reception_area_four = Storage::disk('website')->put('salon_reception_area', $request->reception_area_four);
            }else{
                $reception_area_four = $user->profile->reception_area_four;
            }
            if($request->hasFile('cover_picture')){
                $cover_picture = Storage::disk('website')->put('cover_picture', $request->cover_picture);
            }else{
                $cover_picture = $user->profile->cover_picture;
            }
            if($request->hasFile('service_area_one')){
                $service_area_one = Storage::disk('website')->put('service_area', $request->service_area_one);
            }else{
                $service_area_one = $user->profile->service_area_one;
            }
            if($request->hasFile('service_area_two')){
                $service_area_two = Storage::disk('website')->put('service_area', $request->service_area_two);
            }else{
                $service_area_two = $user->profile->service_area_two;
            }
            if($request->hasFile('service_area_three')){
                $service_area_three = Storage::disk('website')->put('service_area', $request->service_area_three);
            }else{
                $service_area_three = $user->profile->service_area_three;
            }
            if($request->hasFile('service_area_four')){
                $service_area_four = Storage::disk('website')->put('service_area', $request->service_area_four);
            }else{
                $service_area_four = $user->profile->service_area_four;
            }
			User::where('id',$user->id)->update(['salon_setting_updated'=>$salon_setting_updated]);
            $full_name = $request->name;
            $full_name_no_space = str_replace(' ', '', $full_name);
            $link = url('salon_detail', ['id' => $user->id]) . '/' . $full_name_no_space;
            if ($request->open_time < $request->closed_time){
                if ($user->profile->open_time == $request->open_time &&  $user->profile->closed_time == $request->closed_time){
                    if ($request->slot_time != $user->profile->slot_time){
                        Profile::where('user_id',$user->id)->update(['address'=>$address,'city'=>$city,'state'=>$state,'phone'=>$phone, 'vat_number'=>$vat_number,'latitude'=>$latitude,'longitude'=>$longitude,'pic'=>$profil_image,'open_time'=>$open_time,'closed_time'=>$closed_time,'slot_time'=>$slot_time, 'reception_area_one'=>$reception_area_one,'reception_area_two'=>$reception_area_two,'service_area_one'=>$service_area_one,'service_area_two'=>$service_area_two,'location_link'=>$location_link,'cover_picture'=>$cover_picture,'reception_area_three'=>$reception_area_three,'service_area_three'=>$service_area_three,'reception_area_four'=>$reception_area_four,'service_area_four'=>$service_area_four,'service_area_description'=>$service_area_description,'reception_area_description'=>$reception_area_description,'our_team_description'=>$our_team_description,'link' => $link]);
                        if (Slot::where('salon_id', $user->id)->exists()) {
                            Slot::where('salon_id',$user->id)->delete();
                        }
                        $slots = [];
                        $openTime = date('h:i A', strtotime($request->open_time));
                        $closeTime = date('h:i A', strtotime($request->closed_time));

                        $startTime = \DateTime::createFromFormat('h:i A', $openTime);
                        $endTime = \DateTime::createFromFormat('h:i A', $closeTime);
                        $key = 1;
                        $number = 0;
                        while ($startTime < $endTime) {
                            $slot = [
                                'start_time' => $startTime->format('h:i A'),
                            ];
                            $startTime->add(new \DateInterval('PT'.$request->slot_time.'M'));
                            $slot['end_time'] = $startTime->format('h:i A');
                            $breakTime = clone $startTime;
                            $breakTime->add(new \DateInterval('PT'.$request->break_time.'M'));
                            if ($endTime>$startTime) {
                                if ($startTime != $endTime || $breakTime != $endTime) {
                                    $slot['break_start'] = $startTime->format('h:i A');
                                    $slot['break_end'] = $breakTime->format('h:i A');
                                    $slot['salon_id'] = $user->id;
                                    $slot['slot_id'] = $key;
                                    $startTime = $breakTime;
                                    $slots[$number] = $slot;
                                    $number++;
                                    $key++;
                                }
                            }
                        }
                        $slotIds = array_column($slots, 'slot_id');
                        if (Slot::where('salon_id', $user->id)->exists()) {
                            $existingSlot = Slot::where('salon_id', $user->id)->whereNotIn('slot_id', [$slotIds])->update(['avalible_status' => 'Inactive']);
                        }
                        if (is_array($slots)) {
                            foreach ($slots as $slot) {
                                if ($slots) {
                                    $slot['avalible_status'] = 'Active';
                                    $newSlot = Slot::UpdateOrcreate($slot);
                                    $existingSlotIds[] = $newSlot->slot_id;
                                }
                            }
                        }
                    }else {
                        Profile::where('user_id',$user->id)->update(['address'=>$address,'city'=>$city,'state'=>$state,'phone'=>$phone, 'vat_number'=>$vat_number,'latitude'=>$latitude,'longitude'=>$longitude,'pic'=>$profil_image,'open_time'=>$open_time,'closed_time'=>$closed_time,'slot_time'=>$slot_time,'reception_area_one'=>$reception_area_one,'reception_area_two'=>$reception_area_two,'reception_area_three'=>$reception_area_three,'reception_area_four'=>$reception_area_four,'service_area_one'=>$service_area_one,'service_area_two'=>$service_area_two,'service_area_three'=>$service_area_three,'service_area_four'=>$service_area_four,'location_link'=>$location_link,'cover_picture'=>$cover_picture,'service_area_description'=>$service_area_description,'reception_area_description'=>$reception_area_description,'our_team_description'=>$our_team_description,'link'=>$link]);
                        $slots = [];
                        $openTime = date('h:i A', strtotime($request->open_time));
                        $closeTime = date('h:i A', strtotime($request->closed_time));
                        $startTime = \DateTime::createFromFormat('h:i A', $openTime);
                        $endTime = \DateTime::createFromFormat('h:i A', $closeTime);
                        $key = 1;
                        $number = 0;
                        while ($startTime < $endTime) {
                            $slot = [
                                'start_time' => $startTime->format('h:i A'),
                            ];
                            $startTime->add(new \DateInterval('PT'.$request->slot_time.'M'));
                            $slot['end_time'] = $startTime->format('h:i A');
                            $breakTime = clone $startTime;
                            $breakTime->add(new \DateInterval('PT'.$request->break_time.'M'));
                            if ($endTime>$startTime) {
                                if ($startTime != $endTime || $breakTime != $endTime) {
                                    $slot['break_start'] = $startTime->format('h:i A');
                                    $slot['break_end'] = $breakTime->format('h:i A');
                                    $slot['salon_id'] = $user->id;
                                    $slot['slot_id'] = $key;
                                    $startTime = $breakTime;
                                    $slots[$number] = $slot;
                                    $number++;
                                    $key++;
                                }
                            }
                        }
                        $slotIds = array_column($slots, 'slot_id');
                        if (Slot::where('salon_id', $user->id)->exists()){
                            $existingSlot = Slot::where('salon_id', $user->id)->whereNotIn('slot_id', $slotIds)->update(['avalible_status'=>'Inactive']);
                        }
                        if (is_array($slots)) {
                            foreach ($slots as $slot) {
                                if ($slots) {
                                    $slot['avalible_status'] = 'Active';
                                    $newSlot = Slot::updateOrCreate($slot);
                                    $existingSlotIds[] = $newSlot->slot_id;
                                }
                            }
                        }
                    }
                }else{
                    if ($request->slot_time != $user->profile->slot_time){
                        Profile::where('user_id',$user->id)->update(['address'=>$address,'city'=>$city,'state'=>$state,'phone'=>$phone, 'vat_number'=>$vat_number,'latitude'=>$latitude,'longitude'=>$longitude,'pic'=>$profil_image,'open_time'=>$open_time,'closed_time'=>$closed_time,'slot_time'=>$slot_time, 'reception_area_one'=>$reception_area_one,'reception_area_two'=>$reception_area_two,'service_area_one'=>$service_area_one,'service_area_two'=>$service_area_two,'location_link'=>$location_link,'cover_picture'=>$cover_picture,'reception_area_three'=>$reception_area_three,'service_area_three'=>$service_area_three,'reception_area_four'=>$reception_area_four,'service_area_four'=>$service_area_four,'service_area_description'=>$service_area_description,'reception_area_description'=>$reception_area_description,'link'=>$link]);
                        if (Slot::where('salon_id', $user->id)->exists()) {
                            Slot::where('salon_id',$user->id)->delete();
                        }
                        $slots = [];
                        $openTime = date('h:i A', strtotime($request->open_time));
                        $closeTime = date('h:i A', strtotime($request->closed_time));
                        $startTime = \DateTime::createFromFormat('h:i A', $openTime);
                        $endTime = \DateTime::createFromFormat('h:i A', $closeTime);
                        $key = 1;
                        $number = 0;
                        while ($startTime < $endTime) {
                            $slot = [
                                'start_time' => $startTime->format('h:i A'),
                            ];
                            $startTime->add(new \DateInterval('PT'.$request->slot_time.'M'));
                            $slot['end_time'] = $startTime->format('h:i A');
                            $breakTime = clone $startTime;
                            $breakTime->add(new \DateInterval('PT'.$request->break_time.'M'));
                            if ($endTime > $startTime) {
                                if ($startTime != $endTime || $breakTime != $endTime) {
                                    $slot['break_start'] = $startTime->format('h:i A');
                                    $slot['break_end'] = $breakTime->format('h:i A');
                                    $slot['salon_id'] = $user->id;
                                    $slot['slot_id'] = $key;
                                    $startTime = $breakTime;
                                    $slots[$number] = $slot;
                                    $number++;
                                    $key++;
                                }
                            }
                        }
                        $slotIds = array_column($slots, 'slot_id');
                        if (Slot::where('salon_id', $user->id)->exists()){
                            $existingSlot = Slot::where('salon_id', $user->id)->whereNotIn('slot_id', [$slotIds])->update(['avalible_status'=>'Inactive']);
                        }
                        if (is_array($slots)) {
                            foreach ($slots as $slot) {
                                if ($slots) {
                                    $slot['avalible_status'] = 'Active';
                                    $newSlot = Slot::UpdateOrcreate($slot);
                                    $existingSlotIds[] = $newSlot->slot_id;
                                }
                            }
                        }
                    }else {
                        Profile::where('user_id',$user->id)->update(['address'=>$address,'city'=>$city,'state'=>$state,'phone'=>$phone, 'vat_number'=>$vat_number,'latitude'=>$latitude,'longitude'=>$longitude,'pic'=>$profil_image,'open_time'=>$open_time,'closed_time'=>$closed_time,'slot_time'=>$slot_time,'reception_area_one'=>$reception_area_one,'reception_area_two'=>$reception_area_two,'reception_area_three'=>$reception_area_three,'reception_area_four'=>$reception_area_four,'service_area_one'=>$service_area_one,'service_area_two'=>$service_area_two,'service_area_three'=>$service_area_three,'service_area_four'=>$service_area_four,'location_link'=>$location_link,'cover_picture'=>$cover_picture,'service_area_description'=>$service_area_description,'reception_area_description'=>$reception_area_description,'link'=>$link]);
                        if (Slot::where('salon_id', $user->id)->exists()) {
                            Slot::where('salon_id', $user->id)->update(['avalible_status' => 'Inactive']);
                        }
                        $slots = [];
                        $openTime = date('h:i A', strtotime($request->open_time));
                        $closeTime = date('h:i A', strtotime($request->closed_time));
                        $startTime = \DateTime::createFromFormat('h:i A', $openTime);
                        $endTime = \DateTime::createFromFormat('h:i A', $closeTime);
                        $key = 1;
                        $number = 0;
                        while ($startTime < $endTime) {
                            $slot = [
                                'start_time' => $startTime->format('h:i A'),
                            ];
                            $startTime->add(new \DateInterval('PT'.$request->slot_time.'M'));
                            $slot['end_time'] = $startTime->format('h:i A');
                            $breakTime = clone $startTime;
                            $breakTime->add(new \DateInterval('PT'.$request->break_time.'M'));
                            if ($endTime>$startTime) {
                                if ($startTime != $endTime || $breakTime != $endTime) {
                                    $slot['break_start'] = $startTime->format('h:i A');
                                    $slot['break_end'] = $breakTime->format('h:i A');
                                    $slot['salon_id'] = $user->id;
                                    $slot['slot_id'] = $key;
                                    $startTime = $breakTime;
                                    $slots[$number] = $slot;
                                    $number++;
                                    $key++;
                                }
                            }
                        }
                        $slotIds = array_column($slots, 'slot_id');
                        if (Slot::where('salon_id', $user->id)->exists()) {
                            $existingSlot = Slot::where('salon_id', $user->id)->whereNotIn('slot_id', $slotIds)->update(['avalible_status' => 'Inactive']);
                        }
                        if (is_array($slots)) {
                            foreach ($slots as $slot) {
                                if ($slots) {
                                    $slot['avalible_status'] = 'Active';
                                    $newSlot = Slot::updateOrCreate($slot);
                                    $existingSlotIds[] = $newSlot->slot_id;
                                }
                            }
                        }
                    }
                }
            }else{
                return redirect()->back()->with(['title'=>'Error','message'=>'Please Select Correct Time!','type'=>'error']);
            }
            if (User::where('salon_id',Auth::user()->salon_id)->count() == 0){
                return redirect()->back()->with(['title'=>'Done','message'=>'Updated Successfully','type'=>'success']);;
            }else{
                return redirect()->back()->with(['title'=>'Done','message'=>'Updated Successfully','type'=>'success']);;
            }
		}catch(\Exception $e){
            return redirect()->back()->with(['title'=>'Error','message'=>'Please Try Again','type'=>'error']);
		}
    }

}//end class.
