<?php

namespace App\Http\Controllers\SubscriptionPlan;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\SubscriptionPlan;
use App\PackageType;
use Illuminate\Http\Request;

class SubscriptionPlanController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('subscriptionplan','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $subscriptionplan = SubscriptionPlan::where('package_type_id', 'LIKE', "%$keyword%")
                ->orWhere('name', 'LIKE', "%$keyword%")
                ->orWhere('price', 'LIKE', "%$keyword%")
                ->orWhere('status', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $subscriptionplan = SubscriptionPlan::orderBy('id','ASC')->paginate($perPage);
            }

            return view('subscriptionPlan.subscription-plan.index', compact('subscriptionplan'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('subscriptionplan','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            $packageTypes = PackageType::get();
            return view('subscriptionPlan.subscription-plan.create',compact('packageTypes'));
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('subscriptionplan','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            $this->validate($request, [
			'name' => 'required',
			'price' => 'required',
            'description' => 'required'
		]);
            $requestData = $request->all();
            $requestData['description'] = json_encode($request->description);
            SubscriptionPlan::create($requestData);
            return redirect('subscriptionPlan/subscription-plan')->with('flash_message', 'SubscriptionPlan added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('subscriptionplan','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $subscriptionplan = SubscriptionPlan::findOrFail($id);
            return view('subscriptionPlan.subscription-plan.show', compact('subscriptionplan'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('subscriptionplan','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $subscriptionplan = SubscriptionPlan::findOrFail($id);
            $packageTypes = PackageType::get();
            return view('subscriptionPlan.subscription-plan.edit', compact('subscriptionplan','packageTypes'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('subscriptionplan','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $this->validate($request, [
			'name' => 'required',
//			'price' => 'required',
//            'description' => 'required'
		]);
            $requestData = $request->all();
            
            $subscriptionplan = SubscriptionPlan::findOrFail($id);
             $subscriptionplan->update($requestData);

             return redirect('subscriptionPlan/subscription-plan')->with('flash_message', 'SubscriptionPlan updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('subscriptionplan','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            SubscriptionPlan::destroy($id);

            return redirect('subscriptionPlan/subscription-plan')->with('flash_message', 'SubscriptionPlan deleted!');
        }
        return response(view('403'), 403);

    }
}
