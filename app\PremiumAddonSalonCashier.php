<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PremiumAddonSalonCashier extends Model
{
    use SoftDeletes;
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'premium_addon_salon_cashiers';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['primary_salon_id', 'premium_addon_id', 'payment_card_id', 'amount_captured', 'captured_status', 'currency', 'charge_id', 'customer_id', 'stripe_token', 'no_of_users', 'invoice_url'];
    protected $appends = ['premiumAddonHistoryRemaingCashier'];
    public function premiumAddon(){
        return $this->belongsTo(PremiumAddonPackage::class,'premium_addon_id');
    }
    public function getpremiumAddonHistoryRemaingCashierAttribute() {
        $addon = PremiumAddonPackage::where('id', $this->premium_addon_id)->value('no_of_users');
        $history = PremiumAddonCashierHistory::where('premium_addon_salon_cashier_id', $this->id)->count();
        return $addon - $history;
    }
    public function primarySalon(){
        return $this->belongsTo(User::class,'primary_salon_id','id');
    }
}
