<?php

namespace App\Http\Controllers\Discount;

use App\CustomerAppointment;
use App\Http\Controllers\Controller;
use App\Http\Requests;
use Carbon\Carbon;
use App\Discount;
use App\User;
use Auth;
use Illuminate\Http\Request;

class DiscountController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('discount','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 250000;

            if (!empty($keyword)) {
                $discount = Discount::where('validity_date_to', 'LIKE', "%$keyword%")
                    ->orWhere('validity_date_from', 'LIKE', "%$keyword%")
                    ->orWhere('quantity', 'LIKE', "%$keyword%")
                    ->orWhere('discount_number', 'LIKE', "%$keyword%")
                    ->orWhere('percentage', 'LIKE', "%$keyword%")
                    ->orWhere('price', 'LIKE', "%$keyword%")
                    ->orWhere('status', 'LIKE', "%$keyword%")
                    ->orWhere('salon_id', 'LIKE', "%$keyword%")
                    ->paginate($perPage);
            } else {
                $branches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                $branchIds = $branches->pluck('id');
                $discount = Discount::whereIn('salon_id',$branchIds)->orderBy('id','DESC')->paginate($perPage);
                $discountIds = $discount->pluck('id');
                $customerDiscounts = CustomerAppointment::whereIn('discount_id',$discountIds)->whereIn('salon_id',$branchIds)->get();
            }
            return view('discount.discount.index', compact('discount','customerDiscounts','branches'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('discount','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('discount.discount.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('discount','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            extract($request->all());
            $date_to = $request->validity_date_to;
            $date_from = $request->validity_date_from;
            $validity_date_to = Carbon::createFromFormat('m/d/Y', $date_to)->format('Y-m-d');
            $validity_date_from = Carbon::createFromFormat('m/d/Y', $date_from)->format('Y-m-d');
            if ($request->percentage==null){
                $percentage = null;
            }
            if ($request->price==null){
                $price = null;
            }
            $requestData = (['validity_date_to'=>$validity_date_to,'validity_date_from'=>$validity_date_from,'quantity'=>$quantity,'status'=>$status,'salon_id'=>$salon_id,'discount_number'=>$discount_number,'price'=>$price,'percentage'=>$percentage]);
            Discount::create($requestData);
            return redirect('discount/discount')->with('flash_message', 'Discount added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('discount','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $discount = Discount::findOrFail($id);
            return view('discount.discount.show', compact('discount'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('discount','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $discount = Discount::findOrFail($id);
            return view('discount.discount.edit', compact('discount'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('discount','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {

            $requestData = $request->all();

            $discount = Discount::findOrFail($id);
            $discount->update($requestData);

            return redirect('discount/discount')->with('flash_message', 'Discount updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('discount','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Discount::destroy($id);

            return redirect('discount/discount')->with('flash_message', 'Discount deleted!');
        }
        return response(view('403'), 403);

    }
}
