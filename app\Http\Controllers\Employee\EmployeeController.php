<?php

namespace App\Http\Controllers\Employee;

use App\EmployeeExpiryNotification;
use App\CustomNotification;
use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\EmployeeType;
use App\PaymentCard;
use App\PremiumAddonEmployeeHistory;
use App\PremiumAddonPackage;
use App\PremiumAddonSalonEmployee;
use App\PurchaseOrder;
use App\User;
use App\Profile;
use App\SalonService;
use App\ServiceCategory;
use App\EmployeeService;
use App\EmployeeServiceCategory;
use App\UserSubscription;
use Auth;
use Storage;
use Mail;
use Carbon\Carbon;
use Illuminate\Http\Request;

class EmployeeController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('employee','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25000000;
            if (!empty($keyword)) {
                $employee = User::whereHas(
                    'roles', function($q){
                    $q->where('name', 'employee');
                }
                )->where('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('first_name', 'LIKE', "%$keyword%")
                ->orWhere('last_name', 'LIKE', "%$keyword%")
                ->orWhere('email', 'LIKE', "%$keyword%")
                ->orWhere('password', 'LIKE', "%$keyword%")
                ->where('salon_id',Auth::id())->paginate($perPage);
            } else {
                if ($request->branch_id != null){
                    if (in_array($request->branch_id, $this->getBranchIds())){
                        $branches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('id', $request->branch_id)->get();
                        $branchIds = $branches->pluck('id');
                        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                            ->orderBy('user_id', 'ASC')
                            ->orderBy('id', 'DESC')
                            ->get()
                            ->groupBy('user_id');
                        $currentDate = now();
                        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                            $dueDate = $subs->first()->fatoraPdf->due_date;
                            return $dueDate->isPast();
                        })->keys();
                        $employee = User::whereHas(
                            'roles', function($q){
                            $q->where('name', 'employee');
                        }
                        )->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->orderBy('id','DESC')->paginate($perPage);
                        $employeeCount = User::whereHas(
                            'roles', function($q){
                            $q->where('name', 'employee');
                        }
                        )->whereIn('salon_id',$branchIds)->orderBy('id','DESC')->count();
                        $cashierCount = User::whereHas(
                            'roles', function($q){
                            $q->where('name', 'cashier');
                        }
                        )->whereIn('salon_id',$branchIds)->orderBy('id','DESC')->count();
                        $salonServices = EmployeeService::get();
                        $userSubscription = UserSubscription::where('user_id',$branchIds)->orderBy('id','DESC')->first();
                        $allBranches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                    }else{
                        return redirect()->back()->with(['title'=>'Error','message'=>'Please Try Again','type'=>'error']);
                    }
                }else{
                    $allBranches = User::whereHas('roles', function ($query) {
                        $query->where('name', 'spa_salon');
                    })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                    $branchIds = $allBranches->pluck('id');
                    $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                        ->orderBy('user_id', 'ASC')
                        ->orderBy('id', 'DESC')
                        ->get()
                        ->groupBy('user_id');
                    $currentDate = now();
                    $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                        $dueDate = $subs->first()->fatoraPdf->due_date;
                        return $dueDate->isPast();
                    })->keys();
                    $employee = User::whereHas(
                        'roles', function($q){
                        $q->where('name', 'employee');
                    }
                    )->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->orderBy('id','DESC')->paginate($perPage);
                    $employeeCount = User::whereHas(
                        'roles', function($q){
                        $q->where('name', 'employee');
                    }
                    )->whereIn('salon_id',$branchIds)->orderBy('id','DESC')->count();
                    $cashierCount = User::whereHas(
                        'roles', function($q){
                        $q->where('name', 'cashier');
                    }
                    )->whereIn('salon_id',$branchIds)->orderBy('id','DESC')->count();
                    $salonServices = EmployeeService::get();
                    $userSubscription = UserSubscription::where('user_id',Auth::user()->id)->orderBy('id','DESC')->first();
                }
            }
            return view('employee.employee.index', compact('allBranches','employee', 'salonServices','employeeCount','cashierCount','userSubscription'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('employee','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            $branches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->with('userSubscriptionIdLatest.fatoraPdf')->orderBy('id','ASC')->get();
            $branchIds = $branches->pluck('id');
            $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                ->orderBy('user_id', 'ASC')
                ->orderBy('id', 'DESC')
                ->get()
                ->groupBy('user_id');
            $currentDate = now();
            $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                $dueDate = $subs->first()->fatoraPdf->due_date;
                return $dueDate->isFuture();
            })->keys();
            $salonCategory = SalonService::where('salon_id',[$expiredUserIds[0]])->pluck('category_id');
            $categories = ServiceCategory::whereIn('id',$salonCategory)->orderBy('id', 'DESC')->get();
            $services = SalonService::where('salon_id',[$expiredUserIds[0]])->get();
            $premiumAddonsEmployee = PremiumAddonPackage::where('type','Employee')->where('status','1')->get();
            $taxprice = Profile::where('user_id',2)->first()->vat;
            $paymentCards = PaymentCard::where('salon_id',Auth::user()->salon_id)->orderBy('id','DESC')->limit(3)->get();
            return view('employee.employee.create', compact('services','categories', 'branches','premiumAddonsEmployee','taxprice','paymentCards'));
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {   
        $model = str_slug('employee','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            if (in_array($request->salon_id, $this->getBranchIds())) {
                extract($request->all());
                if(User::where('email',$request->email)->exists()){
                    return redirect()->back()->with('flash_message', 'This Email is Already Taken!');
                }
                $expiry_date = Carbon::parse($request->employee_expiry_date);
                $expiry_date_one_month_ago = clone $expiry_date;
                $expiry_date_one_month_ago->subMonth();
                $one_month_expiry = $expiry_date_one_month_ago->format('Y-m-d');
                $expiry_date_two_week_ago = clone $expiry_date;
                $expiry_date_two_week_ago->subWeeks(2);
                $two_week_expiry = $expiry_date_two_week_ago->format('Y-m-d');
                $expiry_date_one_week_ago = clone $expiry_date;
                $expiry_date_one_week_ago->subWeek();
                $one_week_expiry = $expiry_date_one_week_ago->format('Y-m-d');
                $expiry_date_last_day_ago = clone $expiry_date;
                $expiry_date_last_day_ago->subDay();
                $last_day_expiry = $expiry_date_last_day_ago->format('Y-m-d');
                $live_date = Carbon::now();
                $dob_date = Carbon::parse($request->dob);
                $current_year = $live_date->year;
                $next_date_of_birth = Carbon::create($current_year, $dob_date->month, $dob_date->day);
                if ($next_date_of_birth < $live_date) {
                    $next_date_of_birth->addYear();
                }
                $employee_next_date_of_birth = $next_date_of_birth->format('Y-m-d');
                $phoneDefault = str_replace(' ', '', $request->phone);
                $user = User::create(['name'=>$first_name.' '.$last_name,'first_name'=>$first_name,'last_name'=>$last_name,'name'=>$first_name.' '.$last_name,'email'=>$email,'password'=>bcrypt($password),'salon_id'=>$salon_id,'phone'=>$phone]);
                if ($request->file('profile_picture')) {
                    $file = $request->file('profile_picture');
                    $extension = $file->extension()?: 'png';
                    $destinationPath = public_path() . '/storage/uploads/users/';
                    $safeName = str_random(10) . '.' . $extension;
                    $file->move($destinationPath, $safeName);
                    $profile_picture = $safeName;
                }else{
                    $profile_picture = 'no_avatar.jpg';
                }//end if else.
                // 6 role is for employess
                $user->roles()->attach([1 => ['role_id' => 6,'user_id' => $user->id]]);
                if($request->hasFile('employee_attachment_id')){
                    $employee_attachment_id = Storage::disk('website')->put('employee_attachments', $request->employee_attachment_id);
                }else{
                    $employee_attachment_id = '';
                }
                if($request->hasFile('employee_attachment_health')){
                    $employee_attachment_health = Storage::disk('website')->put('employee_attachments_health', $request->employee_attachment_health);
                }else{
                    $employee_attachment_health = '';
                }
                if($request->hasFile('employee_attachment_insurance')){
                    $employee_attachment_insurance = Storage::disk('website')->put('employee_attachments_insurance', $request->employee_attachment_insurance);
                }else{
                    $employee_attachment_insurance = '';
                }
                Profile::create(['user_id'=>$user->id,'pic'=>$profile_picture,'address'=>$address, 'employee_attachment_id'=>$employee_attachment_id, 'employee_attachment_health'=>$employee_attachment_health, 'employee_attachment_insurance'=>$employee_attachment_insurance,'employee_expiry_date'=>$employee_expiry_date,'phone'=>$phone,'dob'=>$dob,'gender'=>$gender,'state'=>$state,'city'=>$city,'country'=>$country,'latitude'=>$lat,'longitude'=>$lng,'postal'=>$zip_code,'open_time'=>Auth::user()->profile->open_time,'closed_time'=>Auth::user()->profile->closed_time]);
                EmployeeType::create(['employee_id'=>$user->id,'salon_id'=>$salon_id,'salon_type_id'=>$request->employee_type]);
                if ($user!=null) {
                    if (is_array($request->salon_service_id)) {
                        foreach ($request->salon_service_id as $value) {
                            $EmployeeService = EmployeeService::create(['salon_service_id'=>$value,'employee_id'=>$user->id]);
                        }
                    }
                    if (is_array($request->service_category_id)) {
                        foreach ($request->service_category_id as $value) {
                            EmployeeServiceCategory::create(['employee_service_id'=>$EmployeeService->id,'service_category_id'=>$value]);
                        }
                    }
                    if ($request->employee_expiry_date){
                        EmployeeExpiryNotification::create([
                            'one_month_expiry'=>$one_month_expiry,
                            'two_week_expiry'=>$two_week_expiry,
                            'one_week_expiry'=>$one_week_expiry,
                            'last_day_expiry'=>$last_day_expiry,
                            'date_of_birth'=>$employee_next_date_of_birth,
                            'one_month_expiry_status'=>0,
                            'two_week_expiry_status'=>0,
                            'one_week_expiry_status'=>0,
                            'last_day_expiry_status'=>0,
                            'date_of_birth_status'=>0,
                            'employee_id'=>$user->id,
                            'salon_id'=>$salon_id
                        ]);
                    }
                    if(Auth::user()->tour_status == 3){
                        $salon = User::where('id',Auth::user()->id)->update(['tour_status'=>4]);
                    }
                    if ($request->premium_addon_salon_employee != null){
                        $premiumAddonSalonEmployee = PremiumAddonSalonEmployee::where('id',$request->premium_addon_salon_employee)->first();
                        $no_of_users_remaining = $premiumAddonSalonEmployee->premiumAddonHistoryRemaingEmployee - 1;
                        ($no_of_users_remaining);
                        $premiumAddonSalonEmployeeHistory = PremiumAddonEmployeeHistory::create(['salon_id'=>$request->salon_id,'premium_addon_id'=>$premiumAddonSalonEmployee->premium_addon_id,'premium_addon_salon_employee_id'=>$premiumAddonSalonEmployee->id,'no_of_users_remaining'=>$no_of_users_remaining,'employee_id'=>$user->id]);
                        ($premiumAddonSalonCashierHistory);
                    }
                }
                try{
                    $salon = User::findOrFail($salon_id);
                    $salon_picture = $salon->profile->pic;
                    $salon_name = $salon->name;
                    $data = array(
                        'name' => $first_name.' '.$last_name ,
                        'email' => $email,
                        'phone' => $phone,
                        'password' => $password,
                        'shopPicture' =>$salon_picture,
                        'shopName' =>$salon_name,
                        'welcome_message' => 'Welcome',
                        'information_message' => 'Account Registration successful',
                        'detail' => env('APP_URL'),
                        'login_url' => env('APP_URL'),
                        'site_url' => env('APP_URL'),
                    );
                    Mail::send('website.email_templates.registration_employee_email',['data'=>$data],function($message) use($data){
                        $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Usman Dev')->subject('Salon Registration successful');;
                    });

                    $notifyData = [
                        'name'                => $first_name.' '.$last_name ,
                        'shopName'            => $salon_name ?? '--',
                        'information_message' => 'Account Registration successful',
                        'type'                => 'humanCapital',
                        'template'            => 'employee_registration',
                    ];
                    $customNotification = CustomNotification::create([
                        'notifiable_id'   => $salon_id,
                        'notifiable_type' => 'App\User',
                        'type'            => 'humanCapital',
                        'data'            => $notifyData,

                    ]);
                }catch (\Exception $e){

                }
                return redirect('employee/employee')->with('flash_message', 'Employee added!');
            }else{
                return redirect()->back()->with(['title'=>'Error','message'=>'Please Try Again','type'=>'error']);
            }
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('employee','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $employee = User::findOrFail($id);
            return view('employee.employee.show', compact('employee'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {   
        $model = str_slug('employee','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $employee = User::where('id', $id)
                ->whereIn('salon_id', $this->getBranchIds())
                ->firstOrFail();
            $salonCategory = SalonService::where('salon_id',$employee->salon_id)->pluck('category_id');
            $categories = ServiceCategory::whereIn('id',$salonCategory)->orderBy('id', 'DESC')->get();
            $services = SalonService::where('is_deleted','1')->where('salon_id',$employee->salon_id)->get();
            return view('employee.employee.edit', compact('employee','services','categories'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id){
    $model = str_slug('employee','-');
    if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
        extract($request->all());
        $employee = User::where('id',$id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
        $requestData = [
            'name'=>$first_name.' '.$last_name,
            'first_name'=>$first_name,
            'last_name'=>$last_name,
            'email'=>$email,
            'phone' => $phone
        ];
        if ($request->password!=''){
            $requestData['password']=bcrypt($request->password);
        }else{
            $requestData['password']  = $employee->password;
        }
        if ($file = $request->file('profile_picture')) {
            $extension = $file->extension()?: 'png';
            $destinationPath = public_path() . '/storage/uploads/users/';
            $safeName = str_random(10) . '.' . $extension;
            $file->move($destinationPath, $safeName);
            $profile_picture = $safeName;
        }else{
            $profile_picture = $employee->profile->pic;
        }
        $employee->update($requestData);
        if ($request->hasFile('employee_attachment_id')){
            $employee_attachment_id = Storage::disk('website')->put('employee_attachments', $request->employee_attachment_id);
            Storage::disk('website')->delete($employee->profile->employee_attachment_id);
        }else{
            $employee_attachment_id  = $employee->profile->employee_attachment_id;
        }
        if ($request->hasFile('employee_attachment_health')){
            $employee_attachment_health = Storage::disk('website')->put('employee_attachments_health', $request->employee_attachment_health);
            Storage::disk('website')->delete($employee->profile->employee_attachment_health);
        }else{
            $employee_attachment_health  = $employee->profile->employee_attachment_health;
        }
        if ($request->hasFile('employee_attachment_insurance')){
            $employee_attachment_insurance = Storage::disk('website')->put('employee_attachments_insurance', $request->employee_attachment_insurance);
            Storage::disk('website')->delete($employee->profile->employee_attachment_insurance);
        }else{
            $employee_attachment_insurance  = $employee->profile->employee_attachment_insurance;
        }
        $profile = [
            'pic'=>$profile_picture,
            'address'=>$address,
            'employee_attachment_id'=>$employee_attachment_id,
            'employee_attachment_health'=>$employee_attachment_health,
            'employee_attachment_insurance'=>$employee_attachment_insurance,
            'employee_expiry_date'=>$employee_expiry_date,
            'phone'=>$phone,
            'dob'=>$dob,
            'gender'=>$gender,
            'state'=>$state,
            'city'=>$city,
            'country'=>$country,
            'latitude'=>$lat,
            'longitude'=>$lng,
            'postal'=>$zip_code
        ];
        Profile::where('user_id',$employee->id)->update($profile);
        if ($employee!=null) {
            EmployeeService::where('employee_id', $id)->delete();
            foreach ($request->salon_service_id as $value) {
                $EmployeeService = EmployeeService::create([
                    'salon_service_id' => $value,
                    'employee_id' => $id
                ]);
            }
            EmployeeServiceCategory::where('employee_service_id', $EmployeeService->id)->delete();
            foreach ($request->service_category_id as $value) {
                EmployeeServiceCategory::create([
                    'service_category_id' => $value,
                    'employee_service_id' => $EmployeeService->id
                ]);
            }
        }
        return redirect('employee/employee')->with('flash_message', 'Employee updated!');
    }
    return response(view('403'), 403);
}


    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('employee','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            $user = User::whereIn('salon_id', $this->getBranchIds())
                ->where('id', $id)
                ->firstOrFail();
            if ($user) {
                $user->delete();
            }
            return redirect('employee/employee')->with('flash_message', 'Employee deleted!');
        }
        return response(view('403'), 403);

    }
    public function employeeConsumeProducts($id){
        $employee = User::whereHas('roles', function ($query) {
            $query->where('name', 'employee');
        })->where('id',$id)->whereIn('salon_id',$this->getBranchIds())->firstOrFail();
        $purchaseOrders = PurchaseOrder::where('salon_id', $employee->salon_id)->where('employee_id',$employee->id)->orderBy('id','DESC')->get();
        return view('dashboard.businessDashboard.employee_consume_products',compact('purchaseOrders','employee'));
    }
}
