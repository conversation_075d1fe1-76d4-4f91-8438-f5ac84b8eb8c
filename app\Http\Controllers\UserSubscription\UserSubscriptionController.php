<?php

namespace App\Http\Controllers\UserSubscription;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\UserSubscription;
use App\FatoraInvoice;
use App\User;
use Illuminate\Http\Request;

class UserSubscriptionController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('usersubscription','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 250000000;

            if (!empty($keyword)) {
                $usersubscription = UserSubscription::where('user_id', 'LIKE', "%$keyword%")
                    ->orWhere('subscription_plan_id', 'LIKE', "%$keyword%")
                    ->orWhere('amount_captured', 'LIKE', "%$keyword%")
                    ->orWhere('captured_status', 'LIKE', "%$keyword%")
                    ->orWhere('captured_at', 'LIKE', "%$keyword%")
                    ->orWhere('currency', 'LIKE', "%$keyword%")
                    ->orWhere('receipt_url', 'LIKE', "%$keyword%")
                    ->orWhere('charge_id', 'LIKE', "%$keyword%")
                    ->orWhere('status', 'LIKE', "%$keyword%")
                    ->orWhere('description', 'LIKE', "%$keyword%")
                    ->paginate($perPage);
            } else {
//                $usersubscription = UserSubscription::orderBy('id','DESC')->paginate($perPage);
                $salons = User::whereNull('salon_id')->whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->get();
                $salonIds = $salons->pluck('id');
                $usersubscription = UserSubscription::whereIn('user_id', $salonIds)
                    ->whereIn('id', function ($query) {
                        $query->selectRaw('MAX(id)')
                            ->from('user_subscriptions')
                            ->groupBy('user_id');
                    })
                    ->orderBy('id','DESC')->paginate($perPage);
            }

            return view('userSubscription.user-subscription.index', compact('usersubscription'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('usersubscription','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('userSubscription.user-subscription.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('usersubscription','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {

            $requestData = $request->all();

            UserSubscription::create($requestData);
            return redirect('userSubscription/user-subscription')->with('flash_message', 'UserSubscription added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('usersubscription','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
//            $usersubscription = UserSubscription::findOrFail($id);
//            $usersubscription = FatoraInvoice::with('userSubscriptions.users')->where('user_subscription_id',$id)->get();
            $user = User::where('salon_id',$id)->get();
            $salonIds = $user->pluck('id')->toArray();
            $usersubscriptionBranch = UserSubscription::whereIn('user_id', $salonIds)->get();
            $mainUserSubscription = UserSubscription::where('user_id',$id)->get();
            $userSubscription = $mainUserSubscription->merge($usersubscriptionBranch);
            return view('userSubscription.user-subscription.show', compact('userSubscription'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('usersubscription','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $usersubscription = UserSubscription::findOrFail($id);
            return view('userSubscription.user-subscription.edit', compact('usersubscription'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('usersubscription','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {

            $requestData = $request->all();

            $usersubscription = UserSubscription::findOrFail($id);
            $usersubscription->update($requestData);

            return redirect('userSubscription/user-subscription')->with('flash_message', 'UserSubscription updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('usersubscription','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            UserSubscription::destroy($id);

            return redirect('userSubscription/user-subscription')->with('flash_message', 'UserSubscription deleted!');
        }
        return response(view('403'), 403);

    }
}
