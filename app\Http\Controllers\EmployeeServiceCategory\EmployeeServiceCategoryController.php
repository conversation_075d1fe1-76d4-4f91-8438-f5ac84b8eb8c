<?php

namespace App\Http\Controllers\EmployeeServiceCategory;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\EmployeeServiceCategory;
use Illuminate\Http\Request;

class EmployeeServiceCategoryController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('employeeservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $employeeservicecategory = EmployeeServiceCategory::where('employee_service_id', 'LIKE', "%$keyword%")
                ->orWhere('service_category_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $employeeservicecategory = EmployeeServiceCategory::paginate($perPage);
            }

            return view('employeeServiceCategory.employee-service-category.index', compact('employeeservicecategory'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('employeeservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('employeeServiceCategory.employee-service-category.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('employeeservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            EmployeeServiceCategory::create($requestData);
            return redirect('employeeServiceCategory/employee-service-category')->with('flash_message', 'EmployeeServiceCategory added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('employeeservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $employeeservicecategory = EmployeeServiceCategory::findOrFail($id);
            return view('employeeServiceCategory.employee-service-category.show', compact('employeeservicecategory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('employeeservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $employeeservicecategory = EmployeeServiceCategory::findOrFail($id);
            return view('employeeServiceCategory.employee-service-category.edit', compact('employeeservicecategory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('employeeservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $employeeservicecategory = EmployeeServiceCategory::findOrFail($id);
             $employeeservicecategory->update($requestData);

             return redirect('employeeServiceCategory/employee-service-category')->with('flash_message', 'EmployeeServiceCategory updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('employeeservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            EmployeeServiceCategory::destroy($id);

            return redirect('employeeServiceCategory/employee-service-category')->with('flash_message', 'EmployeeServiceCategory deleted!');
        }
        return response(view('403'), 403);

    }
}
