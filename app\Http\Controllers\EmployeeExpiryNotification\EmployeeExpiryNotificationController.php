<?php

namespace App\Http\Controllers\EmployeeExpiryNotification;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\EmployeeExpiryNotification;
use Illuminate\Http\Request;

class EmployeeExpiryNotificationController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('employeeexpirynotification','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $employeeexpirynotification = EmployeeExpiryNotification::where('one_month_expiry', 'LIKE', "%$keyword%")
                ->orWhere('two_week_expiry', 'LIKE', "%$keyword%")
                ->orWhere('one_week_expiry', 'LIKE', "%$keyword%")
                ->orWhere('last_day_expiry', 'LIKE', "%$keyword%")
                ->orWhere('date_of_birth', 'LIKE', "%$keyword%")
                ->orWhere('one_month_expiry_status', 'LIKE', "%$keyword%")
                ->orWhere('two_week_expiry_status', 'LIKE', "%$keyword%")
                ->orWhere('one_week_expiry_status', 'LIKE', "%$keyword%")
                ->orWhere('last_day_expiry_status', 'LIKE', "%$keyword%")
                ->orWhere('date_of_birth_status', 'LIKE', "%$keyword%")
                ->orWhere('employee_id', 'LIKE', "%$keyword%")
                ->orWhere('salon_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $employeeexpirynotification = EmployeeExpiryNotification::paginate($perPage);
            }

            return view('employeeExpiryNotification.employee-expiry-notification.index', compact('employeeexpirynotification'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('employeeexpirynotification','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('employeeExpiryNotification.employee-expiry-notification.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('employeeexpirynotification','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            EmployeeExpiryNotification::create($requestData);
            return redirect('employeeExpiryNotification/employee-expiry-notification')->with('flash_message', 'EmployeeExpiryNotification added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('employeeexpirynotification','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $employeeexpirynotification = EmployeeExpiryNotification::findOrFail($id);
            return view('employeeExpiryNotification.employee-expiry-notification.show', compact('employeeexpirynotification'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('employeeexpirynotification','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $employeeexpirynotification = EmployeeExpiryNotification::findOrFail($id);
            return view('employeeExpiryNotification.employee-expiry-notification.edit', compact('employeeexpirynotification'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('employeeexpirynotification','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $employeeexpirynotification = EmployeeExpiryNotification::findOrFail($id);
             $employeeexpirynotification->update($requestData);

             return redirect('employeeExpiryNotification/employee-expiry-notification')->with('flash_message', 'EmployeeExpiryNotification updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('employeeexpirynotification','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            EmployeeExpiryNotification::destroy($id);

            return redirect('employeeExpiryNotification/employee-expiry-notification')->with('flash_message', 'EmployeeExpiryNotification deleted!');
        }
        return response(view('403'), 403);

    }
}
