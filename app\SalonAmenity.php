<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SalonAmenity extends Model
{
    use SoftDeletes;
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'salon_amenities';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['salon_id', 'amenity_id'];

    public function salon(){
        return $this->belongsTo(User::class,'salon_id');
    }
    public function amenity(){
        return $this->belongsTo(Amenity::class,'amenity_id')->withTrashed();
    }
}
