<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Expense extends Model
{
    use SoftDeletes;
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'expenses';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'date', 'expense_category_id', 'amount', 'vendor', 'total_amount', 'invoice_no', 'tax', 'attachments', 'is_recurring', 'time_cycle', 'start_date', 'end_date', 'description','saloon_id','purchase_order_id','amortization_date','product_inventory_id','is_deleted'];
    public function expenseCategory(){
        return $this->belongsTo(ExpenseCategory::class, 'expense_category_id');
    }
    public function calculateAmortizationForDate($date)
    {
        if ($this->amortization_date != null) {
            $amortizationDate = Carbon::parse($this->amortization_date);
            $createdDate = Carbon::parse($this->created_at);
            $providedDate = Carbon::parse($date);
            $totalAmount = $this->total_amount;

            // Ensure provided date does not exceed amortization_date
            if ($providedDate->greaterThan($amortizationDate)) {
                $providedDate = $amortizationDate;
            }

            // Calculate number of days from created_at to provided date
            $numberOfDays = $createdDate->diffInDays($providedDate);

            // Calculate total number of days for amortization
            $totalAmortizationDays = $createdDate->diffInDays($amortizationDate);

            // Avoid division by zero
            if ($totalAmortizationDays <= 0) {
                $amortizationPerDay = 0;
            } else {
                $amortizationPerDay = $totalAmount / $totalAmortizationDays;
            }

            // Calculate expense for the provided number of days
            $expense = $numberOfDays * $amortizationPerDay;

            return [
                'providedDate' => $providedDate->toDateString(),
                'numberOfDays' => $numberOfDays,
                'amortizationPerDay' => $amortizationPerDay,
                'expense' => $expense,
            ];
        } else {
            return null;
        }
    }
}
