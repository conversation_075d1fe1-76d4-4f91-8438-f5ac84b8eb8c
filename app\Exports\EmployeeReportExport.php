<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;  // <-- Added import

class EmployeeReportExport implements FromArray, WithStyles
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $excelData = [];

        // Main header
        $excelData[] = [$this->data['saloonName']];
        $excelData[] = [$this->data['saloonAddress']];
        $excelData[] = ['Date Created: ' . $this->data['dateCreated']];
        $excelData[] = ['Period: ' . $this->data['period']];
        $excelData[] = []; // empty row

        // Section header
        $excelData[] = ['Performance Statement'];
        $excelData[] = ['Product Consumption'];

        // Add product consuming data with totals per employee
        foreach ($this->data['finalData'] as $employeeName => $products) {
            $excelData[] = [$employeeName];
            $excelData[] = ['Product Name', 'Quantity', 'Amount'];

            $totalQty = 0;
            $totalAmt = 0;
            foreach ($products as $product) {
                $qty = $product['quantity'];
                $amt = $product['amount'];
                $excelData[] = [
                    $product['title'],
                    $qty,
                    $amt,  // <-- No 'SAR ' prefix, raw number
                ];
                $totalQty += $qty;
                $totalAmt += $amt;
            }

            // Total row for employee with raw numbers
            $excelData[] = [
                'Total',
                $totalQty,
                $totalAmt,
            ];
        }

        // Overall total for all product consumption
        $excelData[] = ['Total Product Consumption'];

        $allProductTotals = [];
        foreach ($this->data['finalData'] as $products) {
            foreach ($products as $product) {
                $title = $product['title'];
                if (!isset($allProductTotals[$title])) {
                    $allProductTotals[$title] = ['quantity' => 0, 'amount' => 0];
                }
                $allProductTotals[$title]['quantity'] += $product['quantity'];
                $allProductTotals[$title]['amount'] += $product['amount'];
            }
        }
        $grandQty = 0;
        $grandAmt = 0;

        $excelData[] = ['Total consumed Product Name', 'Total Quantity', 'Total Amount'];
        foreach ($allProductTotals as $title => $totals) {
            $excelData[] = [
                $title,
                $totals['quantity'],
                $totals['amount'],  // raw number
            ];
            $grandQty += $totals['quantity'];
            $grandAmt += $totals['amount'];
        }
        $excelData[] = [
            'Total',
            $grandQty,
            $grandAmt,
        ];

        // Appointment Section
        $excelData[] = [];
        $excelData[] = ['Appointment'];
        $excelData[] = ['Employee Name', 'Appointments Count', 'Total Earning Amount'];

        $totalAppointments = 0;
        $totalEarnings = 0;
        foreach ($this->data['employees'] as $employee) {
            $count = $employee->cutomerCountComplete;
            $earnings = $employee->cutomerPayments;
//            $count = $employee->employeeCompletedAppointments->count();
//            $earnings = $employee->employeeCompletedAppointments->pluck('appointmentPrice')
//                ->filter(function ($value) {
//                    return is_numeric($value);
//                })->sum();
            $totalAppointments += $count;
            $totalEarnings += $earnings;
            $excelData[] = [
                $employee->name,
                $count,
                $earnings,  // raw number here too
            ];
        }
        $excelData[] = [
            'Total',
            $totalAppointments,
            $totalEarnings,
        ];

        return $excelData;
    }

//$sheet->getStyle("A{$row}:C{$row}")->applyFromArray([
//    'font' => ['bold' => true, 'size' => 12, 'color' => ['rgb' => 'FFFFFF'], 'name' => 'Cairo'],
//    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '60497a']],
//    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
//]);

    public function styles(Worksheet $sheet)
    {
        $highestRow = $sheet->getHighestRow();
        $sheet->getStyle("C1:C{$highestRow}")
            ->getNumberFormat()
            ->setFormatCode('"SAR " #,##0.00');

        $sheet->getStyle('A1:C' . $highestRow)->applyFromArray([
            'font' => ['color' => ['rgb' => '000000'], 'name' => 'Cairo'],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'CCC0DA'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // Style main header (first row)
        $sheet->getStyle('A1:C1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 16, 'color' => ['rgb' => 'FFFFFF'], 'name' => 'Cairo'],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '60497a']],
        ]);
        $sheet->mergeCells('A1:C1');

        // Style date and period rows
        $sheet->getStyle('A2:A4')->applyFromArray([
            'font' => ['bold' => true, 'size' => 10, 'name' => 'Cairo'],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);

        // Style section headers
        $sectionHeaders = [5, 6, $highestRow - count($this->data['employees']) - 4, $highestRow - 2];
        foreach ($sectionHeaders as $row) {
            if ($row > 0 && $row <= $highestRow) {
                $sheet->getStyle("A{$row}:C{$row}")->applyFromArray([
                    'font' => ['color' => ['rgb' => '000000'], 'name' => 'Cairo'],
                    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
                ]);
//                $sheet->mergeCells("A{$row}:C{$row}");
            }
        }

        // Style employee names (bold purple)
        $row = 7;
        foreach ($this->data['finalData'] as $employeeName => $products) {
            $sheet->getStyle("A{$row}:C{$row}")->applyFromArray([
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF'], 'name' => 'Cairo'],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'b1a0c7'],
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            ]);
            $row += count($products) + 3;
        }

        // Style headers of product tables
//        $sheet->getStyle('A8:C8')->applyFromArray([
//            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
//            'fill' => [
//                'fillType' => Fill::FILL_SOLID,
//                'startColor' => ['rgb' => '403151'],
//            ],
//            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
//        ]);

        // Style totals rows in product consumption per employee in red bold and center align
        $row = 9;
        foreach ($this->data['finalData'] as $products) {
            $row += count($products);
            $sheet->getStyle("A{$row}:C{$row}")->applyFromArray([
                'font' => ['bold' => true, 'color' => ['rgb' => 'FF0000'], 'name' => 'Cairo'],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'ccc0da'],
                ],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            ]);
            $row += 3;
        }
        // Style overall total product consumption header and table header
        $totalProductRow = $row;
        $sheet->getStyle("A{$totalProductRow}:C{$totalProductRow}")->applyFromArray([
            'font' => ['color' => ['rgb' => '000000'], 'name' => 'Cairo'],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);
//        $sheet->mergeCells("A{$totalProductRow}:C{$totalProductRow}");

        $headerRow = $totalProductRow - 1;
        $sheet->getStyle("A{$headerRow}:C{$headerRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF'], 'name' => 'Cairo'],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '403151'],
            ],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);

        $headerRow = $totalProductRow - 2;
        $sheet->getStyle("A{$headerRow}:C{$headerRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF'], 'name' => 'Cairo'],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '60497a'],
            ],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);
//        Total Product Consumption ko merge kiya he
        $sheet->mergeCells("A{$headerRow}:C{$headerRow}");

        $sheet->getStyle("A" . ($totalProductRow + 1) . ":C" . ($totalProductRow + 1))->applyFromArray([
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);

        // Style grand total row red bold center align
        $grandTotalRow = $sheet->getHighestRow();

        $sheet->getStyle("A{$grandTotalRow}:C{$grandTotalRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FF0000'], 'name' => 'Cairo'],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);

        // Style appointment header and table headers
        $appointmentHeaderRow = $grandTotalRow + 2;
//        $sheet->getStyle("A{$appointmentHeaderRow}:C{$appointmentHeaderRow}")->applyFromArray([
//            'font' => ['bold' => true, 'color' => ['rgb' => '6E6A8A'], 'name' => 'Cairo'],
//            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'FF0000']],
//            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
//        ]);
//        $sheet->mergeCells("A{$appointmentHeaderRow}:C{$appointmentHeaderRow}");

        $sheet->getStyle("A" . ($appointmentHeaderRow + 1) . ":C" . ($appointmentHeaderRow + 1))->applyFromArray([
            'font' => ['bold' => true, 'name' => 'Cairo'],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);

        // Style appointment totals row in red bold center align
        $appointmentTotalRow = $sheet->getHighestRow();
        $sheet->getStyle("A{$appointmentTotalRow}:C{$appointmentTotalRow}")->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FF0000'], 'name' => 'Cairo'],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
        ]);

        // Set column widths
        $sheet->getColumnDimension('A')->setWidth(50);
        $sheet->getColumnDimension('B')->setWidth(50);
        $sheet->getColumnDimension('C')->setWidth(50);

        // Borders
        $sheet->getStyle('A1:C' . $sheet->getHighestRow())->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // --- Apply red background ONLY to header rows with Product Name and Appointment header ---
        for ($row = 1; $row <= $highestRow; $row++) {
            $valA = $sheet->getCell("A{$row}")->getValue();
            $valB = $sheet->getCell("B{$row}")->getValue();
            $valC = $sheet->getCell("C{$row}")->getValue();

            if ($valA === 'Product Name' && $valB === 'Quantity' && $valC === 'Amount') {
                $sheet->getStyle("A{$row}:C{$row}")->applyFromArray([
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => '403151'],
                    ],
                    'font' => [
                        'bold' => true,
                        'color' => ['rgb' => 'FFFFFF'],
                        'name' => 'Cairo',
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                ]);
            }
            if (trim($valA) === 'Performance Statement') {
                $sheet->mergeCells("A{$row}:C{$row}");  // Merge columns A se C tak
                $sheet->getStyle("A{$row}:C{$row}")->applyFromArray([
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => '60497a'],  // Sahi hex color
                    ],
                    'font' => [
                        'bold' => true,
                        'color' => ['rgb' => 'FFFFFF'],
                        'name' => 'Cairo',
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                ]);
            }
            if (trim($valA) === 'Product Consumption') {
                $sheet->mergeCells("A{$row}:C{$row}");  // Merge columns A se C tak
                $sheet->getStyle("A{$row}:C{$row}")->applyFromArray([
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => '60497a'],  // Sahi hex color
                    ],
                    'font' => [
                        'bold' => true,
                        'color' => ['rgb' => 'FFFFFF'],
                        'name' => 'Cairo',
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                ]);
            }
            if (trim($valA) === 'Appointment') {
                $prevRow = $row - 1;
                $sheet->getStyle("A{$prevRow}:C{$prevRow}")->applyFromArray([

                    'font' => [
                        'bold' => true,
                        'color' => ['rgb' => 'FF0000'],
                        'name' => 'Cairo',
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                ]);
            }


            if ($valA === 'Employee Name' && $valB === 'Appointments Count' && $valC === 'Total Earning Amount') {
                $prevRow = $row - 1;
                if ($prevRow > 0) {
                    $sheet->mergeCells("A{$prevRow}:C{$prevRow}");
                    $sheet->getStyle("A{$prevRow}:C{$prevRow}")->applyFromArray([
                        'fill' => [
                            'fillType' => Fill::FILL_SOLID,
                            'startColor' => ['rgb' => '60497a'],
                        ],
                        'font' => [
                            'bold' => true,
                            'color' => ['rgb' => 'FFFFFF'],
                            'name' => 'Cairo',
                        ],
                        'alignment' => [
                            'horizontal' => Alignment::HORIZONTAL_CENTER,
                            'vertical' => Alignment::VERTICAL_CENTER,
                        ],
                    ]);
                }

                $sheet->getStyle("A{$row}:C{$row}")->applyFromArray([
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => '403151'],
                    ],
                    'font' => [
                        'bold' => true,
                        'color' => ['rgb' => 'FFFFFF'],
                        'name' => 'Cairo',
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                ]);
            }
        }
    }
}
