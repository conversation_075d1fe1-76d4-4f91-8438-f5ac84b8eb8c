<?php

namespace App\Http\Controllers\Language;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Language;
use Illuminate\Support\Facades\Artisan;
use Cache;
use Illuminate\Support\Facades\Lang;
use Illuminate\Http\Request;

class LanguageController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('language','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25000000000;
            $lang = $request->get('lang', 'en'); // default to 'en' if 'lang' is not set
            if (in_array($lang, ['ar', 'en'])) {
                $data = include resource_path("lang/{$lang}/messages.php");
            } else {
                $data = [];
            }//end if else.
            $language = $data;
            return view('language.language.index', compact('language','lang'));
        }
        return response(view('403'), 403);
    }

    public function fetchData(Request $request)
    {
        $lang = $request->get('lang', 'en'); // default to 'en' if 'lang' is not set

        if (in_array($lang, ['ar', 'en'])) {
            $data = include resource_path("lang/{$lang}/messages.php");
        } else {
            $data = [];
        }

        return response()->json([
            'success' => true,
            'data' => $data,  // Return the translations as data
        ]);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('language','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('language.language.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('language','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            Language::create($requestData);
            return redirect('language/language')->with('flash_message', 'Language added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('language','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $language = Language::findOrFail($id);
            return view('language.language.show', compact('language'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('language','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $language = Language::findOrFail($id);
            return view('language.language.edit', compact('language'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('language','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $language = Language::findOrFail($id);
             $language->update($requestData);

             return redirect('language/language')->with('flash_message', 'Language updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('language','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Language::destroy($id);

            return redirect('language/language')->with('flash_message', 'Language deleted!');
        }
        return response(view('403'), 403);

    }
//    public function updateTranslations(Request $request) {
//        $lang = $request->get('lang', 'en');
//        $translations = $request->input('translations');
//        if (in_array($lang, ['ar', 'en']) && $translations) {
//            $filePath = resource_path("lang/{$lang}/messages.php");
//            $data = include $filePath;
//            foreach ($translations as $key => $value) {
//                $data[$key] = $value;
//            }
//            $content = "<?php\nreturn " . var_export($data, true) . ";\n";
//            file_put_contents($filePath, $content);
//            // Clear the cache
//            // Clear cache and config
//            Cache::forget("lang.{$lang}");
//            Artisan::call('config:clear');
//            Artisan::call('view:clear');
//            Artisan::call('cache:clear');
//            Lang::setLocale($lang);
//
//        }
//        return response()->json([
//            'success' => true,
//            'title' => 'Done',
//            'message' => 'Translations updated successfully',
//            'type' => 'success',
//        ]);
//    }
    public function updateTranslations(Request $request) {
        $lang = $request->get('lang', 'en');
        $translations = $request->input('translations');
        if (in_array($lang, ['ar', 'en']) && $translations) {
            $filePath = resource_path("lang/{$lang}/messages.php");
            $data = include $filePath;
            foreach ($translations as $key => $value) {
                // Use double quotes for values to ensure proper escaping
                $data[$key] = addslashes($value); // Ensure we escape special characters
            }
            // Format the array content with double quotes
            $content = "<?php\nreturn [\n";
            foreach ($data as $key => $value) {
                $content .= "    '{$key}' => \"{$value}\",\n";
            }
            $content .= "];\n";
            file_put_contents($filePath, $content);
            // Clear the cache
            Cache::forget("lang.{$lang}");
            Artisan::call('config:clear');
            Artisan::call('view:clear');
            Artisan::call('cache:clear');
            Lang::setLocale($lang);
        }
        return response()->json([
            'success' => true,
            'title' => 'Done',
            'message' => 'Translations updated successfully',
            'type' => 'success',
        ]);
    }

}
