<?php

namespace App\Http\Controllers\Ads;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use Storage;
use App\Ad;
use Illuminate\Http\Request;

class AdsController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('ads','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $ads = Ad::where('tittle', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->orWhere('link', 'LIKE', "%$keyword%")
                ->orWhere('status', 'LIKE', "%$keyword%")
                ->orWhere('picture', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $ads = Ad::paginate($perPage);
            }

            return view('ads.ads.index', compact('ads'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('ads','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('ads.ads.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('ads','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            if($request->hasFile('picture')){
                $picture = Storage::disk('website')->put('ads_picture', $request->picture);
                $requestData['picture'] = $picture;
            }else{
                $requestData['picture'] = '';
            }
            Ad::create($requestData);
            return redirect('ads/ads')->with('flash_message', 'Ad added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('ads','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $ad = Ad::findOrFail($id);
            return view('ads.ads.show', compact('ad'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('ads','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $ad = Ad::findOrFail($id);
            return view('ads.ads.edit', compact('ad'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('ads','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $ad = Ad::findOrFail($id);
            if($request->hasFile('picture')){
                $picture = Storage::disk('website')->put('ads_picture', $request->picture);
                $requestData['picture'] = $picture;
            }else{
                $requestData['picture'] = $ad->picture;
            }
            $ad->update($requestData);
            return redirect('ads/ads')->with('flash_message', 'Ad updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('ads','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Ad::destroy($id);

            return redirect('ads/ads')->with('flash_message', 'Ad deleted!');
        }
        return response(view('403'), 403);

    }
}
