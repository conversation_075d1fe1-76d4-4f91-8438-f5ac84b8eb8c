<?php

namespace App\Http\Controllers\CashierShuffleBranch;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\CashierShuffleBranch;
use Illuminate\Http\Request;

class CashierShuffleBranchController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('cashiershufflebranch','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $cashiershufflebranch = CashierShuffleBranch::where('cashier_id', 'LIKE', "%$keyword%")
                ->orWhere('old_branch_id', 'LIKE', "%$keyword%")
                ->orWhere('new_branch_id', 'LIKE', "%$keyword%")
                ->orWhere('status', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $cashiershufflebranch = CashierShuffleBranch::paginate($perPage);
            }

            return view('cashierShuffleBranch.cashier-shuffle-branch.index', compact('cashiershufflebranch'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('cashiershufflebranch','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('cashierShuffleBranch.cashier-shuffle-branch.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('cashiershufflebranch','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            CashierShuffleBranch::create($requestData);
            return redirect('cashierShuffleBranch/cashier-shuffle-branch')->with('flash_message', 'CashierShuffleBranch added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('cashiershufflebranch','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $cashiershufflebranch = CashierShuffleBranch::findOrFail($id);
            return view('cashierShuffleBranch.cashier-shuffle-branch.show', compact('cashiershufflebranch'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('cashiershufflebranch','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $cashiershufflebranch = CashierShuffleBranch::findOrFail($id);
            return view('cashierShuffleBranch.cashier-shuffle-branch.edit', compact('cashiershufflebranch'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('cashiershufflebranch','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $cashiershufflebranch = CashierShuffleBranch::findOrFail($id);
             $cashiershufflebranch->update($requestData);

             return redirect('cashierShuffleBranch/cashier-shuffle-branch')->with('flash_message', 'CashierShuffleBranch updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('cashiershufflebranch','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            CashierShuffleBranch::destroy($id);

            return redirect('cashierShuffleBranch/cashier-shuffle-branch')->with('flash_message', 'CashierShuffleBranch deleted!');
        }
        return response(view('403'), 403);

    }
}
