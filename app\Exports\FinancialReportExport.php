<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class FinancialReportExport implements FromArray, WithStyles
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $excelData = [];
        $excelData[] = [$this->data['saloonName'], 'Income Statement'];
        $excelData[] = [$this->data['saloonAddress']];
        $excelData[] = ['Date Created: ' . $this->data['dateCreated'], 'Date Issued: ' . $this->data['dateIssued']];
        $excelData[] = ['Period: ' . $this->data['period']];
        $excelData[] = ['Income Statement'];
        $excelData[] = ['Revenues'];
        $excelData[] = ['Online Client Revenue', $this->data['onlineClientRevenue']];
        $excelData[] = ['Walk In Client Revenue', $this->data['walkInClientRevenue']];
        $excelData[] = ['Other Income', $this->data['otherIncomes']];
        $excelData[] = ['Total Revenue', $this->data['totalRevenue']];
        $excelData[] = ['Service Types Revenue'];
        foreach ($this->data['servicesTypes'] as $serviceType) {
            $excelData[] = [$serviceType['name'], $serviceType['sum']];
        }
        $excelData[] = ['Service Types Total Revenue', $this->data['totalAmountOfServices']];
        $excelData[] = ['Expenses'];
        foreach ($this->data['expenses'] as $expenseCategoryName => $totalAmount) {
            $excelData[] = [$expenseCategoryName, $totalAmount];
        }
        $excelData[] = ['Total Expense', $this->data['totalExpense']];
        $excelData[] = ['Net Profit'];
        $excelData[] = ['Net Profit', $this->data['netProfit']];
        return $excelData;
    }
//    public function styles(Worksheet $sheet)
//    {
//        $sheet->getColumnDimension('A')->setWidth(40);
//        $sheet->getColumnDimension('B')->setWidth(40);
//        $sheet->mergeCells('A1:A1');
//        $sheet->mergeCells('B1:B1');
//        $sheet->getStyle('A1')->applyFromArray([
//            'font' => [
//                'bold' => true,
//                'size' => 18,
//                'color' => ['rgb' => 'FFFFFF']
//            ],
//            'alignment' => [
//                'horizontal' => Alignment::HORIZONTAL_LEFT,
//                'vertical' => Alignment::VERTICAL_CENTER
//            ],
//            'fill' => [
//                'fillType' => Fill::FILL_SOLID,
//                'startColor' => ['rgb' => '4F81BD']
//            ]
//        ]);
//        $sheet->getStyle('B1')->applyFromArray([
//            'font' => [
//                'bold' => true,
//                'size' => 18,
//                'color' => ['rgb' => 'FFFFFF']
//            ],
//            'alignment' => [
//                'horizontal' => Alignment::HORIZONTAL_RIGHT,
//                'vertical' => Alignment::VERTICAL_CENTER
//            ],
//            'fill' => [
//                'fillType' => Fill::FILL_SOLID,
//                'startColor' => ['rgb' => '4F81BD']
//            ]
//        ]);
//        $sheet->getStyle('A1:B1')->getBorders()->getBottom()->setBorderStyle(Border::BORDER_NONE);
//        $sheet->mergeCells('A2:B2');
//        $sheet->getStyle('A2:B2')->applyFromArray([
//            'borders' => [
//                'top' => ['borderStyle' => Border::BORDER_NONE],
//                'bottom' => ['borderStyle' => Border::BORDER_NONE],
//                'left' => ['borderStyle' => Border::BORDER_NONE],
//                'right' => ['borderStyle' => Border::BORDER_NONE],
//            ],
//            'font' => [
//                'bold' => false,
//                'size' => 12,
//                'color' => ['rgb' => 'FFFFFF']
//            ],
//            'alignment' => [
//                'horizontal' => Alignment::HORIZONTAL_LEFT
//            ],
//            'fill' => [
//                'fillType' => Fill::FILL_SOLID,
//                'startColor' => ['rgb' => '4F81BD']
//            ]
//        ]);
//        $startRow = 5;
//        $currentRow = $startRow;
//        $staticHeaders = [
//            'Income Statement' => ['A' . $currentRow . ':B' . $currentRow, 1],
//            'Revenues' => ['A' . ($currentRow + 1) . ':B' . ($currentRow + 1), 4], // 3 rows: Online, Walk-In, and Total Revenue
//        ];
//        $currentRow += 5;
//        $serviceTypesRow = $currentRow+1;
//        $staticHeaders['Service Types Revenue'] = ['A' . $serviceTypesRow . ':B' . $serviceTypesRow, count($this->data['servicesTypes']) + 1];
//        $currentRow += count($this->data['servicesTypes']) + 2; // 1 row for header, service type rows + empty row
//        $expensesRow = $currentRow +1;
//        $staticHeaders['Expenses'] = ['A' . $expensesRow . ':B' . $expensesRow, count($this->data['expenses']) + 1];
//        $currentRow += count($this->data['expenses']) + 2; // 1 row for header, expense rows + empty row
//        $netProfitRow = $currentRow+1;
//        $staticHeaders['Net Profit and Margins'] = ['A' . $netProfitRow . ':B' . $netProfitRow, 2];
//        foreach ($staticHeaders as $header => [$cellRange, $rowCount]) {
//            $sheet->mergeCells($cellRange);
//            $sheet->getStyle($cellRange)->applyFromArray([
//                'font' => [
//                    'bold' => true,
//                    'size' => 14,
//                    'color' => ['rgb' => 'FFFFFF']
//                ],
//                'fill' => [
//                    'fillType' => Fill::FILL_SOLID,
//                    'startColor' => ['rgb' => '4F81BD']
//                ],
//                'alignment' => [
//                    'horizontal' => Alignment::HORIZONTAL_LEFT
//                ]
//            ]);
//            $currentRow += $rowCount;
//        }
//        $subHeaders = [
//            'Total Revenue' => 9,  // Adjust for actual row where 'Total Revenue' appears
//            'Service Types Total Revenue' => $serviceTypesRow + count($this->data['servicesTypes'])+1,
//            'Total Expense' => $expensesRow + count($this->data['expenses'])+1,
//            'Net Profit' => $netProfitRow + 1,
////            'Net Margin' => $netProfitRow + 2,
//        ];
//        foreach ($subHeaders as $subHeader => $row) {
//            $cellRange = 'A' . $row . ':B' . $row;
//            $sheet->getStyle($cellRange)->applyFromArray([
//                'fill' => [
//                    'fillType' => Fill::FILL_SOLID,
//                    'startColor' => ['rgb' => '9fc5e8'],
//                ],
//                'font' => [
//                    'bold' => true
//                ],
//            ]);
//        }
//        $sheet->getStyle('A2:B' . $sheet->getHighestRow())->applyFromArray([
//            'borders' => [
//                'allBorders' => [
//                    'borderStyle' => Border::BORDER_THIN,
//                    'color' => ['rgb' => '000000']
//                ]
//            ]
//        ]);
//    }


    public function styles(Worksheet $sheet)
    {
        $sheet->getColumnDimension('A')->setWidth(40);
        $sheet->getColumnDimension('B')->setWidth(40);

        // Header styles
        $sheet->mergeCells('A1:A1');
        $sheet->mergeCells('B1:B1');
        $sheet->getStyle('A1')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 18,
                'color' => ['rgb' => 'FFFFFF']
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4F81BD']
            ]
        ]);
        $sheet->getStyle('B1')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 18,
                'color' => ['rgb' => 'FFFFFF']
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_RIGHT,
                'vertical' => Alignment::VERTICAL_CENTER
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4F81BD']
            ]
        ]);
        $sheet->getStyle('A1:B1')->getBorders()->getBottom()->setBorderStyle(Border::BORDER_NONE);

        $sheet->mergeCells('A2:B2');
        $sheet->getStyle('A2:B2')->applyFromArray([
            'borders' => [
                'top' => ['borderStyle' => Border::BORDER_NONE],
                'bottom' => ['borderStyle' => Border::BORDER_NONE],
                'left' => ['borderStyle' => Border::BORDER_NONE],
                'right' => ['borderStyle' => Border::BORDER_NONE],
            ],
            'font' => [
                'bold' => false,
                'size' => 12,
                'color' => ['rgb' => 'FFFFFF']
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4F81BD']
            ]
        ]);

        // Static headers
        $startRow = 5;
        $currentRow = $startRow;
        $staticHeaders = [
            'Income Statement' => ['A' . $currentRow . ':B' . $currentRow, 1],
            'Revenues' => ['A' . ($currentRow + 1) . ':B' . ($currentRow + 1), 4], // 3 rows: Online, Walk-In, and Total Revenue
        ];
        $currentRow += 5;
        $serviceTypesRow = $currentRow + 1;
        $staticHeaders['Service Types Revenue'] = ['A' . $serviceTypesRow . ':B' . $serviceTypesRow, count($this->data['servicesTypes']) + 1];
        $currentRow += count($this->data['servicesTypes']) + 2; // 1 row for header, service type rows + empty row
        $expensesRow = $currentRow + 1;
        $staticHeaders['Expenses'] = ['A' . $expensesRow . ':B' . $expensesRow, count($this->data['expenses']) + 1];
        $currentRow += count($this->data['expenses']) + 2; // 1 row for header, expense rows + empty row
        $netProfitRow = $currentRow + 1;
        $staticHeaders['Net Profit and Margins'] = ['A' . $netProfitRow . ':B' . $netProfitRow, 2];

        foreach ($staticHeaders as $header => [$cellRange, $rowCount]) {
            $sheet->mergeCells($cellRange);
            $sheet->getStyle($cellRange)->applyFromArray([
                'font' => [
                    'bold' => true,
                    'size' => 14,
                    'color' => ['rgb' => 'FFFFFF']
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4F81BD']
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_LEFT
                ]
            ]);
            $currentRow += $rowCount;
        }

        // Subheaders for totals
        $subHeaders = [
            'Total Revenue' => 9,  // Adjust for actual row where 'Total Revenue' appears
            'Service Types Total Revenue' => $serviceTypesRow + count($this->data['servicesTypes']) + 1,
            'Total Expense' => $expensesRow + count($this->data['expenses']) + 1,
            'Net Profit' => $netProfitRow + 1,
        ];
        foreach ($subHeaders as $subHeader => $row) {
            $cellRange = 'A' . $row . ':B' . $row;
            $sheet->getStyle($cellRange)->applyFromArray([
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '9fc5e8'],
                ],
                'font' => [
                    'bold' => true
                ],
            ]);
        }

        // Add border to entire table
        $sheet->getStyle('A2:B' . $sheet->getHighestRow())->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);

        // Apply number formatting to numeric values in column B with currency SAR
        $highestRow = $sheet->getHighestRow();
        for ($row = 1; $row <= $highestRow; $row++) {
            $cellValue = $sheet->getCell('B' . $row)->getValue();
            if (is_numeric($cellValue)) {
                $sheet->getStyle('B' . $row)
                    ->getNumberFormat()
                    ->setFormatCode('"SAR" #,##0.00'); // Format as SAR 1,234.56
            }
        }
    }



}




