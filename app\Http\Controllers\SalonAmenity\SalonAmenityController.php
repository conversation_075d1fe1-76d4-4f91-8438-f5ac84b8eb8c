<?php

namespace App\Http\Controllers\SalonAmenity;

use App\Amenity;
use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\User;
use App\SalonAmenity;
use Illuminate\Http\Request;
use Auth;
use Storage;

class SalonAmenityController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('salonamenity','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25000;
            if (!empty($keyword)) {
                $salonamenity = SalonAmenity::where('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('amenity_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                if(Auth::user()->roles[0]->name=="spa_salon"){
                    $salonamenity = SalonAmenity::where('salon_id',Auth::id())->orderBy('id','DESC')->paginate($perPage);
                    $amenitiesIds = $salonamenity->pluck('amenity_id')->toArray();
//                $amenities = Amenity::where('status',1)->where('salon_id',Auth::id())->get();
//                    $amenities = Amenity::whereNotIn('id',$amenitiesIds)->orderBy('id','DESC')->get();
                    $amenities = Amenity::where('salon_id', Auth::id())
                        ->orWhere('salon_id', null)
                        ->orderBy('id', 'DESC')
                        ->get();
                    $amenities = $amenities->whereNotIn('id', $amenitiesIds);
                }else{
                    $salonamenity = SalonAmenity::orderBy('id','DESC')->paginate($perPage);
//                $amenities = Amenity::where('status',1)->where('salon_id',Auth::id())->get();
                    $amenities = Amenity::where('status',1)->orderBy('id','DESC')->get();
                }
            }
            return view('salonAmenity.salon-amenity.index', compact('salonamenity','amenities'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('salonamenity','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('salonAmenity.salon-amenity.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('salonamenity','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            extract($request->all());
            if($request->other_amenity){
                $branches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                if($request->hasFile('other_amenity_image')){
                    $image = Storage::disk('website')->put('amenity_picture', $request->other_amenity_image);
                }
                foreach ($branches as $Key => $branch){
                    $amenity_id = Amenity::create(['title'=>$request->other_amenity,'image'=>$image,'salon_id'=>$branch->id])->id;
                    $amenity = SalonAmenity::create(['salon_id'=>$branch->id, 'amenity_id'=>$amenity_id]);
                }
            }else{
                $branches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                if (isset($branches)){
                    foreach ($branches as $key => $branch){
                        $amenity = SalonAmenity::create(['salon_id'=>$branch->id, 'amenity_id'=>$amenity_id]);
                    }
                }
                if(Auth::user()->tour_status == 4){
                    $salon = User::where('id',Auth::user()->id)->update(['tour_status'=>5]);
                }
            }
            return redirect('salonAmenity/salon-amenity')->with('flash_message', 'Amenity added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('salonamenity','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $salonamenity = SalonAmenity::findOrFail($id);
            return view('salonAmenity.salon-amenity.show', compact('salonamenity'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('salonamenity','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $salonamenity = SalonAmenity::findOrFail($id);
            return view('salonAmenity.salon-amenity.edit', compact('salonamenity'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('salonamenity','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $salonamenity = SalonAmenity::findOrFail($id);
             $salonamenity->update($requestData);

             return redirect('salonAmenity/salon-amenity')->with('flash_message', 'SalonAmenity updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('salonamenity','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            SalonAmenity::destroy($id);

            return redirect('salonAmenity/salon-amenity')->with('flash_message', 'SalonAmenity deleted!');
        }
        return response(view('403'), 403);

    }
}
