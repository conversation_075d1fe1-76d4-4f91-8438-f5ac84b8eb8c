<?php

namespace App\Http\Controllers\ServiceCategory;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use Storage;
use Auth;
use App\ServiceCategory;
use Illuminate\Http\Request;

class ServiceCategoryController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('servicecategory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 250000000;

            if (!empty($keyword)) {
                $servicecategory = ServiceCategory::where('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('name', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->orWhere('picture', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                // $servicecategory = ServiceCategory::paginate($perPage);
                $servicecategory = ServiceCategory::orderBy('id','DESC')->paginate($perPage);
            }

            return view('serviceCategory.service-category.index', compact('servicecategory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('servicecategory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('serviceCategory.service-category.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {   
        $model = str_slug('servicecategory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            extract($request->all());
            if($request->hasFile('picture')){
                $picture = Storage::disk('website')->put('service_category_picture', $request->picture);
            }else{
                $picture = '';
            }
            ServiceCategory::create(['name'=>$name,'picture'=>$picture,'description'=>$description]);
            return redirect('serviceCategory/service-category')->with('flash_message', 'ServiceCategory added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('servicecategory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $servicecategory = ServiceCategory::findOrFail($id);
            return view('serviceCategory.service-category.show', compact('servicecategory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('servicecategory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $servicecategory = ServiceCategory::findOrFail($id);
            return view('serviceCategory.service-category.edit', compact('servicecategory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('servicecategory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            $servicecategory = ServiceCategory::findOrFail($id);
            if($request->hasFile('picture')){
                $picture = Storage::disk('website')->put('service_category_picture', $request->picture);
                Storage::disk('website')->delete($servicecategory->picture);
            }else{
                $picture  = $servicecategory->picture;
            }
            $requestData['picture'] = $picture;
            $servicecategory->update($requestData);
//             return redirect('serviceCategory/service-category')->with('flash_message', 'ServiceCategory updated!');
            return back()->with('flash_message', 'ServiceCategory updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('servicecategory','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            ServiceCategory::destroy($id);

            return redirect('serviceCategory/service-category')->with('flash_message', 'ServiceCategory deleted!');
        }
        return response(view('403'), 403);

    }
}
