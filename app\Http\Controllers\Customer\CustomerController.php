<?php

namespace App\Http\Controllers\Customer;

use App\CustomerSlot;
use App\CustomNotification;
use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Customer;
use App\User;
use App\Profile;
use App\ProductCategory;
use App\Employee;
use App\Product;
use App\CustomerServiceCategory;
use App\AssignedCustomer;
use App\CustomerProductCategory;
use App\CustomerType;
use App\CustomerAppointment;
use App\UserSubscription;
use App\CustomerProduct;
use App\CustomerService;
use App\ServiceCategory;
use Auth;
use Mail;
use App\SalonService;
use Illuminate\Http\Request;

class CustomerController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('customer','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25000;
            if(Auth::user()->hasRole('spa_salon')){
                if (!empty($keyword)) {
                    $customer = User::where('salon_id', 'LIKE', "%$keyword%")
                    ->orWhere('first_name', 'LIKE', "%$keyword%")
                    ->orWhere('last_name', 'LIKE', "%$keyword%")
                    ->orWhere('email', 'LIKE', "%$keyword%")
                    ->orWhere('password', 'LIKE', "%$keyword%")
                    ->paginate($perPage);
                    $services = SalonService::get();
                } else {
                    $branches = User::whereHas('roles', function ($query) {
                        $query->where('name', 'spa_salon');
                    })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                    $branchIds = $branches->pluck('id');
                    $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                        ->orderBy('user_id', 'ASC')
                        ->orderBy('id', 'DESC')
                        ->get()
                        ->groupBy('user_id');
                    $currentDate = now();
                    $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                        $dueDate = $subs->first()->fatoraPdf->due_date;
                        return $dueDate->isPast();
                    })->keys();
                    $user = Auth::user();
                    $customer_ids = CustomerAppointment::whereIn('salon_id', $branchIds)->whereNotIn('salon_id',$expiredUserIds)->pluck('customer_id');
                    $companyCustomer = User::whereHas(
                        'roles', function($q){
                        $q->where('name', 'customer');
                    }
                    )->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->get();
                    $appointmentCustomer = User::whereIn('id', $customer_ids)->get();
                    $customer = $companyCustomer->merge($appointmentCustomer)->sortByDesc('id')->values();
                    $employee = User::whereHas(
                        'roles', function($q){
                            $q->where('name', 'employee');
                        }
                    )->where('salon_id',$user->id)->orderBy('id','DESC')->get();
                    $customer_types = CustomerType::get();
                    $categories = ServiceCategory::whereIn('id',$user->allEmployeeServiceCategoryIds)->orderBy('id', 'DESC')->get();
                    $productCategory = ServiceCategory::whereIn('id',$user->allEmployeeProductCategoryIds)->orderBy('id', 'DESC')->get();
                    $services = SalonService::whereIn('id',$user->allEmployeeServiceIds)->get();
                    $products = Product::where('salon_id', $user->id)->where('product_type_id',1)->get();
                    $userSubscription = UserSubscription::where('user_id',$user->id)->orderBy('id','DESC')->first();
                    return view('customer.customer.index', compact('customer','services','user','products', 'productCategory','employee','customer_types','categories','userSubscription','branches'));
                }
            } elseif(Auth::user()->hasRole('cashier')){
                if (!empty($keyword)) {
                    $customer = User::where('salon_id', 'LIKE', "%$keyword%")
                    ->orWhere('first_name', 'LIKE', "%$keyword%")
                    ->orWhere('last_name', 'LIKE', "%$keyword%")
                    ->orWhere('email', 'LIKE', "%$keyword%")
                    ->orWhere('password', 'LIKE', "%$keyword%")
                    ->paginate($perPage);
                    $services = SalonService::get();
                } else {
                    $user = Auth::user();
                    $customer_ids = CustomerAppointment::where('salon_id', $user->salon_id)->pluck('customer_id');
                    $customer = User::whereIn('id', $customer_ids)->orderBy('id','DESC')->paginate($perPage);
                    $salon = User::findOrFail($user->salon_id);
                    $customer_types = CustomerType::get();
                    $employee = User::whereHas(
                        'roles', function($q){
                            $q->where('name', 'employee');
                        }
                    )->where('salon_id',$user->salon_id)->orderBy('id','DESC')->get();
                    $categories = ServiceCategory::whereIn('id',$user->salon->allEmployeeServiceCategoryIds)->orderBy('id', 'DESC')->get();
                    $productCategory = ServiceCategory::whereIn('id',$user->salon->allEmployeeProductCategoryIds)->orderBy('id', 'DESC')->get();
                    $services = SalonService::whereIn('id',$user->salon->allEmployeeServiceIds)->get();
                    $products = Product::where('salon_id', $user->salon->id)->where('product_type_id',1)->get();
                    $userSubscription = UserSubscription::where('user_id',$user->id)->orderBy('id','DESC')->first();
                    return view('customer.customer.index', compact('customer','services','salon','user','products', 'productCategory','employee','customer_types','categories','userSubscription'));
                }
            }else{
                return response(view('403'), 403);
            }
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('customer','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('customer.customer.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */

    public function store(Request $request){
        $model = str_slug('customer','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            extract($request->all());
            $requestEmail = $request->input('email');
            $requestPhone = $request->input('phone');
            $validatedData = $request->validate([
                'email' => 'nullable|email|max:255',
                'phone' => 'nullable|string|max:20',
            ]);
            $user = User::where(function($query) use ($requestEmail, $requestPhone) {
                if ($requestEmail) {
                    $query->where('email', $requestEmail);
                }
                if ($requestPhone) {
                    $query->orWhere('phone', $requestPhone);
                }
            })->first();
            if ($user == null) {
                $random_password = rand('********','********');
                $phone = $request->phone;
                if ($phone && strpos($phone, '+966') !== 0) {
                    $phone = '+966' . $phone;
                }
                $names = explode(' ', $name, 2);
                $first_name = $names[0];
                $last_name = count($names) > 1 ? $names[1] : '';
                $user = User::create(['name'=>$name,'first_name'=>$first_name,'last_name'=>$last_name,'email'=>$email,'password'=>bcrypt($random_password), 'customer_type_id'=>1,'phone'=>$phone,'salon_id'=>Auth::user()->salon_id]);
                Profile::create(['user_id'=>$user->id,'address'=>$address,'pic'=>'no_avatar.jpg', 'phone'=>$request->phone,'dob'=>$dob, 'latitude'=>$lat,'longitude'=>$lng, 'city'=>$city,'state'=>$state,'postal'=>$zip_code,'country'=>$country,'pic'=>'no_avatar.jpg']);
                $user->roles()->attach([1 => ['role_id' =>5,'user_id' => $user->id]]);
                if (is_array($request->category_id) && is_array($request->customer_service_id) && is_array($request->customer_slot_id)) {
                    $appointment = CustomerAppointment::create(['customer_id'=>$user->id,'customer_appointment_date'=>$customer_appointment_date,'customer_slot_id'=>json_encode($customer_slot_id),'status'=>'Approved', 'salon_id'=>$salon_id]);
                    $assigned = AssignedCustomer::create(['salon_id'=>$salon_id,'appointment_id'=>$appointment->id,'customer_id'=>$user->id,'assigned_user_id'=>Auth::id(),'employee_id'=>$employee_id]);
                    if (is_array($request->customer_slot_id)) {
                        foreach ($request->customer_slot_id as $value) {
                            $CustomerSlot = CustomerSlot::create(['date'=>$customer_appointment_date,'salon_id'=>$salon_id,'employee_id'=>$employee_id,'slot_id'=>$value, 'appointment_id'=>$appointment->id,'status'=>'Pending']);
                        }
                    }
                    if (is_array($request->customer_service_id)) {
                        foreach ($request->customer_service_id as $value) {
                            $CustomerService = CustomerService::create(['customer_id'=>$user->id,'salon_service_id'=>$value, 'appointment_id'=>$appointment->id]);
                        }
                    }
                    if (is_array($request->category_id)) {
                        foreach ($request->category_id as $value) {
                            CustomerServiceCategory::create(['customer_service_id'=>$CustomerService->id,'service_category_id'=>$value, 'appointment_id'=>$appointment->id]);
                        }
                    }
                    if (is_array($request->customer_product_id)) {
                        foreach ($request->customer_product_id as $value) {
                            $CustomerProduct = CustomerProduct::create(['customer_id'=>$user->id,'salon_product_id'=>$value, 'appointment_id'=>$appointment->id]);
                        }
                    }
                    if (is_array($request->product_category_id)) {
                        foreach ($request->product_category_id as $value) {
                            CustomerProductCategory::create(['customer_product_id'=>$CustomerProduct->id,'product_category_id'=>$value, 'appointment_id'=>$appointment->id]);
                        }
                    }
                    try{
                        $name= $request->name;
                        $email = $request->email;
                        $password = $random_password;
                        $employee = $assigned->employee->name;
                        $salon = User::findOrFail($salon_id);
                        $salon_picture = $salon->profile->pic;
                        $salon_name = $salon->name;
                        $data = array(
                            'user_id'             => $user->id,
                            'name' => $name ,
                            'email' => $email,
                            'password' => $password,
                            'employee' => $employee,
                            'salon_picture' =>$salon_picture,
                            'salon_name' =>$salon_name,
                            'appointment_date'    => $customer_appointment_date,
                            'welcome_message' => 'Welcome',
                            'information_message' => 'Account Registration successful',
                            'detail' => env('APP_URL'),
                            'login_url' => env('APP_URL'),
                            'site_url' => env('APP_URL'),
                            'type'                => 'CustomerRegisterEmployee',
                            'template'            => 'CustomerEmployee',
                        );
                        $custom = CustomNotification::create(
                            [
                                'notifiable_id'   => $salon->id,
                                'notifiable_type' => 'App\User',
                                'type'            => 'CustomerRegister',
                                'data'            => $data,
                            ]
                        );
                        $result = Mail::send('website.email_templates.appointment_walkin_customer_email',['data'=>$data],function($message) use($data){
                            $message->to($data['email'], $data['name'])->bcc('<EMAIL>', 'Usman Dev')->subject('Salon Registration successful');
                        });
                    }catch(\Exception $e){
                        return back()->with(['message'=>'Your Creation successfully, but unable to send email.......','type'=>'error','title'=>'Fail']);
                    }
                }else{
                    try{
                        $name= $request->name;
                        $email = $request->email;
                        $password = $random_password;
                        $salon = User::findOrFail($salon_id);
                        $salon_picture = $salon->profile->pic;
                        $salon_name = $salon->name;
                        $data = array(
                            'name' => $name ,
                            'email' => $email,
                            'password' => $password,
                            'salon_picture' =>$salon_picture,
                            'salon_name' =>$salon_name,
                            'welcome_message' => 'Welcome',
                            'information_message' => 'Account Registration successful',
                            'detail' => env('APP_URL'),
                            'login_url' => env('APP_URL'),
                            'site_url' => env('APP_URL'),
                            'type'                => 'SalonCustomerRegister',
                            'template'            => 'SalonCustomer',
                        );
                        $custom = CustomNotification::create(
                            [
                                'notifiable_id'   => $salon->id,
                                'notifiable_type' => 'App\User',
                                'type'            => 'CustomerRegister',
                                'data'            => $data
                            ]
                        );
                        \Log::info('Customer Register Data ELSE: ', ['data'=>$data , 'CusstomerId' => $salon_id]);
                        $result = Mail::send('website.email_templates.salon_customer_email',['data'=>$data],function($message) use($data){
                            $message->to($data['email'], $data['name'])->bcc('<EMAIL>', 'Aftab Ali')->subject('Salon Registration successful');;
                        });
                    }catch(\Exception $e){
                        return back()->with(['message'=>'Your Creation successfully, but unable to send email.','type'=>'error','title'=>'Fail']);;
                        \Log::info('Customer Register Data Exception: ', ['data'=>$data , 'CusstomerId' => $salon_id , 'Exception'=>$e->getMessage()]);
                    }
                }
                \Log::info('Customer Register Data: ', ['data'=>$data , 'CusstomerId' => $salon_id]);
                return back()->with('flash_message', 'Customer added!');
            } else {
                if (is_array($request->category_id) && is_array($request->customer_service_id) && is_array($request->customer_slot_id)) {
                    $appointment = CustomerAppointment::create(['customer_id'=>$user->id,'customer_appointment_date'=>$customer_appointment_date,'customer_slot_id'=>json_encode($customer_slot_id),'status'=>'Approved', 'salon_id'=>$salon_id]);
                    $assigned = AssignedCustomer::create(['salon_id'=>$salon_id,'appointment_id'=>$appointment->id,'customer_id'=>$user->id,'assigned_user_id'=>Auth::id(),'employee_id'=>$employee_id]);
                    if (is_array($request->customer_slot_id)) {
                        foreach ($request->customer_slot_id as $value) {
                            $CustomerSlot = CustomerSlot::create(['date'=>$customer_appointment_date,'salon_id'=>$salon_id,'employee_id'=>$employee_id,'slot_id'=>$value, 'appointment_id'=>$appointment->id,'status'=>'Pending']);
                        }
                    }
                    if (is_array($request->customer_service_id)) {
                        foreach ($request->customer_service_id as $value) {
                            $CustomerService = CustomerService::create(['customer_id'=>$user->id,'salon_service_id'=>$value,'appointment_id'=>$appointment->id]);
                        }
                    }
                    if (is_array($request->category_id)) {
                        foreach ($request->category_id as $value) {
                            CustomerServiceCategory::create(['customer_service_id'=>$CustomerService->id,'service_category_id'=>$value, 'appointment_id'=>$appointment->id]);
                        }
                    }
                    if (is_array($request->customer_product_id)) {
                        foreach ($request->customer_product_id as $value) {
                            $CustomerProduct = CustomerProduct::create(['customer_id'=>$user->id,'salon_product_id'=>$value,'appointment_id'=>$appointment->id]);
                        }
                    }
                    if (is_array($request->product_category_id)) {
                        foreach ($request->product_category_id as $value) {
                            CustomerProductCategory::create(['customer_product_id'=>$CustomerProduct->id,'product_category_id'=>$value, 'appointment_id'=>$appointment->id]);
                        }
                    }
                }
                \Log::info('Talha' , ['salon id : ' => $salon_id??'zxc' , 'customer appointment date' => $salon->id??'zxc']);
                return back()->with('flash_message', 'Customer added!');
            }
        }
        return response(view('403'), 403);
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('customer','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $customer = User::findOrFail($id);
            return view('customer.customer.show', compact('customer'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('customer','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $customer = User::findOrFail($id);
            return view('customer.customer.edit', compact('customer'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('customer','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $customer = User::findOrFail($id);
             $customer->update($requestData);

             return redirect('customer/customer')->with('flash_message', 'Customer updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('customer','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            User::destroy($id);

            return redirect('customer/customer')->with('flash_message', 'Customer deleted!');
        }
        return response(view('403'), 403);

    }
}
