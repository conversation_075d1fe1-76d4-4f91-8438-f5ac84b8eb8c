<?php

namespace App\Http\Controllers\OffDate;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\OffDate;
use Illuminate\Http\Request;

class OffDateController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('offdate','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $offdate = OffDate::where('date', 'LIKE', "%$keyword%")
                ->orWhere('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('date_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $offdate = OffDate::paginate($perPage);
            }

            return view('offDate.off-date.index', compact('offdate'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('offdate','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('offDate.off-date.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('offdate','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            OffDate::create($requestData);
            return redirect('offDate/off-date')->with('flash_message', 'OffDate added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('offdate','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $offdate = OffDate::findOrFail($id);
            return view('offDate.off-date.show', compact('offdate'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('offdate','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $offdate = OffDate::findOrFail($id);
            return view('offDate.off-date.edit', compact('offdate'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('offdate','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $offdate = OffDate::findOrFail($id);
             $offdate->update($requestData);

             return redirect('offDate/off-date')->with('flash_message', 'OffDate updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('offdate','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            OffDate::destroy($id);

            return redirect('offDate/off-date')->with('flash_message', 'OffDate deleted!');
        }
        return response(view('403'), 403);

    }
}
