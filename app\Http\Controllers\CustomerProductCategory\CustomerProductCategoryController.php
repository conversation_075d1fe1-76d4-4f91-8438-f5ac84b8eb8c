<?php

namespace App\Http\Controllers\CustomerProductCategory;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\CustomerProductCategory;
use Illuminate\Http\Request;

class CustomerProductCategoryController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('customerproductcategory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $customerproductcategory = CustomerProductCategory::where('customer_product_id', 'LIKE', "%$keyword%")
                ->orWhere('product_category_id', 'LIKE', "%$keyword%")
                ->orWhere('appointment_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $customerproductcategory = CustomerProductCategory::paginate($perPage);
            }

            return view('customerProductCategory.customer-product-category.index', compact('customerproductcategory'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('customerproductcategory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('customerProductCategory.customer-product-category.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('customerproductcategory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            CustomerProductCategory::create($requestData);
            return redirect('customerProductCategory/customer-product-category')->with('flash_message', 'CustomerProductCategory added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('customerproductcategory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $customerproductcategory = CustomerProductCategory::findOrFail($id);
            return view('customerProductCategory.customer-product-category.show', compact('customerproductcategory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('customerproductcategory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $customerproductcategory = CustomerProductCategory::findOrFail($id);
            return view('customerProductCategory.customer-product-category.edit', compact('customerproductcategory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('customerproductcategory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $customerproductcategory = CustomerProductCategory::findOrFail($id);
             $customerproductcategory->update($requestData);

             return redirect('customerProductCategory/customer-product-category')->with('flash_message', 'CustomerProductCategory updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('customerproductcategory','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            CustomerProductCategory::destroy($id);

            return redirect('customerProductCategory/customer-product-category')->with('flash_message', 'CustomerProductCategory deleted!');
        }
        return response(view('403'), 403);

    }
}
