<?php

namespace App\Http\Controllers\Slot;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Slot;
use Illuminate\Http\Request;

class SlotController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('slot','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $slot = Slot::where('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('start_time', 'LIKE', "%$keyword%")
                ->orWhere('end_time', 'LIKE', "%$keyword%")
                ->orWhere('break_start', 'LIKE', "%$keyword%")
                ->orWhere('break_end', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $slot = Slot::paginate($perPage);
            }

            return view('slot.slot.index', compact('slot'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('slot','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('slot.slot.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('slot','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            Slot::create($requestData);
            return redirect('slot/slot')->with('flash_message', 'Slot added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('slot','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $slot = Slot::findOrFail($id);
            return view('slot.slot.show', compact('slot'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('slot','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $slot = Slot::findOrFail($id);
            return view('slot.slot.edit', compact('slot'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('slot','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $slot = Slot::findOrFail($id);
             $slot->update($requestData);

             return redirect('slot/slot')->with('flash_message', 'Slot updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('slot','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Slot::destroy($id);

            return redirect('slot/slot')->with('flash_message', 'Slot deleted!');
        }
        return response(view('403'), 403);

    }
}
