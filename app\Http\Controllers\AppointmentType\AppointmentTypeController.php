<?php

namespace App\Http\Controllers\AppointmentType;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\AppointmentType;
use Illuminate\Http\Request;

class AppointmentTypeController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('appointmenttype','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $appointmenttype = AppointmentType::where('name', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $appointmenttype = AppointmentType::paginate($perPage);
            }

            return view('appointmentType.appointment-type.index', compact('appointmenttype'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('appointmenttype','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('appointmentType.appointment-type.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('appointmenttype','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            AppointmentType::create($requestData);
            return redirect('appointmentType/appointment-type')->with('flash_message', 'AppointmentType added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('appointmenttype','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $appointmenttype = AppointmentType::findOrFail($id);
            return view('appointmentType.appointment-type.show', compact('appointmenttype'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('appointmenttype','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $appointmenttype = AppointmentType::findOrFail($id);
            return view('appointmentType.appointment-type.edit', compact('appointmenttype'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('appointmenttype','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $appointmenttype = AppointmentType::findOrFail($id);
             $appointmenttype->update($requestData);

             return redirect('appointmentType/appointment-type')->with('flash_message', 'AppointmentType updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('appointmenttype','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            AppointmentType::destroy($id);

            return redirect('appointmentType/appointment-type')->with('flash_message', 'AppointmentType deleted!');
        }
        return response(view('403'), 403);

    }
}
