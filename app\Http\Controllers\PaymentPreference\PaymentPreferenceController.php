<?php

namespace App\Http\Controllers\PaymentPreference;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\PaymentPreference;
use Illuminate\Http\Request;

class PaymentPreferenceController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('paymentpreference','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $paymentpreference = PaymentPreference::where('name', 'LIKE', "%$keyword%")
                ->orWhere('status', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $paymentpreference = PaymentPreference::paginate($perPage);
            }

            return view('paymentPreference.payment-preference.index', compact('paymentpreference'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('paymentpreference','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('paymentPreference.payment-preference.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('paymentpreference','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            PaymentPreference::create($requestData);
            return redirect('paymentPreference/payment-preference')->with('flash_message', 'PaymentPreference added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('paymentpreference','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $paymentpreference = PaymentPreference::findOrFail($id);
            return view('paymentPreference.payment-preference.show', compact('paymentpreference'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('paymentpreference','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $paymentpreference = PaymentPreference::findOrFail($id);
            return view('paymentPreference.payment-preference.edit', compact('paymentpreference'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('paymentpreference','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $paymentpreference = PaymentPreference::findOrFail($id);
             $paymentpreference->update($requestData);

             return redirect('paymentPreference/payment-preference')->with('flash_message', 'PaymentPreference updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('paymentpreference','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            PaymentPreference::destroy($id);

            return redirect('paymentPreference/payment-preference')->with('flash_message', 'PaymentPreference deleted!');
        }
        return response(view('403'), 403);

    }
}
