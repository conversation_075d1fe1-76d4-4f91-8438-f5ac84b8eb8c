<?php

namespace App\Http\Controllers\ProductInventory;

use App\Expense;
use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Revenue;
use App\StockOut;
use App\Product;
use App\ProductInventory;
use App\PurchaseOrder;
use App\Supplier;
use App\User;
use App\UserSubscription;
use Illuminate\Http\Request;
use Auth;
use Storage;

class ProductInventoryController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request,$slug,$Id)
    {
        $model = str_slug('productinventory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;
            if (!empty($keyword)) {
                $productinventory = ProductInventory::where('product_id', 'LIKE', "%$keyword%")
                ->orWhere('quantity', 'LIKE', "%$keyword%")
                ->orWhere('price', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                if(Auth::user()->hasRole('spa_salon')){
                    if($slug == "supplier"){
                        $supplierPurchaseOrder = ProductInventory::where("supplier_id",$Id)->orderBy('id','DESC')->get();
                        $slugName = Supplier::where('id',$Id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
                    }else{
                        $supplierPurchaseOrder = ProductInventory::where("product_id",$Id)->orderBy('id','DESC')->get();
                        $slugName = Product::where('id',$Id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
                    }
                }else{
                    if($slug == "supplier"){
                        $supplierPurchaseOrder = ProductInventory::where("supplier_id",$Id)->orderBy('id','DESC')->get();
                        $slugName = Supplier::where('id',$Id)->where('salon_id', Auth::user()->salon_id)->firstOrFail();
                    }else{
                        $supplierPurchaseOrder = ProductInventory::where("product_id",$Id)->orderBy('id','DESC')->get();
                        $slugName = Product::where('id',$Id)->where('salon_id', Auth::user()->salon_id)->firstOrFail();
                    }
                }
            }
            return view('productInventory.product-inventory.index', compact('supplierPurchaseOrder','slug','Id','slugName'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create(Request $request,$slug,$id)
    {
        $model = str_slug('productinventory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            if(Auth::user()->hasRole('spa_salon')){
                $allBranches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
                $branchIds = $allBranches->pluck('id');
                $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                    ->orderBy('user_id', 'ASC')
                    ->orderBy('id', 'DESC')
                    ->get()
                    ->groupBy('user_id');
                $currentDate = now();
                $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                    $dueDate = $subs->first()->fatoraPdf->due_date;
                    return $dueDate->isPast();
                })->keys();
                $suppliers = Supplier::whereIn('salon_id', $branchIds)->whereNotIn('salon_id', $expiredUserIds)->get();
                $products = Product::whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->get();
                if($slug=="supplier"){
                    $slugName = Supplier::where('id',$id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
                }else{
                    $slugName = Product::where('id',$id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
                }
            }else{
                $allBranches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
                $branchIds = $allBranches->pluck('id');
                $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                    ->orderBy('user_id', 'ASC')
                    ->orderBy('id', 'DESC')
                    ->get()
                    ->groupBy('user_id');
                $currentDate = now();
                $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                    $dueDate = $subs->first()->fatoraPdf->due_date;
                    return $dueDate->isPast();
                })->keys();
                $suppliers = Supplier::whereIn('salon_id', $branchIds)->whereNotIn('salon_id', $expiredUserIds)->get();
                $products = Product::whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->get();
                if($slug=="supplier"){
                    $slugName = Supplier::where('id',$id)->where('salon_id', Auth::user()->salon_id)->firstOrFail();
                }else{
                    $slugName = Product::where('id',$id)->where('salon_id', Auth::user()->salon_id)->firstOrFail();
                }
            }

            return view('productInventory.product-inventory.create',compact("suppliers","products","slug","id"));
        }
        return response(view('403'), 403);
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('productinventory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            extract($request->all());
            if ($request->hasFile('attachment')) {
                $attachment = Storage::disk('website')->put('purcahse_order', $request->attachment);
            } else {
                $attachment = (NULL);
            }
            if ($request->hasFile('attachment')) {
                $expenseAttachment = Storage::disk('website')->put('expense_attachments', $request->attachment);
            } else {
                $expenseAttachment = (NULL);
            }
            $productInventory= ProductInventory::create(['product_id'=>$product_id,'total_cost_with_vat'=>$total_cost_with_vat,'total_cost_without_vat'=>$total_cost_without_vat, 'quantity'=>$quantity,'price'=>$sale_price,'expiry_date'=>$expiry_date,'sku_id'=>$sku_id,'supplier_id'=>$supplier_id,'vat'=>$vat,'per_cost_price'=>$per_cost_price,'shelf'=>$shelf,'consumed_quantity'=>0,'attachment'=>$attachment]);
            $expense = Expense::create([
                'name' => 'Supplier Purchasing',
                'saloon_id' => Auth::user()->salon_id??"",
                'vendor' => Auth::user()->name??"",
                'expense_category_id' => '5',
                'amount' => $total_cost_without_vat??"",
                'tax' => $vat,
                'total_amount' => $total_cost_with_vat??"",
                'date' => date('Y-m-d'),
                'description' => "Supplier Purchasing",
                'attachments' => $expenseAttachment,
            ]);
            return redirect('productInventory/product-inventory/'.$slug.'='.$id)->with('flash_message', 'ProductInventory added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('productinventory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            return $id;
            return $productinventory = ProductInventory::findOrFail($id);
            return view('productInventory.product-inventory.show', compact('productinventory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('productinventory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $productinventory = ProductInventory::findOrFail($id);
            return view('productInventory.product-inventory.edit', compact('productinventory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('productinventory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $productinventory = ProductInventory::findOrFail($id);
             $productinventory->update($requestData);

             return redirect('productInventory/product-inventory')->with('flash_message', 'ProductInventory updated!');
        }
        return response(view('403'), 403);

    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('productinventory','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            ProductInventory::destroy($id);
            return redirect('productInventory/product-inventory')->with('flash_message', 'ProductInventory deleted!');
        }
        return response(view('403'), 403);

    }
    public function productRefundToSupplier(Request $request){
        $product = ProductInventory::findorfail($request->refund_id);
        $updatePriceWithVat = $product->per_cost_price * $product->quantity;
        $updatePriceWithOutVat = $updatePriceWithVat / (1 + ($product->vat / 100));
        $product->status = "refund";
        $product->consumed_quantity = $product->quantity;
        $product->quantity = 0;
        $product->save();
        $purchaseOrder = PurchaseOrder::create(['salon_id'=>Auth::user()->salon_id,'notes'=>'Refund','date'=>date('Y-m-d'),'status'=>'refund','total_quantity'=>$product->consumed_quantity,'vat'=>$product->vat,'total_amount_with_vat'=>$updatePriceWithVat,'total_amount_without_vat'=>$updatePriceWithOutVat]);
        StockOut::create([
            'product_id' => $product->product_id??"",
            'quantity' => $product->consumed_quantity??"",
            'price_per_product' => $product->price??"",
            'total_price_per_product' => $product->total_cost_with_vat??"",
            'cost_price_per_product'=>$product->total_cost_without_vat ??"",
            'salon_id' => Auth::user()->salon_id??"",
            'purchase_order_id' => $purchaseOrder->id??"",
            'sku_id'=>$product->sku_id??"",
        ]);
        if ($product->attachment != null) {
            $attachment = Storage::disk('website')->put('revenue_attachments', $product->attachment);
        } else {
            $attachment = (NULL);
        }
        Revenue::create([
            'saloon_id' => $purchaseOrder->salon->id??"",
            'purchase_order_id' => $purchaseOrder->id??"",
            'name' => $product->getSupplier->name??"",
            'email'=>$product->getSupplier->email??"",
            'total_amount' => $purchaseOrder->total_amount_with_vat??"",
            'tax' => $purchaseOrder->vat??"",
            'amount' => $purchaseOrder->total_amount_without_vat??"",
            'date' => date('Y-m-d'),
            'vendor' => $purchaseOrder->salon->name??"",
            'attachments' => $attachment,
        ]);
        return redirect('productInventory/product-inventory/'.$request->slug.'='.$request->id)->with('flash_message', 'ProductInventory Refund Successfully!');
    }
}
