<?php

namespace App\Http\Controllers\Blog;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Blog;
use Storage;
use Auth;
use Illuminate\Http\Request;

class BlogController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('blog','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 250000;

            if (!empty($keyword)) {
                $blog = Blog::where('title', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->orWhere('picture', 'LIKE', "%$keyword%")
                ->orWhere('created_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $blog = Blog::paginate($perPage);
            }

            return view('blog.blog.index', compact('blog'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('blog','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('blog.blog.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('blog','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            $requestData = $request->all();
            if($request->hasFile('picture')){
                $picture = Storage::disk('website')->put('blogs_picture', $request->picture);
                $requestData['picture'] = $picture;
            }else{
                $requestData['picture'] = '';
            }
            if($request->facebook_link){
                $facebook_link = $request->facebook_link;
            }else{
                $requestData['facebook_link'] = null;
            }
            if($request->instagram_link){
                $instagram_link = $request->instagram_link;
            }else{
                $requestData['instagram_link'] = null;
            }
            if($request->twitter_link){
                $twitter_link = $request->twitter_link;
            }else{
                $requestData['twitter_link'] = null;
            }
            if($request->tiktok_link){
                $tiktok_link = $request->tiktok_link;
            }else{
                $requestData['tiktok_link'] = null;
            }
            if($request->youtube_link){
                $youtube_link = $request->youtube_link;
            }else{
                $requestData['youtube_link'] = null;
            }
            $requestData['status'] = 1;
            Blog::create($requestData);
            return redirect('blog/blog')->with('flash_message', 'Blog added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('blog','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $blog = Blog::findOrFail($id);
            return view('blog.blog.show', compact('blog'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('blog','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $blog = Blog::findOrFail($id);
            return view('blog.blog.edit', compact('blog'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('blog','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $requestData = $request->all();
            $blog = Blog::findOrFail($id);
            if($request->hasFile('picture')){
                $picture = Storage::disk('website')->put('blogs_picture', $request->picture);
                $requestData['picture'] = $picture;
            }else{
                $requestData['picture'] = $blog->picture;
            }
            $blog->update($requestData);
            return redirect('blog/blog')->with('flash_message', 'Blog updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('blog','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Blog::destroy($id);

            return redirect('blog/blog')->with('flash_message', 'Blog deleted!');
        }
        return response(view('403'), 403);

    }
}
