<?php

namespace App\Http\Controllers;

use App\Permission;
use App\Role;
use App\User;
use Artisan;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Response;
use Illuminate\View\View;

class ProcessController extends Controller
{
    /**
     * Display generator.
     *
     * @return Response
     */
    public function getGenerator()
    {
        $roles = Role::all();
        return view('laravel-admin::generator',compact('roles'));
    }

    /**
     * Process generator.
     *
     * @return Response
     */
    public function postGenerator(Request $request)
    {
        //
    }
}
