<?php

namespace App\Http\Controllers\GuideSection;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use Storage;
use App\GuideSection;
use Illuminate\Http\Request;

class GuideSectionController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('guidesection','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $guidesection = GuideSection::where('tittle', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->orWhere('url', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $guidesection = GuideSection::paginate($perPage);
            }

            return view('guideSection.guide-section.index', compact('guidesection'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('guidesection','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('guideSection.guide-section.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('guidesection','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            GuideSection::create($requestData);
            return redirect('guideSection/guide-section')->with('flash_message', 'GuideSection added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('guidesection','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $guidesection = GuideSection::findOrFail($id);
            return view('guideSection.guide-section.show', compact('guidesection'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('guidesection','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $guidesection = GuideSection::findOrFail($id);
            return view('guideSection.guide-section.edit', compact('guidesection'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('guidesection','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $guidesection = GuideSection::findOrFail($id);
            if($request->hasFile('image')){
                $image = Storage::disk('website')->put('guide_section', $request->image);
            }else{
                $image = $guidesection->image;
            }
            $guidesection->update([
                'tittle'=>$request->tittle,
                'description'=>$request->description,
                'url'=>$request->url,
                'image'=>$image
            ]);


             return redirect('guideSection/guide-section')->with('flash_message', 'GuideSection updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('guidesection','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            GuideSection::destroy($id);

            return redirect('guideSection/guide-section')->with('flash_message', 'GuideSection deleted!');
        }
        return response(view('403'), 403);

    }
}
