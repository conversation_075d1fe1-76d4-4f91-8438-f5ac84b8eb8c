<?php

namespace App\Http\Controllers\PremiumAddonSalonCashier;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\PremiumAddonSalonCashier;
use Illuminate\Http\Request;

class PremiumAddonSalonCashierController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('premiumaddonsaloncashier','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $premiumaddonsaloncashier = PremiumAddonSalonCashier::where('primary_salon_id', 'LIKE', "%$keyword%")
                ->orWhere('premium_addon_id', 'LIKE', "%$keyword%")
                ->orWhere('payment_card_id', 'LIKE', "%$keyword%")
                ->orWhere('amount_captured', 'LIKE', "%$keyword%")
                ->orWhere('captured_status', 'LIKE', "%$keyword%")
                ->orWhere('currency', 'LIKE', "%$keyword%")
                ->orWhere('charge_id', 'LIKE', "%$keyword%")
                ->orWhere('customer_id', 'LIKE', "%$keyword%")
                ->orWhere('stripe_token', 'LIKE', "%$keyword%")
                ->orWhere('no_of_users', 'LIKE', "%$keyword%")
                ->orWhere('invoice_url', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $premiumaddonsaloncashier = PremiumAddonSalonCashier::paginate($perPage);
            }

            return view('premiumAddonSalonCashier.premium-addon-salon-cashier.index', compact('premiumaddonsaloncashier'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('premiumaddonsaloncashier','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('premiumAddonSalonCashier.premium-addon-salon-cashier.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('premiumaddonsaloncashier','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            PremiumAddonSalonCashier::create($requestData);
            return redirect('premiumAddonSalonCashier/premium-addon-salon-cashier')->with('flash_message', 'PremiumAddonSalonCashier added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('premiumaddonsaloncashier','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $premiumaddonsaloncashier = PremiumAddonSalonCashier::findOrFail($id);
            return view('premiumAddonSalonCashier.premium-addon-salon-cashier.show', compact('premiumaddonsaloncashier'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('premiumaddonsaloncashier','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $premiumaddonsaloncashier = PremiumAddonSalonCashier::findOrFail($id);
            return view('premiumAddonSalonCashier.premium-addon-salon-cashier.edit', compact('premiumaddonsaloncashier'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('premiumaddonsaloncashier','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $premiumaddonsaloncashier = PremiumAddonSalonCashier::findOrFail($id);
             $premiumaddonsaloncashier->update($requestData);

             return redirect('premiumAddonSalonCashier/premium-addon-salon-cashier')->with('flash_message', 'PremiumAddonSalonCashier updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('premiumaddonsaloncashier','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            PremiumAddonSalonCashier::destroy($id);

            return redirect('premiumAddonSalonCashier/premium-addon-salon-cashier')->with('flash_message', 'PremiumAddonSalonCashier deleted!');
        }
        return response(view('403'), 403);

    }
}
