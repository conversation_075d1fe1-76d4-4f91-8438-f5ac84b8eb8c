<?php

namespace App\Http\Controllers\Supplier;

use App\CustomNotification;
use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Product;
use App\ProductBrand;
use App\ProductInventory;
use App\Supplier;
use App\SupplierType;
use App\ProductSupplierType;
use App\User;
use App\Profile;
use App\UserSubscription;
use Illuminate\Http\Request;
use Auth;

class SupplierController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('supplier','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25000000;
            if (!empty($keyword)) {
                $supplier = Supplier::where('name', 'LIKE', "%$keyword%")
                ->orWhere('email', 'LIKE', "%$keyword%")
                ->orWhere('picture', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->orWhere('supplier_type_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                if(Auth::user()->hasRole('spa_salon')){
                    if ($request->branch_id != null){
                        if (in_array($request->branch_id, $this->getBranchIds())){
                            $branches = User::whereHas('roles', function ($query) {
                                $query->where('name', 'spa_salon');
                            })->where('id', $request->branch_id)->get();
                            $branchIds = $branches->pluck('id');
                            $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                                ->orderBy('user_id', 'ASC')
                                ->orderBy('id', 'DESC')
                                ->get()
                                ->groupBy('user_id');
                            $currentDate = now();
                            $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                                $dueDate = $subs->first()->fatoraPdf->due_date;
                                return $dueDate->isPast();
                            })->keys();
                            $suppliers = Supplier::whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->paginate($perPage);
                            $userSubscription = UserSubscription::where('user_id',$branchIds)->orderBy('id','DESC')->first();
                            $allBranches = User::whereHas('roles', function ($query) {
                                $query->where('name', 'spa_salon');
                            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                        }else{
                            return redirect()->back()->with([
                                'title' => 'Error',
                                'message' => 'Please Try Again',
                                'type' => 'error'
                            ]);
                        }
                    }else {
                        $allBranches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
                        $branchIds = $allBranches->pluck('id');
                        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                            ->orderBy('user_id', 'ASC')
                            ->orderBy('id', 'DESC')
                            ->get()
                            ->groupBy('user_id');
                        $currentDate = now();
                        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                            $dueDate = $subs->first()->fatoraPdf->due_date;
                            return $dueDate->isPast();
                        })->keys();
                        $suppliers = Supplier::whereIn('salon_id', $branchIds)->whereNotIn('salon_id', $expiredUserIds)->paginate($perPage);
                    }
                }else{
                    $allBranches = User::whereHas('roles', function ($query) {
                        $query->where('name', 'spa_salon');
                    })->where('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
                    $branchIds = $allBranches->pluck('id');
                    $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                        ->orderBy('user_id', 'ASC')
                        ->orderBy('id', 'DESC')
                        ->get()
                        ->groupBy('user_id');
                    $currentDate = now();
                    $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                        $dueDate = $subs->first()->fatoraPdf->due_date;
                        return $dueDate->isPast();
                    })->keys();
                    $suppliers = Supplier::whereIn('salon_id', $branchIds)->whereNotIn('salon_id', $expiredUserIds)->paginate($perPage);
                }
            }
            return view('supplier.supplier.index', compact('suppliers','allBranches','expiredUserIds'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('supplier','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            if(Auth::user()->hasRole('spa_salon')){
                $allBranches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
            }else{
                $allBranches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
            }
            return view('supplier.supplier.create',compact('allBranches'));
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('supplier','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            extract($request->all());
            $supplier = Supplier::create([
                "name"=>$name??"",
                "email"=>$email??"",
                "address"=>$address??"",
                "phone"=>$phone??"",
                "description"=>$description??"",
                "salon_id"=>$branch_id??"",
                "web_link"=>$web_link??"",
            ]);
            $data = [
                'supplierName'   => $supplier->name,
                'supplierEmail'  => $supplier->email,
                'salon_name'     => User::find($supplier->salon_id)->name??'',
                'type'           => 'newSupplierCreated',
            ];
            $custom = CustomNotification::create(
                [
                    'notifiable_id' => Auth::user()->salon_id??"",
                    'notifiable_type' => 'App\User',
                    'type' => 'InventoryProduct',
                    'data' => $data,
                ]
            );
            if ($supplier) {
                return redirect(url('supplier/supplier'))->with(['title'=>'Done','message'=>'supplier Added successfully','type'=>'success']);
            }
            else{
                return redirect(url('supplier/supplier'))->with(['title'=>'Fail','message'=>'Unable to add supplier','type'=>'error']);
            }

        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('supplier','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $supplier = Supplier::findOrFail($id);
            return view('supplier.supplier.show', compact('supplier'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {   
        $model = str_slug('supplier','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            if(Auth::user()->hasRole('spa_salon')){
                $supplier = Supplier::where('id',$id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
                $allBranches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
            }else{
                $supplier = Supplier::where('id',$id)->where('salon_id', Auth::user()->salon_id)->firstOrFail();
                $allBranches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
            }
            $supplierType = SupplierType::get();
            return view('supplier.supplier.edit', compact('supplier','supplierType','allBranches'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('supplier','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            extract( $request->all());
            $supplier = Supplier::findOrFail($id);
            $requestData = $request->all();
            $supplier->update($requestData);
//            $supplier = User::findOrFail($id);
//            $requestData = [
//                'name'=>$first_name.' '.$last_name,
//                'first_name'=>$first_name,
//                'last_name'=>$last_name,
//                'email'=>$email,
//                'description' =>$description
//            ];
//            $supplier->update($requestData);
//            if ($file = $request->file('picture')) {
//                $extension = $file->extension()?: 'png';
//                $destinationPath = public_path() . '/storage/uploads/users/';
//                $safeName = str_random(10) . '.' . $extension;
//                $file->move($destinationPath, $safeName);
//                $profile_picture = $safeName;
//            }else{
//                $profile_picture = $supplier->profile->pic;
//            }
//            $profile = [
//                'pic'=>$profile_picture,
//                'link'=>$request->link,
//            ];
//            Profile::where('user_id',$supplier->id)->update($profile);
//            ProductSupplierType::where('supplier_id',$supplier->id)->update(['supplier_type_id'=>$supplier_type_id]);
            return redirect('supplier/supplier')->with('flash_message', 'Supplier updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('supplier','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Supplier::destroy($id);

            return redirect('supplier/supplier')->with('flash_message', 'Supplier deleted!');
        }
        return response(view('403'), 403);

    }
    public function purchaseOrder($id)
    {
        $supplierPurchaseOrder=ProductInventory::where("supplier_id",$id)->get();
        return view("supplier/supplier/purchase_order",compact("supplierPurchaseOrder"));
    }

    public function addPurchase(Request $request){

        if ($request->branch_id != null){
            $branches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('id', $request->branch_id)->get();
            $branchIds = $branches->pluck('id');
            $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                ->orderBy('user_id', 'ASC')
                ->orderBy('id', 'DESC')
                ->get()
                ->groupBy('user_id');
            $currentDate = now();
            $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                $dueDate = $subs->first()->fatoraPdf->due_date;
                return $dueDate->isPast();
            })->keys();
            $suppliers = Supplier::whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->get();
            $products = Product::whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->get();

            $userSubscription = UserSubscription::where('user_id',$branchIds)->orderBy('id','DESC')->first();
            $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
        }else {
            $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
            $branchIds = $allBranches->pluck('id');
            $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                ->orderBy('user_id', 'ASC')
                ->orderBy('id', 'DESC')
                ->get()
                ->groupBy('user_id');
            $currentDate = now();
            $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                $dueDate = $subs->first()->fatoraPdf->due_date;
                return $dueDate->isPast();
            })->keys();
            $suppliers = Supplier::whereIn('salon_id', $branchIds)->whereNotIn('salon_id', $expiredUserIds)->get();
            $products = Product::whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->get();
        }
         return view("supplier/supplier/add_purchase",compact("suppliers","products"));
    }
}
