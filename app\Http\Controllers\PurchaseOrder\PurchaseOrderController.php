<?php

namespace App\Http\Controllers\PurchaseOrder;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\PurchaseOrder;
use Illuminate\Http\Request;

class PurchaseOrderController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('purchaseorder','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $purchaseorder = PurchaseOrder::where('user_id', 'LIKE', "%$keyword%")
                ->orWhere('employee_id', 'LIKE', "%$keyword%")
                ->orWhere('notes', 'LIKE', "%$keyword%")
                ->orWhere('date', 'LIKE', "%$keyword%")
                ->orWhere('total_amount_with_vat', 'LIKE', "%$keyword%")
                ->orWhere('total_amount_without_vat', 'LIKE', "%$keyword%")
                ->orWhere('vat', 'LIKE', "%$keyword%")
                ->orWhere('total_quantity', 'LIKE', "%$keyword%")
                ->orWhere('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('status', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $purchaseorder = PurchaseOrder::paginate($perPage);
            }

            return view('purchaseOrder.purchase-order.index', compact('purchaseorder'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('purchaseorder','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('purchaseOrder.purchase-order.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('purchaseorder','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            PurchaseOrder::create($requestData);
            return redirect('purchaseOrder/purchase-order')->with('flash_message', 'PurchaseOrder added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('purchaseorder','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $purchaseorder = PurchaseOrder::findOrFail($id);
            return view('purchaseOrder.purchase-order.show', compact('purchaseorder'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('purchaseorder','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $purchaseorder = PurchaseOrder::findOrFail($id);
            return view('purchaseOrder.purchase-order.edit', compact('purchaseorder'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('purchaseorder','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $purchaseorder = PurchaseOrder::findOrFail($id);
             $purchaseorder->update($requestData);

             return redirect('purchaseOrder/purchase-order')->with('flash_message', 'PurchaseOrder updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('purchaseorder','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            PurchaseOrder::destroy($id);

            return redirect('purchaseOrder/purchase-order')->with('flash_message', 'PurchaseOrder deleted!');
        }
        return response(view('403'), 403);

    }
}
