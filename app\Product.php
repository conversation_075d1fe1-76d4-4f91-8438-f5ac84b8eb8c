<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use SoftDeletes;
     protected $appends = ['productCurrentInventory'];
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'products';

    /**
    * The database primary key value.
    *
    * @var string
    */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['title', 'brand_id', 'description', 'product_category_id', 'product_type_id', 'salon_id', 'status','supplier_id','depreciation_date','is_deleted'];

    public function salon(){
        return $this->belongsTo(User::class,'salon_id');
    }
    public function productType(){
        return $this->belongsTo(ProductType::class,'product_type_id');
    }
    public function productImage(){
        return $this->hasOne(ProductImage::class,'product_id','id');
    }
    public function productImages(){
        return $this->hasMany(ProductImage::class,'product_id','id');
    }
    public function productStockOut(){
        return $this->hasMany(StockOut::class,'product_id','id');
    }


    public function productInventory(){
        return $this->hasOne(ProductInventory::class,'product_id');
    }
    public function productInventories(){
        return $this->hasMany(ProductInventory::class,'product_id')->where('is_deleted',0);
    }
    public function getProductCategory(){
        return $this->belongsTo(ProductCategory::class,'product_category_id');
    }
    public function getCategory(){
        return $this->belongsTo(ServiceCategory::class,'product_category_id')->withTrashed();
    }

    public function getSupplier(){
        return $this->belongsTo(Supplier::class,'supplier_id');
    }
    public function getproductCurrentInventoryAttribute(){
        $currentDate = now();
        return $this->productInventories()
            ->where('expiry_date', '>=', $currentDate)
            ->where('quantity', '>', 0)
            ->orderBy('expiry_date', 'asc')
            ->first();
    }
}
