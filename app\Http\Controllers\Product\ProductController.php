<?php

namespace App\Http\Controllers\Product;

use App\CustomNotification;
use App\Expense;
use App\Http\Controllers\Controller;
use App\ProductBrand;
use App\ProductInventory;
use App\Supplier;
use App\UserSubscription;
use Auth;
use App\ProductCategory;
use App\ServiceCategory;
use App\Product;
use App\ProductType;
use App\ProductImage;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;


class   ProductController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\RedirectResponse
     */

    public function index(Request $request)
    {
        $model = str_slug('product','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 2500000;
            if (!empty($keyword)) {
                $product = Product::where('title', 'LIKE', "%$keyword%")
                ->orWhere('price', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->orWhere('product_type_id', 'LIKE', "%$keyword%")
                ->orWhere('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('status', 'LIKE', "%$keyword%")
                ->where('salon_id', Auth::id())->paginate($perPage);
            } else {
                if(Auth::user()->hasRole('spa_salon')){
                    if ($request->branch_id != null){
                        if (in_array($request->branch_id, $this->getBranchIds())){
                            $branches = User::whereHas('roles', function ($query) {
                                $query->where('name', 'spa_salon');
                            })->where('id', $request->branch_id)->get();
                            $branchIds = $branches->pluck('id');
                            $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                                ->orderBy('user_id', 'ASC')
                                ->orderBy('id', 'DESC')
                                ->get()
                                ->groupBy('user_id');
                            $currentDate = now();
                            $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                                $dueDate = $subs->first()->fatoraPdf->due_date;
                                return $dueDate->isPast();
                            })->keys();
                            $products = Product::where('is_deleted','1')->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->paginate($perPage);
                            $userSubscription = UserSubscription::where('user_id',$branchIds)->orderBy('id','DESC')->first();
                            $allBranches = User::whereHas('roles', function ($query) {
                                $query->where('name', 'spa_salon');
                            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                        }else{
                            return redirect()->back()->with([
                                'title' => 'Error',
                                'message' => 'Please Try Again',
                                'type' => 'error'
                            ]);
                        }
                    }else{
                        $allBranches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                        $branchIds = $allBranches->pluck('id');
                        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                            ->orderBy('user_id', 'ASC')
                            ->orderBy('id', 'DESC')
                            ->get()
                            ->groupBy('user_id');
                        $currentDate = now();
                        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                            $dueDate = $subs->first()->fatoraPdf->due_date;
                            return $dueDate->isPast();
                        })->keys();
                        $products = Product::where('is_deleted','1')->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->paginate($perPage);
                    }
                }else{
                    $allBranches = User::whereHas('roles', function ($query) {
                        $query->where('name', 'spa_salon');
                    })->where('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                    $branchIds = $allBranches->pluck('id');
                    $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                        ->orderBy('user_id', 'ASC')
                        ->orderBy('id', 'DESC')
                        ->get()
                        ->groupBy('user_id');
                    $currentDate = now();
                    $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                        $dueDate = $subs->first()->fatoraPdf->due_date;
                        return $dueDate->isPast();
                    })->keys();
                    $products = Product::where('is_deleted','1')->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->paginate($perPage);
                }
            }

            return view('product.product.index', compact('products','allBranches','expiredUserIds'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('product','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            if(Auth::user()->hasRole('spa_salon')){
                $allBranches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                $branchIds = $allBranches->pluck('id');
                $productCategories = ServiceCategory::get();
                $brands= ProductBrand::get();
                $suppliers = Supplier::whereIn('salon_id',$branchIds)->get();
            }else{
                $allBranches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                $branchIds = $allBranches->pluck('id');
                $productCategories = ServiceCategory::get();
                $brands= ProductBrand::get();
                $suppliers = Supplier::whereIn('salon_id',$branchIds)->get();
            }
            return view('product.product.create', compact('brands','productCategories','suppliers','allBranches'));
        }
        return response(view('403'), 403);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('product','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            extract($request->all());
            if($request->product_type_id == "2"){
                $product = Product::create(["title"=>$product_name??"","product_category_id"=>$product_category??"","brand_id"=>$brand_id??"", "supplier_id"=>$supplier_id??"", "salon_id"=>$branch_id??"","description"=>$description,"product_type_id"=>$product_type_id]);
                if ($request->has('cropped_images')) {
                    if (is_array($request->cropped_images)) {
                        foreach ($request->cropped_images as $key => $base64Image) {
                            $imageData = str_replace('data:image/png;base64,', '', $base64Image);
                            $imageData = str_replace(' ', '+', $imageData);
                            $decodedImage = base64_decode($imageData);
                            $extension = 'png';
                            $imageName = Str::random(10) . '.' . $extension;
                            $destinationPath = 'product_image/';
                            $pic = Storage::disk('website')->put($destinationPath . $imageName, $decodedImage);
                            ProductImage::create([
                                'name' => $destinationPath . $imageName,
                                'product_id' => $product->id
                            ]);
                        }
                    }
                } else {
                    $pic = "arrow.png";
                }
                $productInventory = ProductInventory::create(['product_id'=>$product->id,'total_cost_with_vat'=>$total_cost_with_vat,'total_cost_without_vat'=>$total_cost_without_vat, 'quantity'=>$quantity,'per_cost_price'=>$per_cost_price,'price'=>$sale_price,'expiry_date'=>$expiry_date,'sku_id'=>$sku_id,'supplier_id'=>$supplier_id,'shelf'=>$shelf, 'vat'=>$vat]);
                $expense = Expense::create([
                    'name' => $product->title,
                    'product_inventory_id' => $productInventory->id,
                    'saloon_id' => $branch_id??"",
                    'vendor' => $productInventory->getSupplier->name??"",
                    'expense_category_id' => '5',
                    'amount' => $total_cost_without_vat??"",
                    'total_amount' => $total_cost_with_vat??"",
                    'tax' => $vat,
                    'date' => date('Y-m-d')??"",
                    'description' => $request->description??"",
                ]);
                if($productInventory && $expense){
                    $data = [
                        'productQuantity' => $productInventory->quantity,
                        'productSku' => $productInventory->sku_id,
                        'productTitle' => $expense->name,
                        'productAmount' => $expense->total_amount,
                        'type' => 'products',
                        'template' => 'addProducts',
                    ];
                }
                $custom = CustomNotification::create(
                    [
                        'notifiable_id' => Auth::user()->salon_id??"",
                        'notifiable_type' => 'App\User',
                        'type' => 'InventoryProduct',
                        'data' => $data,
                    ]
                );
                if ($product) {
                    return redirect(url('product/product'))->with(['title'=>'Done','message'=>'product Added successfully','type'=>'success']);
                }else{
                    return redirect(url('product/product'))->with(['title'=>'Fail','message'=>'Unable to add product','type'=>'error']);
                }
            }else{
                $product = Product::create(["title"=>$product_name??"","product_category_id"=>$product_category??"","brand_id"=>$brand_id??"", "supplier_id"=>$supplier_id??"",
                    "salon_id"=>$branch_id??"","description"=>$description,"product_type_id"=>$product_type_id,'depreciation_date'=>$depreciation_date]);
                if ($request->has('cropped_images')) {
                    if (is_array($request->cropped_images)) {
                        foreach ($request->cropped_images as $key => $base64Image) {
                            $imageData = str_replace('data:image/png;base64,', '', $base64Image);
                            $imageData = str_replace(' ', '+', $imageData);
                            $decodedImage = base64_decode($imageData);
                            $extension = 'png';
                            $imageName = Str::random(10) . '.' . $extension;
                            $destinationPath = 'product_image/';
                            $pic = Storage::disk('website')->put($destinationPath . $imageName, $decodedImage);
                            ProductImage::create([
                                'name' => $destinationPath . $imageName,
                                'product_id' => $product->id
                            ]);
                        }
                    }
                } else {
                    $pic = "arrow.png";
                }
                $productInventory = ProductInventory::create(['product_id'=>$product->id,'total_cost_with_vat'=>$total_cost_with_vat,'total_cost_without_vat'=>$total_cost_without_vat,
                    'quantity'=>$quantity,'per_cost_price'=>$per_cost_price,'price'=>$sale_price,'expiry_date'=>$expiry_date,'sku_id'=>$sku_id,'supplier_id'=>$supplier_id,'shelf'=>$shelf,
                    'vat'=>$vat,'status'=>'equipment']);
                $expense = Expense::create([
                    'name' => $product->title,
                    'product_inventory_id' => $productInventory->id,
                    'saloon_id' => $branch_id??"",
                    'vendor' => $productInventory->getSupplier->name??"",
                    'expense_category_id' => '5',
                    'amount' => $total_cost_without_vat??"",
                    'total_amount' => $total_cost_with_vat??"",
                    'tax' => $vat,
                    'date' => date('Y-m-d')??"",
                    'description' => $request->description??"",
                ]);
                $data = [
                    'productQuantity' => $productInventory->quantity,
                    'productSku' => $productInventory->sku_id,
                    'productAmount' => $expense->total_amount,
                    'vendor' => $productInventory->getSupplier->name??"",
                    'productTitle' => $expense->name,
                    'type' => 'products',
                    'template' => 'product_inventory'
                ];
                $custom = CustomNotification::create(
                    [
                        'notifiable_id' => Auth::user()->salon_id??"",
                        'notifiable_type' => 'App\User',
                        'type' => 'InventoryProduct',
                        'data' => $data,
                    ]
                );
                if ($product) {
                    return redirect(url('product/product'))->with(['title'=>'Done','message'=>'product Added successfully','type'=>'success']);
                }else{
                    return redirect(url('product/product'))->with(['title'=>'Fail','message'=>'Unable to add product','type'=>'error']);
                }
            }
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('product','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $product = Product::findOrFail($id);
            return view('product.product.show', compact('product'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('product','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            if(Auth::user()->hasRole('spa_salon')){
                $product = Product::where('id',$id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
                $allBranches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                $branchIds = $allBranches->pluck('id');
                $productCategories = ServiceCategory::get();
                $brands = ProductBrand::get();
                $suppliers = Supplier::whereIn('salon_id',$this->getBranchIds())->get();
            }else{
                $product = Product::where('id',$id)->where('salon_id', Auth::user()->salon_id)->firstOrFail();
                $allBranches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                $branchIds = $allBranches->pluck('id');
                $productCategories = ServiceCategory::get();
                $brands = ProductBrand::get();
                $suppliers = Supplier::where('salon_id',$branchIds)->get();
            }
            return view('product.product.edit', compact('product','productCategories','allBranches','brands','suppliers'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('product','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            extract($request->all());
            $product = Product::where('id',$id)->firstOrFail();
            $requestData = (["title"=>$product_name??"","product_category_id"=>$product_category??"","brand_id"=>$brand_id??"","description"=>$description]);
            $product->update($requestData);
            if ($request->has('cropped_images')) {
                if (is_array($request->cropped_images)) {
                    foreach ($request->cropped_images as $key => $base64Image) {
                        $imageData = str_replace('data:image/png;base64,', '', $base64Image);
                        $imageData = str_replace(' ', '+', $imageData);
                        $decodedImage = base64_decode($imageData);
                        $extension = 'png';
                        $imageName = Str::random(10) . '.' . $extension;
                        $destinationPath = 'product_image/';
                        Storage::disk('website')->delete($product->productImage->name);
                        ProductImage::where('product_id',$product->id)->delete();
                        $pic = Storage::disk('website')->put($destinationPath . $imageName, $decodedImage);
                        ProductImage::create([
                            'name' => $destinationPath . $imageName,
                            'product_id' => $product->id
                        ]);
                    }
                }
            }
            return redirect('product/product')->with('flash_message', 'Product updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('product','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Product::where('id',$id)->update(['is_deleted'=>'0']);
            return redirect('product/product')->with('flash_message', 'Product deleted!');
        }
        return response(view('403'), 403);

    }
    public function updateStock(Request $request)
    {
        $request->validate([
            'product_id' => 'required|integer|exists:products,id',
            'out_of_stock' => 'required|integer'
        ]);
        $product = Product::where('id',$request->product_id)->firstOrFail();
        if ($product) {
            $product->near_to_out_of_stock = $request->out_of_stock;
            $product->save();
            $currentDate = \Carbon\Carbon::now();
            $nearestExpiry = optional($product->productInventories->sortBy('expiry_date')->firstWhere('quantity', '>', 0))->expiry_date;
            $showWarning = $nearestExpiry && \Carbon\Carbon::parse($nearestExpiry)->diffInDays($currentDate) <= 15;
            return response()->json([
                'quantity' => $product->productInventories->sum('quantity'),
                'near_to_out_of_stock' => $product->near_to_out_of_stock,
                'showWarning' => $showWarning
            ]);
        }
        return response()->json(['error' => 'Product not found'], 404);
    }
}
