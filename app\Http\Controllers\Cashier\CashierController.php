<?php

namespace App\Http\Controllers\Cashier;

use App\CustomNotification;
use App\Http\Controllers\Controller;
use App\PaymentCard;
use App\PremiumAddonPackage;
use App\PremiumAddonSalonCashier;
use App\User;
use App\Profile;
use App\UserSubscription;
use Illuminate\Http\Request;
use App\PremiumAddonCashierHistory;
use Auth;
use Mail;

class CashierController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('cashier','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25000000;
            if (!empty($keyword)) {
                 $cashier = User::whereHas(
                    'roles', function($q){
                    $q->where('name', 'cashier');
                }
                )->where('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('name', 'LIKE', "%$keyword%")
                ->orWhere('email', 'LIKE', "%$keyword%")
                ->orWhere('password', 'LIKE', "%$keyword%")
                ->where('salon_id',Auth::id())->paginate($perPage);
            } else {
                if ($request->branch_id != null){
                    if (in_array($request->branch_id, $this->getBranchIds())){
                        $branches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('id', $request->branch_id)->get();
                        $branchIds = $branches->pluck('id');
                        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                            ->orderBy('user_id', 'ASC')
                            ->orderBy('id', 'DESC')
                            ->get()
                            ->groupBy('user_id');
                        $currentDate = now();
                        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                            $dueDate = $subs->first()->fatoraPdf->due_date;
                            return $dueDate->isPast();
                        })->keys();
                        $cashier = User::whereHas(
                            'roles', function($q){
                            $q->where('name', 'cashier');
                        }
                        )->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->orderBy('id','DESC')->paginate($perPage);
                        $cashierCount = User::whereHas(
                            'roles', function($q){
                            $q->where('name', 'cashier');
                        }
                        )->whereIn('salon_id',$branchIds)->orderBy('id','DESC')->count();
                        $userSubscription = UserSubscription::where('user_id',$branchIds)->orderBy('id','DESC')->first();
                        $allBranches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                    }else{
                        return redirect()->back()->with(['title'=>'Error','message'=>'Please Try Again','type'=>'error']);
                    }
                }else{
                    $allBranches = User::whereHas('roles', function ($query) {
                        $query->where('name', 'spa_salon');
                    })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                    $branchIds = $allBranches->pluck('id');
                    $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                        ->orderBy('user_id', 'ASC')
                        ->orderBy('id', 'DESC')
                        ->get()
                        ->groupBy('user_id');
                    $currentDate = now();
                    $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                        $dueDate = $subs->first()->fatoraPdf->due_date;
                        return $dueDate->isPast();
                    })->keys();
                    $cashier = User::whereHas(
                        'roles', function($q){
                        $q->where('name', 'cashier');
                    }
                    )->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->orderBy('id','DESC')->paginate($perPage);
                    $cashierCount = User::whereHas(
                        'roles', function($q){
                        $q->where('name', 'cashier');
                    }
                    )->whereIn('salon_id',[Auth::id()])->orderBy('id','DESC')->count();
                    $userSubscription = UserSubscription::where('user_id',Auth::id())->orderBy('id','DESC')->first();
                }
            }
            return view('cashier.cashier.index', compact('cashier','cashierCount','userSubscription','allBranches','userSubscriptionsDueDate','expiredUserIds'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create(Request $request)
    {
        $model = str_slug('cashier','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            if ($request->branch_id != null){
                $branches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                $selectedbranch = User::where('id',$request->branch_id)->first();
                $premiumAddonsCashier = PremiumAddonPackage::where('type','Cashier')->where('status','1')->get();
                $taxprice = Profile::where('user_id',2)->first()->vat;
                $paymentCards = PaymentCard::where('salon_id',Auth::user()->salon_id)->orderBy('id','DESC')->limit(3)->get();
            }else{
                $branches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                $selectedbranch = "";
                $premiumAddonsCashier = PremiumAddonPackage::where('type','Cashier')->where('status','1')->get();
                $taxprice = Profile::where('user_id',2)->first()->vat;
                $paymentCards = PaymentCard::where('salon_id',Auth::user()->salon_id)->orderBy('id','DESC')->limit(3)->get();
            }
            return view('cashier.cashier.create', compact('branches','selectedbranch','premiumAddonsCashier','taxprice','paymentCards'));
        }
        return response(view('403'), 403);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('cashier','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            if (in_array($request->salon_id, $this->getBranchIds())) {
                extract($request->all());
                if(User::where('email',$request->email)->exists()){
                    return redirect()->back()->with('flash_message', 'This Email is Already Taken!');
                }
                if (isset($request->premium_addon_salon_cashier) && $request->premium_addon_salon_cashier != null){
                    $phoneDefault = str_replace(' ', '', $request->phone);
                    $user = User::create(['name'=>$first_name.' '.$last_name,'first_name'=>$first_name,'last_name'=>$last_name,'name'=>$first_name.' '.$last_name,'email'=>$email,'password'=>bcrypt($password),'salon_id'=>$salon_id,'phone'=>$phoneDefault]);
                    if ($request->file('profile_picture')) {
                        $file = $request->file('profile_picture');
                        $extension = $file->extension()?: 'png';
                        $destinationPath = public_path() . '/storage/uploads/users/';
                        $safeName = str_random(10) . '.' . $extension;
                        $file->move($destinationPath, $safeName);
                        $profile_picture = $safeName;
                    }else{
                        $profile_picture = 'no_avatar.jpg';
                    }//end if else.
                    // 4 role is for cashier
                    Profile::create(['user_id'=>$user->id,'pic'=>$profile_picture,'address'=>$address,'phone'=>$phone,'dob'=>$dob,'gender'=>$gender,'state'=>$state,'city'=>$city,'country'=>$country,'latitude'=>$lat,'longitude'=>$lng,'postal'=>$zip_code]);
                    $user->roles()->attach([1 => ['role_id' =>4,'user_id' => $user->id]]);
                    if(Auth::user()->tour_status == 4){
                        $salon = User::where('id',Auth::user()->id)->update(['tour_status'=>5]);
                    }
                    $premiumAddonSalonCashier = PremiumAddonSalonCashier::where('id',$request->premium_addon_salon_cashier)->first();
                    $no_of_users_remaining = $premiumAddonSalonCashier->premiumAddonHistoryRemaingCashier - 1;
                    ($no_of_users_remaining);
                    $premiumAddonSalonCashierHistory = PremiumAddonCashierHistory::create(['salon_id'=>$request->salon_id,'premium_addon_id'=>$premiumAddonSalonCashier->premium_addon_id,'premium_addon_salon_cashier_id'=>$premiumAddonSalonCashier->id,'no_of_users_remaining'=>$no_of_users_remaining,'cashier_id'=>$user->id]);
                    ($premiumAddonSalonCashierHistory);;
                    try{
                        $salon = User::findOrFail($salon_id);
                        $salon_picture = $salon->profile->pic;
                        $salon_name = $salon->name;
                        $data = array(
                            'name' => $first_name.' '.$last_name ,
                            'email' => $email,
                            'phone' => $phoneDefault,
                            'password' => $password,
                            'shopPicture' =>$salon_picture,
                            'shopName' =>$salon_name,
                            'welcome_message' => 'Welcome',
                            'information_message' => 'Account Registration successful',
                            'detail' => env('APP_URL'),
                            'login_url' => env('APP_URL'),
                            'site_url' => env('APP_URL'),
                        );
                        $notifyData = [
                            'name'                => $first_name.' '.$last_name ,
                            'shopName'            => $salon_name,
                            'information_message' => 'Account Registration successful',
                            'type'                => 'humanCapital',
                            'template'            => 'cashier_registration',
                        ];
                        $custom = CustomNotification::create(
                            [
                                'notifiable_id'   => $salon->id,
                                'notifiable_type' => 'App\User',
                                'type'            => 'humanCapital',
                                'data'            => $notifyData,
                            ]
                        );
                        \Log::info('Custom Notification Created For humanCapital: ', ['data' => $custom,$salon->id]);
                        Mail::send('website.email_templates.registration_employee_email',['data'=>$data],function($message) use($data){
                            $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Usman Dev')->subject('Verify Your Details - Cashier Registration');;
                        });
                    }catch (\Exception $e){
                        return redirect()->back();
                    }//end try catch.
                    return redirect('cashier/cashier')->with('flash_message', 'Cashier added!');
                }else{
                    $phoneDefault = str_replace(' ', '', $request->phone);
                    $user = User::create(['name'=>$first_name.' '.$last_name,'first_name'=>$first_name,'last_name'=>$last_name,'name'=>$first_name.' '.$last_name,'email'=>$email,'password'=>bcrypt($password),'salon_id'=>$salon_id,'phone'=>$phoneDefault]);
                    if ($request->file('profile_picture')) {
                        $file = $request->file('profile_picture');
                        $extension = $file->extension()?: 'png';
                        $destinationPath = public_path() . '/storage/uploads/users/';
                        $safeName = str_random(10) . '.' . $extension;
                        $file->move($destinationPath, $safeName);
                        $profile_picture = $safeName;
                    }else{
                        $profile_picture = 'no_avatar.jpg';
                    }//end if else.
                    // 4 role is for cashier
                    Profile::create(['user_id'=>$user->id,'pic'=>$profile_picture,'address'=>$address,'phone'=>$phone,'dob'=>$dob,'gender'=>$gender,'state'=>$state,'city'=>$city,'country'=>$country,'latitude'=>$lat,'longitude'=>$lng,'postal'=>$zip_code]);
                    $user->roles()->attach([1 => ['role_id' =>4,'user_id' => $user->id]]);
                    if(Auth::user()->tour_status == 4){
                        $salon = User::where('id',Auth::user()->id)->update(['tour_status'=>5]);
                    }
                    try{
                        $salon = User::findOrFail($salon_id);
                        $salon_picture = $salon->profile->pic;
                        $salon_name = $salon->name;
                        $data = array(
                            'name' => $first_name.' '.$last_name ,
                            'email' => $email,
                            'phone' => $phoneDefault,
                            'password' => $password,
                            'shopPicture' =>$salon_picture,
                            'shopName' =>$salon_name,
                            'welcome_message' => 'Welcome',
                            'information_message' => 'Account Registration successful',
                            'detail' => env('APP_URL'),
                            'login_url' => env('APP_URL'),
                            'site_url' => env('APP_URL'),
                            'type'            => 'humanCapital',
                            'template'            => 'CashierVerify',
                        );
                        $custom = CustomNotification::create(
                            [
                                'notifiable_id'   => $salon_id,
                                'notifiable_type' => 'App\User',
                                'type'            => 'humanCapital',
                                'data'            => $data,
                            ]
                        );
                        \Log::info('Custom Notification Created For CashierVerify: ', ['data' => $custom,$salon_id]);
                        Mail::send('website.email_templates.registration_employee_email',['data'=>$data],function($message) use($data){
                            $message->to($data['email'], $data['name'])->cc('<EMAIL>', 'Usman Dev')->subject('Verify Your Details - Cashier Registration');;
                        });
                    }catch (\Exception $e){
                        return redirect()->back();
                    }//end try catch.
                    return redirect('cashier/cashier')->with('flash_message', 'Cashier added!');
                }
            }else{
                return redirect()->back()->with(['title'=>'Error','message'=>'Please Try Again','type'=>'error']);
            }
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('cashier','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $cashier = User::where('id', $id)
                ->whereIn('salon_id', $this->getBranchIds())
                ->firstOrFail();
            return view('cashier.cashier.show', compact('cashier'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('cashier','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $cashier = User::where('id', $id)
                ->whereIn('salon_id', $this->getBranchIds())
                ->firstOrFail();
            return view('cashier.cashier.edit', compact('cashier'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('cashier','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            extract($request->all());
            $requestData = ['name'=>$first_name.' '.$last_name,'first_name'=>$first_name,'last_name'=>$last_name,'name'=>$first_name.' '.$last_name,'email'=>$email,'phone'=>$phone];
            $cashier = User::where('id', $id)
                ->whereIn('salon_id', $this->getBranchIds())
                ->firstOrFail();
            if ($request->password!=''){
                $requestData['password']=bcrypt($request->password);
            }else{
                $requestData['password']= $cashier->password;
            }
            if ($file = $request->file('profile_picture')) {
                $extension = $file->extension()?: 'png';
                $destinationPath = public_path() . '/storage/uploads/users/';
                $safeName = str_random(10) . '.' . $extension;
                $file->move($destinationPath, $safeName);
                $profile_picture = $safeName;
            }else{
                $profile_picture = $cashier->profile->pic;
            }
            $cashier->update($requestData);
            $profile = ['pic'=>$profile_picture,'address'=>$address,'phone'=>$phone,'dob'=>$dob,'gender'=>$gender,'state'=>$state,'city'=>$city,'country'=>$country,'latitude'=>$lat,'longitude'=>$lng,'postal'=>$zip_code];
            Profile::where('user_id',$cashier->id)->update($profile);
            return redirect('cashier/cashier')->with('flash_message', 'Cashier updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('cashier','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            $user = User::whereIn('salon_id', $this->getBranchIds())
                ->where('id', $id)
                ->firstOrFail();
            if ($user) {
                $user->delete();
            }
            return redirect('cashier/cashier')->with('flash_message', 'Cashier deleted!');
        }
        return response(view('403'), 403);
    }
}
