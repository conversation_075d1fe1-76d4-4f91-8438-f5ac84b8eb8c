<?php

namespace App\Http\Controllers\SalonService;

use App\CustomNotification;
use App\Http\Controllers\Controller;
use Auth;
use Illuminate\Support\Str;
use Storage;
use App\SalonService;
use App\EmployeeService;
use App\UserSubscription;
use App\User;
use App\ServiceCategory;
use App\EmployeeServiceCategory;
use Illuminate\Http\Request;

class SalonServiceController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('salonservice','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 250000;

            if (!empty($keyword)) {
                $salonservice = SalonService::where('name', 'LIKE', "%$keyword%")
                ->orWhere('salon_id', 'LIKE', "%$keyword%")
                ->where('salon_id',Auth::id())->paginate($perPage);
            } else {
                if ($request->branch_id != null){
                    if (in_array($request->branch_id, $this->getBranchIds())) {
                        $branches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('id', $request->branch_id)->get();
                        $salonservice = SalonService::where('is_deleted','1')->where('salon_id', $request->branch_id)->orderBy('id', 'DESC')->paginate($perPage);
                        $categories = ServiceCategory::where('salon_id',$request->branch_id)->orWhere('salon_id',null)->orderBy('id', 'DESC')->get();
                        $employees = User::whereHas(
                            'roles', function($q){
                            $q->where('name', 'employee');
                        }
                        )->where('salon_id',Auth::id())->orderBy('id','DESC')->get();
                        $allBranches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                    }else{
                        return redirect()->back()->with(['title'=>'Error','message'=>'Please Try Again','type'=>'error']);
                    }
                }else{
                    $allBranches = User::whereHas('roles', function ($query) {
                        $query->where('name', 'spa_salon');
                    })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                    $branches = User::whereHas('roles', function ($query) {
                        $query->where('name', 'spa_salon');
                    })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                    $salonIds = $branches->pluck('id');
                    $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $salonIds)
                        ->orderBy('user_id', 'ASC')
                        ->orderBy('id', 'DESC')
                        ->get()
                        ->groupBy('user_id');
                    $currentDate = now();
                    $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                        $dueDate = $subs->first()->fatoraPdf->due_date;
                        return $dueDate->isPast();
                    })->keys();
                    $salonservice = SalonService::where('is_deleted','1')->whereIn('salon_id', $salonIds)->whereNotIn('salon_id',$expiredUserIds)->orderBy('id', 'DESC')->paginate($perPage);
                    $categories = ServiceCategory::where('salon_id',Auth::user()->salon_id)->orWhere('id',Auth::user()->salon_id)->orWhere('salon_id',null)->orderBy('id', 'DESC')->get();
                    $employees = User::whereHas(
                        'roles', function($q){
                        $q->where('name', 'employee');
                    }
                    )->where('salon_id',Auth::id())->orderBy('id','DESC')->get();
                }
            }
            return view('salonService.salon-service.index', compact('salonservice','categories','employees','branches','allBranches'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('salonservice','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('salonService.salon-service.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('salonservice','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            if (in_array($request->salon_id, $this->getBranchIds())) {
                extract($request->all());
                if($request->other_category){
                    if ($request->has('cropped_image')){
                        $base64Image = $request->input('cropped_image');
                        $imageData = str_replace('data:image/png;base64,', '', $base64Image);
                        $imageData = str_replace(' ', '+', $imageData);
                        $decodedImage = base64_decode($imageData);
                        $extension = 'png';
                        $imageName = Str::random(10) . '.' . $extension;
                        $destinationPath = 'service_category_picture/';
                        $picture = Storage::disk('website')->put($destinationPath .$imageName, $decodedImage);
                    }else{
                        $picture = '';
                    }
                    $category_id = ServiceCategory::create(['name'=>$other_category, 'picture'=>$picture,'salon_id'=>$request->salon_id])->id;
                }
                if($request->hasFile('picture')){
                    $picture = Storage::disk('website')->put('salon_service_picture', $request->picture);
                }else{
                    $picture = '';
                }
                $service = SalonService::create(['name'=>$name,'picture'=>$picture,'salon_id'=>$request->salon_id, 'category_id'=>$category_id,'price'=>$price,'description'=>$description]);
                if ($service!=null) {
                    if (is_array($request->employee_id)) {
                        foreach ($request->employee_id as $index) {
                            $EmployeeService = EmployeeService::create(['employee_id'=>$index,'salon_service_id'=>$service->id]);
                            EmployeeServiceCategory::create(['employee_service_id'=>$EmployeeService->id,'service_category_id'=>$category_id]);
                        }
                    }
                    if(Auth::user()->tour_status == 2){
                        $salon = User::where('id',Auth::user()->id)->update(['tour_status'=>3]);
                    }
                    $data = [
                        'serviceName'  => $service->name,
                        'servicePrice' => $service->price,
                        'type'         => 'salon_add_service',

                    ];
                    $custom = CustomNotification::create(
                        [
                            'notifiable_type' => 'App\User',
                            'notifiable_id'   => $service->salon_id,
                            'type'            => 'salon_add_service',
                            'data'            => $data,

                        ]
                    );
                }
                return redirect('salonService/salon-service')->with('flash_message', 'Centre Service added!');
            }else{
                return redirect()->back()->with(['title'=>'Error','message'=>'Please Try Again','type'=>'error']);
            }
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('salonservice','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $salonservice = SalonService::where('id', $id)
                ->whereIn('salon_id', $this->getBranchIds())
                ->firstOrFail();
            return view('salonService.salon-service.show', compact('salonservice'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('salonservice','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $salonservice = SalonService::where('id', $id)
                ->whereIn('salon_id', $this->getBranchIds())
                ->firstOrFail();
            $categories = ServiceCategory::where('salon_id',$salonservice->id)->orWhere('salon_id',null)->orderBy('id', 'DESC')->get();
            $employees = User::whereHas(
                'roles', function($q){
                $q->where('name', 'employee');
            }
            )->where('salon_id',$salonservice->salon_id)->orderBy('id','DESC')->get();
            return view('salonService.salon-service.edit', compact('salonservice','categories','employees'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {   
        $model = str_slug('salonservice','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            extract($request->all());
            $salonservice = SalonService::where('id',$id)->whereIn('salon_id', $this->getBranchIds())->firstOrFail();
            if ($request->hasFile('picture')){
                $picture = Storage::disk('website')->put('salon_service_picture', $request->picture);
                Storage::disk('website')->delete($salonservice->picture);
            }else{
                $picture  = $salonservice->picture;
            }
            $requestData = (['name'=>$name,'picture'=>$picture,'price'=>$price,'description'=>$description,'category_id'=>$category_id]);
            $salonservice->update($requestData);
            if ($salonservice!=null) {
                EmployeeService::where('salon_service_id', $id)->delete();
                if (is_array($request->employee_id)) {
                    foreach ($request->employee_id as $value) {
                        $EmployeeService = EmployeeService::create([
                            'employee_id' => $value,
                            'salon_service_id' => $id
                        ]);
                    }
                    EmployeeServiceCategory::where('employee_service_id', $EmployeeService->id)->delete();
                    EmployeeServiceCategory::create([
                        'service_category_id' => $category_id,
                        'employee_service_id' => $EmployeeService->id
                    ]);
                }
            }
             return redirect('salonService/salon-service')->with('flash_message', ' Centre Service Updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('salonservice','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            SalonService::where('id',$id)->whereIn('salon_id', $this->getBranchIds())->update(['is_deleted'=>'0']);
            return redirect('salonService/salon-service')->with('flash_message', 'SalonService deleted!');
        }
        return response(view('403'), 403);
    }
}
