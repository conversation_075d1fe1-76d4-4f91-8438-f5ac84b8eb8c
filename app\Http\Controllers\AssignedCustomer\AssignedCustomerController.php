<?php

namespace App\Http\Controllers\AssignedCustomer;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use Mail;
use App\AssignedCustomer;
use App\CustomerAppointment;
use Illuminate\Http\Request;

class AssignedCustomerController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.`
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('assignedcustomer','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $assignedcustomer = AssignedCustomer::where('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('employee_id', 'LIKE', "%$keyword%")
                ->orWhere('customer_id', 'LIKE', "%$keyword%")
                ->orWhere('assigned_user_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $assignedcustomer = AssignedCustomer::paginate($perPage);
            }

            return view('assignedCustomer.assigned-customer.index', compact('assignedcustomer'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('assignedcustomer','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('assignedCustomer.assigned-customer.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {

        $model = str_slug('assignedcustomer','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $assigned = AssignedCustomer::create($requestData);
            if($request->appointment_id){
                CustomerAppointment::where('id',$request->appointment_id)->update(['status'=>"Approved"]);
                $status = "Approved";
            }
            try{
                $cutomer_name= $assigned->customer->name;
                $customer_email = $assigned->customer->email;
                $employee_name = $assigned->employee->name;
                $owner_email = $assigned->employee->salon->profile->owner_email;
                $employee_email = $assigned->employee->email;
                $status = $status;
                $subject = 'Appointment Assigned';
                $content = "Hello " . $cutomer_name . ",\n\n" .
                    "Your Appointment Is Assigned!\n\n" .
                    "" . $employee_name . "\n" .
                    "- And Your Appointment: " . $status . "\n" .
                    "- App URL: " . env('APP_URL') . "\n\n" .
                    "Thank you for using our application!\n\n" .
                    "Thanks,\n" .
                    env('APP_URL');
                Mail::raw($content, function ($message) use ($customer_email, $subject) {
                    $message->to($customer_email)
                        ->subject($subject);
                });
                $subject = 'Appointment Assigned';
                $content = "Hello " . $employee_name . ",\n\n" .
                    "Your customer Is Assigned!\n\n" .
                    "Please Check Customer Information.\n\n" .
                    "- Your Customer : " . $cutomer_name . "\n" .
                    "- App URL: " . env('APP_URL') . "\n\n" .
                    "Thank you for using our application!\n\n" .
                    "Thanks,\n" .
                    env('APP_URL');
                Mail::raw($content, function ($message) use ($employee_email, $subject,$owner_email) {
                    $message->to($employee_email)
                        ->cc($owner_email)->subject($subject);
                });
                return back()->with('flash_message', 'AssignedCustomer added!');
            }catch(\Exception $e){
                return back()->with(['message'=>'Your Creation successfully, but unable to send email.','type'=>'error','title'=>'Fail']);
            }

            return back()->with('flash_message', 'AssignedCustomer added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('assignedcustomer','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $assignedcustomer = AssignedCustomer::findOrFail($id);
            return view('assignedCustomer.assigned-customer.show', compact('assignedcustomer'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('assignedcustomer','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $assignedcustomer = AssignedCustomer::findOrFail($id);
            return view('assignedCustomer.assigned-customer.edit', compact('assignedcustomer'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('assignedcustomer','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $assignedcustomer = AssignedCustomer::findOrFail($id);
             $assignedcustomer->update($requestData);

             return redirect('assignedCustomer/assigned-customer')->with('flash_message', 'AssignedCustomer updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('assignedcustomer','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            AssignedCustomer::destroy($id);

            return redirect('assignedCustomer/assigned-customer')->with('flash_message', 'AssignedCustomer deleted!');
        }
        return response(view('403'), 403);

    }
}
