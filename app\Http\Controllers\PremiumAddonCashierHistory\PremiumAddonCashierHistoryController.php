<?php

namespace App\Http\Controllers\PremiumAddonCashierHistory;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\PremiumAddonCashierHistory;
use Illuminate\Http\Request;

class PremiumAddonCashierHistoryController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('premiumaddoncashierhistory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $premiumaddoncashierhistory = PremiumAddonCashierHistory::where('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('premium_addon_id', 'LIKE', "%$keyword%")
                ->orWhere('premium_addon_salon_cashier_id', 'LIKE', "%$keyword%")
                ->orWhere('no_of_users_remaining', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $premiumaddoncashierhistory = PremiumAddonCashierHistory::paginate($perPage);
            }

            return view('premiumAddonCashierHistory.premium-addon-cashier-history.index', compact('premiumaddoncashierhistory'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('premiumaddoncashierhistory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('premiumAddonCashierHistory.premium-addon-cashier-history.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('premiumaddoncashierhistory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            PremiumAddonCashierHistory::create($requestData);
            return redirect('premiumAddonCashierHistory/premium-addon-cashier-history')->with('flash_message', 'PremiumAddonCashierHistory added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('premiumaddoncashierhistory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $premiumaddoncashierhistory = PremiumAddonCashierHistory::findOrFail($id);
            return view('premiumAddonCashierHistory.premium-addon-cashier-history.show', compact('premiumaddoncashierhistory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('premiumaddoncashierhistory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $premiumaddoncashierhistory = PremiumAddonCashierHistory::findOrFail($id);
            return view('premiumAddonCashierHistory.premium-addon-cashier-history.edit', compact('premiumaddoncashierhistory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('premiumaddoncashierhistory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $premiumaddoncashierhistory = PremiumAddonCashierHistory::findOrFail($id);
             $premiumaddoncashierhistory->update($requestData);

             return redirect('premiumAddonCashierHistory/premium-addon-cashier-history')->with('flash_message', 'PremiumAddonCashierHistory updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('premiumaddoncashierhistory','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            PremiumAddonCashierHistory::destroy($id);

            return redirect('premiumAddonCashierHistory/premium-addon-cashier-history')->with('flash_message', 'PremiumAddonCashierHistory deleted!');
        }
        return response(view('403'), 403);

    }
}
