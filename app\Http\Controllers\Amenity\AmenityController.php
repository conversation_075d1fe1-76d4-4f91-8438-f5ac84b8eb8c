<?php

namespace App\Http\Controllers\Amenity;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use Storage;
use App\Amenity;
use Illuminate\Http\Request;

class AmenityController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('amenity','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25000000000;

            if (!empty($keyword)) {
                $amenity = Amenity::where('image', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $amenity = Amenity::orderBy('id','DESC')->paginate($perPage);
            }

            return view('amenity.amenity.index', compact('amenity'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('amenity','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('amenity.amenity.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('amenity','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            $this->validate($request, [
			'image' => 'required'
		]);
            extract($request->all());
            if($request->hasFile('image')){
                $image = Storage::disk('website')->put('amenity_picture', $request->image);
            }else{
                $image = '';
            }
            Amenity::create(['title'=>$title,'image'=>$image,'description'=>$description,'status'=>$status]);
            return redirect('amenity/amenity')->with('flash_message', 'Amenity added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('amenity','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $amenity = Amenity::findOrFail($id);
            return view('amenity.amenity.show', compact('amenity'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('amenity','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $amenity = Amenity::findOrFail($id);
            return view('amenity.amenity.edit', compact('amenity'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('amenity','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $this->validate($request, [
			'title' => 'required'
		]);
            $amenity = Amenity::findOrFail($id);
            extract($request->all());
            if($request->hasFile('image')){
                $image = Storage::disk('website')->put('amenity_picture', $request->image);
            }else{
                $image = $amenity->image;
            }
            $amenity->update(['title'=>$title,'image'=>$image,'description'=>$description,'status'=>$status]);
            return redirect('amenity/amenity')->with('flash_message', 'Amenity updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('amenity','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Amenity::destroy($id);

            return redirect('amenity/amenity')->with('flash_message', 'Amenity deleted!');
        }
        return response(view('403'), 403);

    }
}
