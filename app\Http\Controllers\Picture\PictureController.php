<?php

namespace App\Http\Controllers\Picture;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Picture;
use Illuminate\Http\Request;
// use Storage;
use Illuminate\Support\Facades\Storage;


class PictureController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('picture','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $picture = Picture::where('image', 'LIKE', "%$keyword%")
                ->orWhere('slug', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $picture = Picture::paginate($perPage);
            }

            return view('picture.picture.index', compact('picture'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('picture','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('picture.picture.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('picture','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            if($request->hasFile('image')){
                $picture = Storage::disk('website')->put('content_images', $request->image);
            }else{
                $picture = '';
            }

            // Picture::create($requestData);
            Picture::create(['image'=>$picture,'slug'=>$requestData['slug'],]);
            return redirect('picture/picture')->with('flash_message', 'Picture added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('picture','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $picture = Picture::findOrFail($id);
            return view('picture.picture.show', compact('picture'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('picture','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $picture = Picture::findOrFail($id);
            return view('picture.picture.edit', compact('picture'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('picture','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $picture = Picture::findOrFail($id);
            //  $picture->update($requestData);

            if($request->hasFile('image')){
                $image = Storage::disk('website')->put('content_images', $request->image);
                Storage::disk('website')->delete($picture->image);
            }else{
                $image  = $picture->image;
            }
            $requestData['image'] = $image;
             $picture->update($requestData);

             return redirect('picture/picture')->with('flash_message', 'Picture updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('picture','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Picture::destroy($id);

            return redirect('picture/picture')->with('flash_message', 'Picture deleted!');
        }
        return response(view('403'), 403);

    }
}
