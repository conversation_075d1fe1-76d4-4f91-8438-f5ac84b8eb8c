<?php

namespace App\Http\Controllers\PackageType;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\PackageType;
use Illuminate\Http\Request;

class PackageTypeController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('packagetype','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $packagetype = PackageType::where('name', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $packagetype = PackageType::paginate($perPage);
            }

            return view('packageType.package-type.index', compact('packagetype'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('packagetype','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('packageType.package-type.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('packagetype','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            $this->validate($request, [
			'name' => 'required'
		]);
            $requestData = $request->all();
            
            PackageType::create($requestData);
            return redirect('packageType/package-type')->with('flash_message', 'PackageType added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('packagetype','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $packagetype = PackageType::findOrFail($id);
            return view('packageType.package-type.show', compact('packagetype'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('packagetype','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $packagetype = PackageType::findOrFail($id);
            return view('packageType.package-type.edit', compact('packagetype'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('packagetype','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $this->validate($request, [
			'name' => 'required'
		]);
            $requestData = $request->all();
            
            $packagetype = PackageType::findOrFail($id);
             $packagetype->update($requestData);

             return redirect('packageType/package-type')->with('flash_message', 'PackageType updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('packagetype','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            PackageType::destroy($id);

            return redirect('packageType/package-type')->with('flash_message', 'PackageType deleted!');
        }
        return response(view('403'), 403);

    }
}
