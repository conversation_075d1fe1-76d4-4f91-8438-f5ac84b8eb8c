<?php

namespace App\Http\Controllers\CustomerProduct;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\CustomerProduct;
use Illuminate\Http\Request;

class CustomerProductController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('customerproduct','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $customerproduct = CustomerProduct::where('customer_id', 'LIKE', "%$keyword%")
                ->orWhere('salon_product_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $customerproduct = CustomerProduct::paginate($perPage);
            }

            return view('customerProduct.customer-product.index', compact('customerproduct'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('customerproduct','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('customerProduct.customer-product.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('customerproduct','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            CustomerProduct::create($requestData);
            return redirect('customerProduct/customer-product')->with('flash_message', 'CustomerProduct added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('customerproduct','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $customerproduct = CustomerProduct::findOrFail($id);
            return view('customerProduct.customer-product.show', compact('customerproduct'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('customerproduct','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $customerproduct = CustomerProduct::findOrFail($id);
            return view('customerProduct.customer-product.edit', compact('customerproduct'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('customerproduct','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $customerproduct = CustomerProduct::findOrFail($id);
             $customerproduct->update($requestData);

             return redirect('customerProduct/customer-product')->with('flash_message', 'CustomerProduct updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('customerproduct','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            CustomerProduct::destroy($id);

            return redirect('customerProduct/customer-product')->with('flash_message', 'CustomerProduct deleted!');
        }
        return response(view('403'), 403);

    }
}
