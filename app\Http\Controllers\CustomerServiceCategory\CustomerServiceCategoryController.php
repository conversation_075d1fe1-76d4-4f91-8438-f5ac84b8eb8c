<?php

namespace App\Http\Controllers\CustomerServiceCategory;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\CustomerServiceCategory;
use Illuminate\Http\Request;

class CustomerServiceCategoryController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('customerservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $customerservicecategory = CustomerServiceCategory::where('customer_service_id', 'LIKE', "%$keyword%")
                ->orWhere('service_category_id', 'LIKE', "%$keyword%")
                ->orWhere('appointment_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $customerservicecategory = CustomerServiceCategory::paginate($perPage);
            }

            return view('customerServiceCategory.customer-service-category.index', compact('customerservicecategory'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('customerservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('customerServiceCategory.customer-service-category.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('customerservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            CustomerServiceCategory::create($requestData);
            return redirect('customerServiceCategory/customer-service-category')->with('flash_message', 'CustomerServiceCategory added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('customerservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $customerservicecategory = CustomerServiceCategory::findOrFail($id);
            return view('customerServiceCategory.customer-service-category.show', compact('customerservicecategory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('customerservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $customerservicecategory = CustomerServiceCategory::findOrFail($id);
            return view('customerServiceCategory.customer-service-category.edit', compact('customerservicecategory'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('customerservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $customerservicecategory = CustomerServiceCategory::findOrFail($id);
             $customerservicecategory->update($requestData);

             return redirect('customerServiceCategory/customer-service-category')->with('flash_message', 'CustomerServiceCategory updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('customerservicecategory','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            CustomerServiceCategory::destroy($id);

            return redirect('customerServiceCategory/customer-service-category')->with('flash_message', 'CustomerServiceCategory deleted!');
        }
        return response(view('403'), 403);

    }
}
