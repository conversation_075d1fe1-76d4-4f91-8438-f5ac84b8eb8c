<?php

namespace App\Http\Controllers\PaymentCardDetail;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\PaymentCardDetail;
use Illuminate\Http\Request;

class PaymentCardDetailController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('paymentcarddetail','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $paymentcarddetail = PaymentCardDetail::where('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('usage_description', 'LIKE', "%$keyword%")
                ->orWhere('ammount_charge', 'LIKE', "%$keyword%")
                ->orWhere('tax_charge', 'LIKE', "%$keyword%")
                ->orWhere('payment_card_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $paymentcarddetail = PaymentCardDetail::paginate($perPage);
            }

            return view('paymentCardDetail.payment-card-detail.index', compact('paymentcarddetail'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('paymentcarddetail','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('paymentCardDetail.payment-card-detail.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('paymentcarddetail','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            PaymentCardDetail::create($requestData);
            return redirect('paymentCardDetail/payment-card-detail')->with('flash_message', 'PaymentCardDetail added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('paymentcarddetail','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $paymentcarddetail = PaymentCardDetail::findOrFail($id);
            return view('paymentCardDetail.payment-card-detail.show', compact('paymentcarddetail'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('paymentcarddetail','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $paymentcarddetail = PaymentCardDetail::findOrFail($id);
            return view('paymentCardDetail.payment-card-detail.edit', compact('paymentcarddetail'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('paymentcarddetail','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $paymentcarddetail = PaymentCardDetail::findOrFail($id);
             $paymentcarddetail->update($requestData);

             return redirect('paymentCardDetail/payment-card-detail')->with('flash_message', 'PaymentCardDetail updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('paymentcarddetail','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            PaymentCardDetail::destroy($id);

            return redirect('paymentCardDetail/payment-card-detail')->with('flash_message', 'PaymentCardDetail deleted!');
        }
        return response(view('403'), 403);

    }
}
