<?php

namespace App\Http\Controllers\ProductSupplierType;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\ProductSupplierType;
use Illuminate\Http\Request;

class ProductSupplierTypeController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('productsuppliertype','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $productsuppliertype = ProductSupplierType::where('supplier_id', 'LIKE', "%$keyword%")
                ->orWhere('supplier_type_id', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $productsuppliertype = ProductSupplierType::paginate($perPage);
            }

            return view('productSupplierType.product-supplier-type.index', compact('productsuppliertype'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('productsuppliertype','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('productSupplierType.product-supplier-type.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('productsuppliertype','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            ProductSupplierType::create($requestData);
            return redirect('productSupplierType/product-supplier-type')->with('flash_message', 'ProductSupplierType added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('productsuppliertype','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $productsuppliertype = ProductSupplierType::findOrFail($id);
            return view('productSupplierType.product-supplier-type.show', compact('productsuppliertype'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('productsuppliertype','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $productsuppliertype = ProductSupplierType::findOrFail($id);
            return view('productSupplierType.product-supplier-type.edit', compact('productsuppliertype'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('productsuppliertype','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $productsuppliertype = ProductSupplierType::findOrFail($id);
             $productsuppliertype->update($requestData);

             return redirect('productSupplierType/product-supplier-type')->with('flash_message', 'ProductSupplierType updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('productsuppliertype','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            ProductSupplierType::destroy($id);

            return redirect('productSupplierType/product-supplier-type')->with('flash_message', 'ProductSupplierType deleted!');
        }
        return response(view('403'), 403);

    }
}
