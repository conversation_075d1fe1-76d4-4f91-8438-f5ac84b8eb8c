<?php

namespace App\Http\Controllers\PaymentCard;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\PaymentCard;
use Illuminate\Http\Request;

class PaymentCardController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('paymentcard','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $paymentcard = PaymentCard::where('card_number', 'LIKE', "%$keyword%")
                ->orWhere('last4', 'LIKE', "%$keyword%")
                ->orWhere('name', 'LIKE', "%$keyword%")
                ->orWhere('cvv', 'LIKE', "%$keyword%")
                ->orWhere('expiry_date', 'LIKE', "%$keyword%")
                ->orWhere('type', 'LIKE', "%$keyword%")
                ->orWhere('status', 'LIKE', "%$keyword%")
                ->orWhere('salon_id', 'LIKE', "%$keyword%")
                ->orWhere('stripe_token', 'LIKE', "%$keyword%")
                ->orWhere('client_ip', 'LIKE', "%$keyword%")
                ->orWhere('fingerprint', 'LIKE', "%$keyword%")
                ->orWhere('is_primary', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $paymentcard = PaymentCard::paginate($perPage);
            }

            return view('paymentCard.payment-card.index', compact('paymentcard'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('paymentcard','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('paymentCard.payment-card.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('paymentcard','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            PaymentCard::create($requestData);
            return redirect('paymentCard/payment-card')->with('flash_message', 'PaymentCard added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('paymentcard','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $paymentcard = PaymentCard::findOrFail($id);
            return view('paymentCard.payment-card.show', compact('paymentcard'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('paymentcard','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $paymentcard = PaymentCard::findOrFail($id);
            return view('paymentCard.payment-card.edit', compact('paymentcard'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('paymentcard','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $paymentcard = PaymentCard::findOrFail($id);
             $paymentcard->update($requestData);

             return redirect('paymentCard/payment-card')->with('flash_message', 'PaymentCard updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('paymentcard','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            PaymentCard::destroy($id);

            return redirect('paymentCard/payment-card')->with('flash_message', 'PaymentCard deleted!');
        }
        return response(view('403'), 403);

    }
}
