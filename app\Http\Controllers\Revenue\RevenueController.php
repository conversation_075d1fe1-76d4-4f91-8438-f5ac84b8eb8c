<?php

namespace App\Http\Controllers\Revenue;

use App\CustomerAppointment;
use App\Http\Controllers\Controller;
use App\Revenue;
use App\User;
use App\UserSubscription;
use Illuminate\Http\Request;
use Storage;
use Auth;

class RevenueController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('revenue','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 250000;

            if (!empty($keyword)) {
                $revenue = Revenue::where('name', 'LIKE', "%$keyword%")
                ->orWhere('date', 'LIKE', "%$keyword%")
                ->orWhere('client_name', 'LIKE', "%$keyword%")
                ->orWhere('amount', 'LIKE', "%$keyword%")
                ->orWhere('vendor', 'LIKE', "%$keyword%")
                ->orWhere('tax', 'LIKE', "%$keyword%")
                ->orWhere('total_amount', 'LIKE', "%$keyword%")
                ->orWhere('client_type', 'LIKE', "%$keyword%")
                ->orWhere('attachments', 'LIKE', "%$keyword%")
                ->orWhere('is_recurring', 'LIKE', "%$keyword%")
                ->orWhere('time_cycle', 'LIKE', "%$keyword%")
                ->orWhere('start_date', 'LIKE', "%$keyword%")
                ->orWhere('end_date', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $allBranches = User::whereHas('roles', function ($query) {
                    $query->where('name', 'spa_salon');
                })->where('id', Auth::user()->id)->get();
                $branchIds = $allBranches->pluck('id');
                $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                    ->orderBy('user_id', 'ASC')
                    ->orderBy('id', 'DESC')
                    ->get()
                    ->groupBy('user_id');
                $currentDate = now();
                $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                    $dueDate = $subs->first()->fatoraPdf->due_date;
                    return $dueDate->isPast();
                })->keys();
                $customerAppointments = CustomerAppointment::whereIn('salon_id', $branchIds)
                    ->whereNotIn('salon_id', $expiredUserIds)
                    ->whereIn('status', ['Complete'])
                    ->orderBy('created_at', 'DESC')
                    ->get();
                $revenue = Revenue::whereIn('saloon_id',$branchIds)->orderBy('created_at','DESC')->get();
                $customerAppointmentsItems = $customerAppointments;
                $revenueItems = $revenue;
                $allRevenue = collect(array_merge($customerAppointmentsItems->all(), $revenueItems->all()));
                $allRevenue = $allRevenue->sortByDesc('created_at');
                $employees = User::whereHas(
                    'roles', function($q){
                    $q->where('name', 'employee');
                }
                )->whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->orderBy('id','DESC')->pluck('name');
            }
            return view('revenue.revenue.index', compact('allRevenue','employees'));
        }
        return response(view('403'), 403);

    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('revenue','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id',Auth::user()->salon_id)->orderBy('id','ASC')->get();
            return view('revenue.revenue.create',compact('allBranches'));
        }

        return response(view('403'), 403);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('revenue','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            extract($request->all());
            if ($request->hasFile('attachment')) {
                $attachment = Storage::disk('website')->put('revenue_attachments', $request->attachment);
            } else {
                $attachment = (NULL);
            }
            $revenue = Revenue::create([
                'saloon_id'=> Auth::user()->id??'',
                'name' => $revenue_name??'',
                'date' => $revenue_date??'',
                'client_name' => $client_name??'',
                'amount' => $amount??'',
                'total_amount' => $total_amount??'',
                'tax' => $revenue_tax??'',
                'client_type'=>$revenue_type??'',
                'is_recurring' => $recuring ?? '0',
                'time_cycle' => $time_cycle??null,
                'start_date' => $start_date??null,
                'end_date' => $end_date??null,
                'description' => $revenue_description??'',
                'attachments'=>$attachment??'',
            ]);

            if ($revenue) {
                return redirect(url('revenue/revenue'))->with(['title'=>'Done','message'=>'revenue details saved successfully','type'=>'success']);
            }
            else{
                return redirect(url('revenue/revenue'))->with(['title'=>'Fail','message'=>'Unable to save revenue details','type'=>'error']);
            }
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('revenue','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $revenue = Revenue::where('id',$id)->whereIn('saloon_id', $this->getBranchIds())->firstOrFail();
            return view('revenue.revenue.show', compact('revenue'));
        }

        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('revenue','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $revenue = Revenue::where('id',$id)->whereIn('saloon_id', $this->getBranchIds())->firstOrFail();
            return view('revenue.revenue.edit', compact('revenue'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('revenue','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
//            $requestData = $request->all();
            extract($request->all());
            $revenue = Revenue::findOrFail($id);
            if ($request->hasFile('attachment')) {
                $attachment = Storage::disk('website')->put('revenue_attachments', $request->attachment);
                Storage::disk('website')->delete($revenue->attachments);
            } else {
                $attachment = $revenue->attachments;
            }
            $revenue->update([
                'name' => $revenue_name??'',
                'date' => $revenue_date??'',
                'client_name' => $client_name??'',
                'amount' => $amount??'',
//                'vendor' => $revenue_vendor??'',
                'total_amount' => $total_amount??'',
                'tax' => $revenue_tax??'',
                'client_type'=>$client_type??'',
                'is_recurring' => $recuring ?? '0',
                'time_cycle' => $time_cycle??null,
                'start_date' => $start_date??null,
                'end_date' => $end_date??null,
                'description' => $revenue_description??'',
                'attachments'=>$attachment??'',
            ]);
            if ($revenue) {
                return redirect(url('revenue/revenue'))->with(['title'=>'Done','message'=>'revenue details updated successfully','type'=>'success']);
            }
            else{
                return redirect(url('revenue/revenue'))->with(['title'=>'Fail','message'=>'Unable to update revenue details','type'=>'error']);
            }
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('revenue','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Revenue::destroy($id);

            return redirect('revenue/revenue')->with('flash_message', 'Revenue deleted!');
        }
        return response(view('403'), 403);
    }
}
