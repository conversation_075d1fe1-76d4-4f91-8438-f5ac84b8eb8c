<?php

namespace App\Http\Controllers\OurService;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use Storage;
use App\OurService;
use Illuminate\Http\Request;

class OurServiceController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('ourservice','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $ourservice = OurService::where('title', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->orWhere('image', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                 $ourservice = OurService::paginate($perPage);
            }

            return view('ourService.our-service.index', compact('ourservice'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('ourservice','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('ourService.our-service.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('ourservice','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            if($request->hasFile('image')){
                $image = Storage::disk('website')->put('our_service', $request->image);
            }else{
                $image = '';
            }
            $ourService=OurService::create([
                'title'=>$request->title,
                'description'=>$request->description,
                'image'=>$image
            ]);
            return redirect('ourService/our-service')->with('flash_message', 'OurService added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('ourservice','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $ourservice = OurService::findOrFail($id);
            return view('ourService.our-service.show', compact('ourservice'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('ourservice','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $ourservice = OurService::findOrFail($id);
            return view('ourService.our-service.edit', compact('ourservice'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('ourservice','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $ourservice = OurService::findOrFail($id);
            if($request->hasFile('image')){
                $image = Storage::disk('website')->put('our_service', $request->image);
            }else{
                $image = $ourservice->image;
            }
            $ourservice->update([
                'title'=>$request->title,
                'description'=>$request->description,
                'image'=>$image
            ]);
             return redirect('ourService/our-service')->with('flash_message', 'OurService updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('ourservice','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            OurService::destroy($id);

            return redirect('ourService/our-service')->with('flash_message', 'OurService deleted!');
        }
        return response(view('403'), 403);

    }
}
