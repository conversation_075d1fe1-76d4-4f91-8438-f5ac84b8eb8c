<?php

namespace App\Http\Controllers\Admin;

use App\Ad;
use App\CustomerService;
use App\CustomNotification;
use App\Expense;
use App\Feedback;
use App\Http\Controllers\Controller;
use App\PaymentCard;
use App\PremiumAddonCashierHistory;
use App\PremiumAddonSalonCashier;
use App\PremiumAddonSalonEmployee;
use App\ProductInventory;
use App\PurchaseOrder;
use App\Revenue;
use App\ServiceCategory;
use App\StockOut;
use App\Supplier;
use Auth;
use Illuminate\Http\Request;
use App\SalonService;
use App\User;
use App\SubscriptionPlan;
use App\Product;
use Carbon\Carbon;
use App\UserSubscription;
use App\CustomerAppointment;
use App\FatoraInvoice;
use App\AssignedCustomer;
use Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use DB;
use App\Exports\FinancialReportExport;
use App\Exports\EmployeeReportExport;
use Maatwebsite\Excel\Facades\Excel;
use PDF;
use Illuminate\Support\Facades\Http;



class AdminController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return void
     */
    public function index(){
        return view('admin.dashboard');
    }

    public function dashboard(Request $request){
        return $request->all();
    }//end dashboard function.


    //Profile Setting Page
    public function profileSetting(){
        $user = Auth::user();
        $allCustomers = CustomerAppointment::where('salon_id', Auth::user()->salon_id)->orderBy('id', 'DESC')->get();
        $currentDate = Carbon::now()->format('m/d/Y');
        $upcomingAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
            ->where('customer_appointment_date', '>', $currentDate)
            ->whereIn('status', ['Pending', 'Approved'])
            ->orderBy('customer_appointment_date', 'asc')
            ->get();
        $currentAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
            ->where('customer_appointment_date', $currentDate)
            ->whereIn('status', ['Pending', 'Approved'])
            ->orderBy('customer_appointment_date', 'asc')
            ->get();
        $completeAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
            ->where('status', 'Complete')
            ->orderBy('customer_appointment_date', 'asc')
            ->get();
        $cancelAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)
            ->where('status', 'Cancel')
            ->orderBy('customer_appointment_date', 'asc')
            ->get();
        $activeAppointments = AssignedCustomer::where('salon_id', Auth::user()->salon_id)->orderBy('id', 'DESC')->get();
        return view('dashboard.profile_setting', compact('user','allCustomers','upcomingAppointments','currentAppointments','completeAppointments','cancelAppointments','activeAppointments'));
    }//end profileSetting function.
    public function userInformation(){
        return view('dashboard.user-information');
    }
    //Admin Dashboard Pages
    public function adminDashboard(){
        $totalSubscriptionPrice = UserSubscription::get()->pluck('amount_captured')->sum();
        $totalEarningThisMonth = UserSubscription::whereMonth('created_at', Carbon::now()->month)
            ->sum('amount_captured');
        $totalNewSubscriptionsThisMonth = User::whereHas(
            'roles', function($q){
            $q->where('name', 'spa_salon');
        }
        )->whereMonth('created_at', Carbon::now()->month)->count();
        $totalCanceledThisMonth = User::where('register_status','Canceled')->whereHas(
            'roles', function($q){
            $q->where('name', 'spa_salon');
        }
        )->whereMonth('created_at', Carbon::now()->month)->count();
        $totalSubscribersCount = UserSubscription::count();
        $averageAppointmentsPerProvider = DB::table('customer_appointments')
            ->select('salon_id', DB::raw('COUNT(*) as total_appointments'))
            ->groupBy('salon_id')
            ->get();
        $allSeriveProviderCount = User::whereHas(
            'roles', function($q){
            $q->where('name', 'spa_salon');
        }
        )->count();
        $activeSubscribersCount = User::whereHas(
            'roles', function($q){
            $q->where('name', 'spa_salon');
        }
        )->where('register_status',"Accepted")->count();
        $newSalons = User::whereHas(
            'roles', function($q){
            $q->where('name', 'spa_salon');
        }
        )->orderBy('id','DESC')
            ->get();
        $allSalons = User::whereHas(
            'roles', function($q){
            $q->where('name', 'spa_salon');
        }
        )->orderBy('id','DESC')->get();
        $allAppointmentCount = CustomerAppointment::count();
        $pendingAppointmentCount = CustomerAppointment::where('status','Pending')->count();
        $completedAppointmentCount = CustomerAppointment::where('status','Complete')->count();
        $subscribers = UserSubscription::orderBy('id','DESC');
        $subscribersUsers = $subscribers->get();
        $topservice = CustomerAppointment::pluck('salon_id');
        $allsalonsId = User::whereIn('id',$topservice)->orderBy('id','desc')->limit(4)->get();
        $newCustomers = CustomerAppointment::orderBy('id','desc')->limit(10)->get();
        return view('dashboard.adminDashboard.index',compact('totalSubscribersCount','allSalons', 'subscribersUsers','allAppointmentCount','pendingAppointmentCount','allsalonsId','activeSubscribersCount','totalSubscriptionPrice','allSeriveProviderCount','newCustomers','newSalons','totalEarningThisMonth','completedAppointmentCount','totalNewSubscriptionsThisMonth','totalCanceledThisMonth','averageAppointmentsPerProvider'));
    }//end adminDashboard function.
    public function serviceProvider(){
        $userSubscribers = UserSubscription::get();
        return view('dashboard.adminDashboard.service_provider', compact('userSubscribers'));
    }//end serviceProvider function.
    public function serviceProviderCustomers(){
//        $allCustomerCount = User::whereHas(
//            'roles', function($q){
//            $q->where('name', 'customer');
//        }
//        )->count();
        $completeAppointment = CustomerAppointment::get();
        $customer = User::whereHas(
            'roles', function($q){
            $q->where('name', 'customer');
        }
        )->get();
        $serviceProvider = User::whereHas(
            'roles', function($q){
            $q->where('name', 'spa_salon');
        }
        )->where('salon_setting_updated', 1)->get();

//        return $walk_Appointment = CustomerAppointment::where('salon_id',$serviceProvider)->get();
//        $walk_Appointment = CustomerAppointment::where('salon_id',Auth::id())->get();
        return view('dashboard.adminDashboard.service_provider_customers', compact('completeAppointment','customer','serviceProvider'));
    }//end serviceProvider function.
    public function serviceProvidErearning(){
        return view('dashboard.adminDashboard.services_providers_earning');
    }//end serviceProvider function.
    public function serviceProviderInner(){
        return view('dashboard.adminDashboard.service_provider_inner');
    }//end serviceProviderInner function.
    public function finance(){
        return view('dashboard.adminDashboard.finance');
    }//end finance function.
    public function coupon(){
        return view('dashboard.adminDashboard.coupon');
    }//end coupon function.
    public function supplier(){
        return view('dashboard.adminDashboard.supplier');
    }//end supplier function.
    public function inventoryManagement(){
        return view('dashboard.adminDashboard.inventory_management');
    }//end inventoryManagement function.
    public function email(){
        return view('dashboard.adminDashboard.email');
    }//end email function.

    public function popularProducts(){
        return view('dashboard.adminDashboard.popular_products');
    }


    //Business Dashboard Pages
    public function productFlow(){
        $allSalons = User::whereNull('salon_id')->whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })
            ->get()
            ->sortByDesc(function ($user) {
                return count($user->allShopCustomers);
            })
            ->values();
        $topSalons = $allSalons->take(3);
        $allProducts = $allSalons->flatMap(function($salon) {
            return $salon->shopBranchesProducts;
        });
        $grouped = $allProducts->groupBy('id')->map(function($products, $productId) {
            $product = $products->first();
            $allStockOuts = $products->flatMap->productStockOut;
            $salesStockOuts = $allStockOuts->filter(function ($stockOut) {
                return $stockOut->purchaseOrder
                    && $stockOut->purchaseOrder->status === 'sales';
            });
            $product->totalSold = $salesStockOuts->sum('quantity');
            $product->uniqueCustomerCount = $salesStockOuts
                ->pluck('purchaseOrder.user_id')
                ->unique()
                ->count();

            return $product;
        });
        $topProducts = $grouped
            ->sortByDesc('totalSold')
            ->take(3)
            ->values();
        return view('dashboard.adminDashboard.product_flow', compact('allSalons','topSalons','topProducts'));
    }
    public function adminProductDetail($id) {
        $salon = User::findOrFail($id);
        $categories = [];
        foreach ($salon->shopBranchesProducts as $product) {
            $categoryName = $product->getCategory->name;
            $productName = $product->title;
            $branchName = $product->salon->name;
            // Calculate value only for 'sales' status purchaseOrders
            $productValue = "0";
            foreach ($product->productStockOut as $item => $val){
                if(isset($val->purchaseOrder) && $val->purchaseOrder != null){
                    if ($val->purchaseOrder->status == "sales"){
                        $productValue = $val->purchaseOrder->total_amount_without_vat??0;
                    }
                }
            }
            // Initialize category if not exists
            if (!isset($categories[$categoryName])) {
                $categories[$categoryName] = [];
            }
            // Check if product already added
            $existingIndex = null;
            foreach ($categories[$categoryName] as $index => $existingProduct) {
                if ($existingProduct['name'] === $productName) {
                    $existingIndex = $index;
                    break;
                }
            }
            if ($existingIndex !== null) {
                // Merge branch if not already added
                if (!in_array($branchName, $categories[$categoryName][$existingIndex]['branches'])) {
                    $categories[$categoryName][$existingIndex]['branches'][] = $branchName;
                }

                // Add value
                $categories[$categoryName][$existingIndex]['value'] += $productValue;
            } else {
                // New product entry
                $categories[$categoryName][] = [
                    'name' => $productName,
                    'value' => $productValue,
                    'branches' => [$branchName],
                    'date' => "2025-08-08" // Optional: make dynamic from stockOut
                ];
            }
        }
        $formattedCategories = [];
        foreach ($categories as $categoryName => $products) {
            $formattedProducts = [];
            foreach ($products as $product) {
                $formattedProducts[] = [
                    'name' => $product['name'],
                    'value' => $product['value'],
                    'branches' => $product['branches'],
                    'date' => $product['date'],
                ];
            }
            $formattedCategories[$categoryName] = $formattedProducts;
        }
        return view('dashboard.adminDashboard.product_detail', compact('salon', 'categories','formattedCategories'));
    }

    public function businessDashboard(){
//        return CustomNotification::where('notifiable_id', auth()->user()->id)
//            ->whereNotNull('data->type')
//            ->get();
        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
        $branchIds = $allBranches->pluck('id');
        $salonServices = SalonService::whereIn('salon_id',$branchIds)->get();
        $customerAppointments = CustomerAppointment::whereIn('salon_id',$branchIds)->orderBy('id','DESC')->limit(10)->get();
        $assignedCustomers = AssignedCustomer::whereIn('salon_id',$branchIds)->orderBy('id','DESC')->get();
        $employee = User::whereHas(
            'roles', function($q){
            $q->where('name', 'employee');
        }
        )->whereIn('salon_id',$branchIds)->orderBy('id','DESC')->get();
        $employeeCount = User::whereHas(
            'roles', function($q){
            $q->where('name', 'employee');
        }
        )->whereIn('salon_id',$branchIds)->orderBy('id','DESC')->count();
        $customer_ids = CustomerAppointment::whereIn('salon_id', $branchIds)->pluck('customer_id');
        $customer = User::whereIn('id', $customer_ids)->orderBy('id','DESC')->count();
        $allAppointmentCount = CustomerAppointment::whereIn('salon_id',$branchIds)->count();
        $pendingAppointmentCount = CustomerAppointment::whereIn('salon_id',$branchIds)->where('status','Pending')->count();
        $newSubscribers = UserSubscription::orderBy('id','DESC')->limit('5')->get();
        $customerAppointment = CustomerAppointment::whereIn('salon_id',$branchIds)->get();
        $totalEarning = $customerAppointment->where('status','Complete')->pluck('appointmentPrice')->sum();
        $activeAppointments = $customerAppointment->where('status','Approved')->count();
        $totalservice = SalonService::whereIn('salon_id',$branchIds)->count();
        $totalproduct = Product::whereIn('salon_id',$branchIds)->count();
        return view('dashboard.businessDashboard.index', compact('salonServices','customerAppointments','assignedCustomers','employee','employeeCount','customer','allAppointmentCount','pendingAppointmentCount','customerAppointment','totalEarning','activeAppointments','totalservice','totalproduct'));
    }//end businessDashboard function.
    public function customDashboard(){
        return view('dashboard.businessDashboard.custom_dashboard');
    }//end customDashboard function.
    public function financeManagement(){
        return view('dashboard.businessDashboard.finance_management');
    }//end financeManagement function.
    public function customers(){
        $customers = User::whereHas(
            'roles', function($q){
            $q->where('name', 'customer');
        }
        )->get();
        return view('dashboard.businessDashboard.customers', compact('customers'));
    }//end customers function.
    public function dailyOperations(){
        return view('dashboard.businessDashboard.daily_operations');
    }//end dailyOperations function.
    public function businessInventoryManagement(){
        return view('dashboard.businessDashboard.inventory_management');
    }//end businessInventoryManagement function.
    public function orders(){
        return view('dashboard.businessDashboard.orders');
    }//end orders function.
    public function businessAppointments(){
        return view('dashboard.businessDashboard.appointments');
    }//end cashierAppointments function.
    public function addAppointment(){
        return view('dashboard.businessDashboard.add_appointment');
    }//end addAppointment function.
    public function reasonCancellation(){
        $cancelAppointments = CustomerAppointment::with('customerAppointmentAssigned')->where('salon_id', Auth::user()->salon_id)->where('status','Cancel')->get();
        $services = SalonService::where('salon_id',Auth::user()->salon_id)->get();
        return view('dashboard.businessDashboard.reason_cancellation', compact('cancelAppointments','services'));
    }//end reasonCancellation function.
    public function pendingRequest(){
        return view('dashboard.businessDashboard.pending_request');
    }//end pendingRequest function.
    public function assignedAppointment(){
        $assignedCustomers = AssignedCustomer::where('salon_id',Auth::user()->salon_id)->orderBy('id','DESC')->get();
        return view('dashboard.businessDashboard.assigned_appointment', compact('assignedCustomers'));
    }//end assignedAppointment function.
    public function canceledAppointment(){
        $cancelAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)->where('status','Cancel')->get();
        $services = SalonService::where('salon_id',Auth::user()->salon_id)->get();
        return view('dashboard.businessDashboard.canceled_appointment',compact('cancelAppointments','services'));
    }//end canceledAppointment function.
    public function updateSubscription($id=null){
        if ($id && in_array($id, $this->getBranchIds())) {
            $userSubscriptionFreeTrailUsed = UserSubscription::where('user_id',$id)->where('subscription_plan_id',7)->count();
            if($userSubscriptionFreeTrailUsed == 1){
                $capturedAt = auth::user()->userSubscriptionId->where('user_id',$id)->where('subscription_plan_id',7)->first()->captured_at;
                $liveDateCarbon = Carbon::now();
                $liveDate = $liveDateCarbon->format('Y-m-d H:i:s');
                $capturedDate = Carbon::createFromFormat('Y-m-d H:i:s', $capturedAt);
                $liveDate = Carbon::parse($liveDate);
                $sevenDaysLater = $capturedDate->copy()->addDays(7);
                $remainingDays = $liveDate->diffInDays($sevenDaysLater);
                if ($sevenDaysLater < $liveDate) {
                    $userSubscriptionFreeTrailUsed = "Used";
                } else {
                    $userSubscriptionFreeTrailUsed = $remainingDays . " Days Remaining";
                }
            }else{
                $userSubscriptionFreeTrailUsed = "";
            }
            $userSubscription = UserSubscription::where('user_id',$id)->orderBy('id','DESC')->first();
            $latestSubscription = $userSubscription->subscription_plan_id;
            $currentlySubscription = SubscriptionPlan::where('id',$latestSubscription)->first();
            $freeTrailSubscriptionPlans = SubscriptionPlan::where('status',1)->whereHas('subscriptionType', function($q){
                $q->where('name', 'Free Trail');
            })->get();
            $monthlySubscriptionPlans = SubscriptionPlan::where('status',1)->whereHas('subscriptionType', function($q){
                $q->where('name', 'monthly');
            })->get();
            $yearlySubscriptionPlans = SubscriptionPlan::where('status',1)->whereHas('subscriptionType', function($q){
                $q->where('name', 'yearly');
            })->get();
            return view('dashboard.businessDashboard.subscriptions', compact('monthlySubscriptionPlans','yearlySubscriptionPlans','freeTrailSubscriptionPlans','userSubscriptionFreeTrailUsed','latestSubscription','currentlySubscription','userSubscription','id'));
        }else{
            return redirect()->back()->with([
                'title' => 'Error',
                'message' => 'Please Try Again',
                'type' => 'error'
            ]);
        }
    }//end subscriptions function.
    public function adminSubscription(){
        $freeSubscriptionPlans = SubscriptionPlan::where('status',1)->whereHas('subscriptionType', function($q){
            $q->where('name', 'Free');
        })->get();
        return view('dashboard.adminDashboard.subscriptions', compact('freeSubscriptionPlans'));
    }
    public function createSubscriber($id){
        $subscriptionPlan = SubscriptionPlan::findOrFail($id);
        return view('dashboard.adminDashboard.create_subscriber', compact('subscriptionPlan'));
    }//end subscriptionsPayment function.

    public function changedSubscription($id,$salonId=null){
        $subscriptionPlan = SubscriptionPlan::findOrFail($id);
        $userSubscription = UserSubscription::where('user_id',$salonId)->orderBy('id','DESC')->first();
        $salon = User::findOrFail($salonId);
        return view('dashboard.businessDashboard.subscriptions_payment', compact('subscriptionPlan','userSubscription','salon'));
    }//end subscriptionsPayment function.
    public function changeSubscriber($id){
        $freeSubscriptionPlans = SubscriptionPlan::where('status',1)->whereHas('subscriptionType', function($q){
            $q->where('name', 'Free');
        })->get();
        $userSubscription = UserSubscription::where('user_id',$id)->orderBy('id','DESC')->first();
        return view('dashboard.adminDashboard.subscriptions', compact('freeSubscriptionPlans','userSubscription'));
    }//end subscriptionsPayment function.
    public function changeSubscriberSubscription($id,$userId){
        $user = User::findOrFail($userId);
        $subscriptionPlan = SubscriptionPlan::findOrfail($id);
        $description = $subscriptionPlan->subscriptionType->name." Subscription of `".$user->first_name." ".$user->last_name."`
            Including tax.            
                Billing Address:$user->profile->address,
                Country:$user->profile->country,
                City:$user->profile->city,
                State:$user->profile->state,
                Zip:$user->profile->postal.
        ";
        $company = User::whereHas(
            'roles', function($q){
            $q->where('name', 'user');
        }
        )->first();
        $invoice_number = rand('11111111','99999999');
        $date = new \DateTime('now', new \DateTimeZone('Asia/Riyadh'));
        $current_date = $date->format('Y-m-d H:i:s');
        $amount = "Free";
        try{
            $due_date = "";
            if(isset($subscriptionPlan->id) && $subscriptionPlan->id == "8") {
                $due_date = $date->modify('+30 days')->format('Y-m-d');
                $userSubscription = UserSubscription::create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $subscriptionPlan->id,
                    'amount_captured' => null,
                    'captured_status' => "Free",
                    'captured_at' => $current_date,
                    'currency' => null,
                    'invoice_id' => null,
                    'charge_id' => null,
                    'description' => $description,
                    'customer_id' => null,
                    'product_id' => null,
                    'price_id' => null,
                ]);
                FatoraInvoice::create(['user_subscription_id'=>$userSubscription->id,'company_name'=>$company->name,'company_address'=>$company->profile->address,'company_vat_number'=>$company->profile->vat_number,'commercial_registration_number'=>38833738,'customer_name'=>$user->first_name.' '.$user->last_name,'customer_address'=>$user->profile->address,'customer_vat_number'=>$user->profile->vat_number,'total_amount'=>$amount,'unit_price'=>"Free",'vat_amount'=>1 * 15,'quantity'=>1,'description'=>$subscriptionPlan->description,'invoice_number'=>$invoice_number,'current_date'=>$current_date,'due_date'=>$due_date]);
            }elseif (isset($subscriptionPlan->id) && $subscriptionPlan->id == "9"){
                $due_date = $date->modify('+1 year')->format('Y-m-d');
                $userSubscription = UserSubscription::create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $subscriptionPlan->id,
                    'amount_captured' => null,
                    'captured_status' => "Free",
                    'captured_at' => $current_date,
                    'currency' => null,
                    'invoice_id' => null,
                    'charge_id' => null,
                    'description' => $description,
                    'customer_id' => null,
                    'product_id' => null,
                    'price_id' => null,
                ]);
                FatoraInvoice::create(['user_subscription_id'=>$userSubscription->id,'company_name'=>$company->name,'company_address'=>$company->profile->address,'company_vat_number'=>$company->profile->vat_number,'commercial_registration_number'=>38833738,'customer_name'=>$user->first_name.' '.$user->last_name,'customer_address'=>$user->profile->address,'customer_vat_number'=>$user->profile->vat_number,'total_amount'=>$amount,'unit_price'=>"Free",'vat_amount'=>1 * 15,'quantity'=>1,'description'=>$subscriptionPlan->description,'invoice_number'=>$invoice_number,'current_date'=>$current_date,'due_date'=>$due_date]);
            }elseif (isset($subscriptionPlan->id) && $subscriptionPlan->id == "10"){
                $due_date = $date->modify('+50 year')->format('Y-m-d');
                $userSubscription = UserSubscription::create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $subscriptionPlan->id,
                    'amount_captured' => null,
                    'captured_status' => "Free",
                    'captured_at' => $current_date,
                    'currency' => null,
                    'invoice_id' => null,
                    'charge_id' => null,
                    'description' => $description,
                    'customer_id' => null,
                    'product_id' => null,
                    'price_id' => null,
                ]);
                FatoraInvoice::create(['user_subscription_id'=>$userSubscription->id,'company_name'=>$company->name,'company_address'=>$company->profile->address,'company_vat_number'=>$company->profile->vat_number,'commercial_registration_number'=>38833738,'customer_name'=>$user->first_name.' '.$user->last_name,'customer_address'=>$user->profile->address,'customer_vat_number'=>$user->profile->vat_number,'total_amount'=>$amount,'unit_price'=>"Free",'vat_amount'=>1 * 15,'quantity'=>1,'description'=>$subscriptionPlan->description,'invoice_number'=>$invoice_number,'current_date'=>$current_date,'due_date'=>$due_date]);
            }
            $data = array(
                'user_id'             => $user->id,
                'name' => $user->first_name.' '.$user->last_name ,
                'description'         => $description,
                'email'               => $user->email,
                'captured_at'         => $current_date,
                'amount'              => '0',
                'free_package'        => 'Free Package',
                'package_name'        => $subscriptionPlan->name,
                'welcome_message'     => 'Welcome',
                'information_message' => 'Welcome to LIINK! Your Free Trial Awaits',
                'detail'              => env('APP_URL'),
                'login_url'           => env('APP_URL'),
                'description'         => $subscriptionPlan->descriptionDetails,
                'site_url'            => env('APP_URL'),
                'support_phone'       => $company->phone??'',
                'support_email'       => $company->email,
                'trial_end_date'      => $due_date,
            );
            Mail::send('website.email_templates.registration_welcome_free_trail_email',['data'=>$data],function($message) use($data){
                $message->to($data['email'], $data['name'])->bcc('<EMAIL>', 'Usman Dev')->subject('مرحبًا بك في LIINK! تجربتك المجانية بإنتظارك');
            });
            return redirect(url('userSubscription/user-subscription'));
        }catch(\Exception $e){
            return $e->getMessage();
        }
    }
    public function payment(){
        return view('dashboard.businessDashboard.payment');
    }//end payment function.
    public function employees(){
        return view('dashboard.businessDashboard.employees');
    }//end employees function.
    public function employeesTwo(){
        return view('dashboard.businessDashboard.employees_two');
    }//end employees function.
    public function salonSetting(){
        return view('dashboard.businessDashboard.salon_setting');
    }//end salonSetting function.
    public function addServices(){
        return view('dashboard.businessDashboard.add_services');
    }//end salonSetting function.
    public function businessProfileSetting(){
        $paymentCards = PaymentCard::where('salon_id',Auth::user()->salon_id)->get();
        return view('dashboard.businessDashboard.business_profile_setting', compact('paymentCards'));
    }//end salonSetting function.

    //Cashier Dashboard Pages
    public function cashierDashboard(){
        return view('dashboard.cashierDashboard.index');
    }//end cashierDashboard function.
    public function cashierCustomers(){
        return view('dashboard.cashierDashboard.customers');
    }//end cashierCustomers function.
    public function productRequest(){
        return view('dashboard.cashierDashboard.product_request');
    }//end cashierCustomers function.
    public function cashierFinanceManagement(){
        return view('dashboard.cashierDashboard.finance_management');
    }//end cashierFinanceManagement function.
//    public function cashierInvoice(){
//        return view('dashboard.cashierDashboard.cashier_invoice');
//    }//end cashierInvoice function.

    //Customer Dashboard
    public function customerDashboard(){
        $ads = Ad::where('status',1)->get();
        $salons = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })
            ->where('register_status', 'Accepted')
            ->where('salon_setting_updated', 1)
            ->get();
        $salonIds = $salons->pluck('id');
        $userSubscriptions = UserSubscription::whereIn('user_id', $salonIds)
            ->whereIn('id', function ($query) {
                $query->selectRaw('MAX(id)')
                    ->from('user_subscriptions')
                    ->groupBy('user_id');
            })
            ->get();
        $customerAppointment = CustomerAppointment::where('customer_id',Auth::id())->orderBy('id','DESC')->get();
        $allAppointment = $customerAppointment->count();
        $pendingAppointment = $customerAppointment->where('status','Pending')->count();
        $activeAppointment = $customerAppointment->where('status','Approved')->count();
        return view('dashboard.customerDashboard.index', compact('allAppointment','pendingAppointment','activeAppointment','customerAppointment','ads','salons','userSubscriptions'));
    }//end customerDashboard function.
    public function appointments(){
        $appointments = CustomerAppointment::where('customer_id', Auth::user()->id)->orderBy('id','DESC')->whereNotIn('status',['Complete','Cancel'])->get();
        return view('dashboard.customerDashboard.appointments',compact('appointments'));
    }//end appointments function.
    public function completeAppointments(){
        $appointments = CustomerAppointment::where('customer_id', Auth::user()->id)->where('status','Complete')->orderBy('id','DESC')->get();
        return view('dashboard.customerDashboard.complete_appointments',compact('appointments'));
    }//end caompleteAppointments function.
    public function cancelAppointments(){
        $appointments = CustomerAppointment::where('customer_id', Auth::user()->id)->where('status','Cancel')->orderBy('id','DESC')->get();
        return view('dashboard.customerDashboard.cancel_appointment',compact('appointments'));
    }//end cancelAppointment function.
    public function customerProducts(){

        return view('dashboard.customerDashboard.products');
    }//end customerProducts function.
    public function customerProfileSetting(){
        $customerAppointments = CustomerAppointment::where('customer_id',Auth::id())->get();
        return view('dashboard.customerDashboard.customer_profile_setting',compact('customerAppointments'));
    }//end customerProfileSetting function.

    //staff Dashboard Pages
    public function staffDashboard(){
        return view('dashboard.staffDashboard.index');
    }//end staffDashboard function.
    public function assignedAppointments(){
//        return $assignedAppointments = CustomerAppointment::whereIn('status', ['Pending','Approved'])->where('salon_id', Auth::user()->salon_id)->get();
        $assignedAppointments = AssignedCustomer::whereHas(
            'appoitmentId', function ($q) {
            $q->whereNotIn('status',['Complete']);
        })->where('employee_id', Auth::id())->orderBy('id','DESC')->get();
//        $assignedAppointments = CustomerAppointment::where('salon_id', Auth::user()->salon_id)->get();
        return view('dashboard.staffDashboard.assigned_appointments', compact('assignedAppointments'));
    }//end assignedAppointments function.
    public function completedAppointments(){
        $appointments = AssignedCustomer::where('employee_id', Auth::id())->whereHas('appoitmentId', function($q){
            $q->where('id', '!=', null);
        })->get();
        return view('dashboard.staffDashboard.completed_appointments',compact('appointments'));
    }//end completedAppointments function.
    public function employeeStockOut(){
        $purchaseOrders = PurchaseOrder::where('employee_id',Auth::id())->where('salon_id',Auth::user()->salon_id)->where('status','services')->get();
        return view('dashboard.staffDashboard.employee_stock_out', compact('purchaseOrders'));
    }
//    public function expense(){
//        return view('dashboard.businessDashboard.expense');
//    }//end expense function.
//
//    public function addExpense(){
//        return view('dashboard.businessDashboard.add_expense');
//    }//end addExpense function.
//
//    public function revenue(){
//        return view('dashboard.businessDashboard.revenue');
//    }//end revenue function.
//
//    public function addRevenue(){
//        return view('dashboard.businessDashboard.add_revenue');
//    }//end addRevenue function.

    public function suppliers(){
        return view('dashboard.businessDashboard.suppliers');
    }//end suppliers function.

    public function addSuppliers(){
        return view('dashboard.businessDashboard.add_suppliers');
    }//end addsuppliers function.

    public function purchaseOrder(){
        return view('dashboard.businessDashboard.purchase_order');
    }//end purchase order function.

    public function addPurchase(){
        return view('dashboard.businessDashboard.add_purchase');
    }//end purchase order function.

    public function product(){
        return view('dashboard.businessDashboard.product');
    }//end product function.
    public function addProduct(){
        return view('dashboard.businessDashboard.add_product');
    }//end add_product function.
    public function stockIns(Request $request){
        if ($request->branch_id != null){
            $branches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('id', $request->branch_id)->get();
            $branchIds = $branches->pluck('id');
            $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                ->orderBy('user_id', 'ASC')
                ->orderBy('id', 'DESC')
                ->get()
                ->groupBy('user_id');
            $currentDate = now();
            $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                $dueDate = $subs->first()->fatoraPdf->due_date;
                return $dueDate->isPast();
            })->keys();
            $products = Product::whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->get();
            $userSubscription = UserSubscription::where('user_id',$branchIds)->orderBy('id','DESC')->first();
            $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
        }else{
            $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
            $branchIds = $allBranches->pluck('id');
            $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                ->orderBy('user_id', 'ASC')
                ->orderBy('id', 'DESC')
                ->get()
                ->groupBy('user_id');
            $currentDate = now();
            $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                $dueDate = $subs->first()->fatoraPdf->due_date;
                return $dueDate->isPast();
            })->keys();
            $products = Product::whereIn('salon_id',$branchIds)->whereNotIn('salon_id',$expiredUserIds)->get();
        }
        return view('dashboard.businessDashboard.Stock_ins',compact("products"));
    }//end stock-ins function.

    public function stockEntries(Request $request,$id){
        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
        $branchIds = $allBranches->pluck('id');
        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
            ->orderBy('user_id', 'ASC')
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy('user_id');
        $currentDate = now();
        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
            $dueDate = $subs->first()->fatoraPdf->due_date;
            return $dueDate->isPast();
        })->keys();
        $product = Product::whereIn('salon_id', $branchIds)->whereNotIn('salon_id', $expiredUserIds)->where('id', $id)->firstOrFail();
        $suppliers = Supplier::where('salon_id',Auth::id())->get();
        return view('dashboard.businessDashboard.stock_entries',compact('product','suppliers'));
    }//end stock-ins function.
    public function productInventry(Request $request)
    {
        $requestData = $request->all();
        extract($requestData);

        if (!empty($product_id)) {
            if (is_array($product_id)) {
                foreach ($product_id as $key => $value) {
                    if ($request->depreciation_date != null){
                        $productInventory = ProductInventory::create([
                            'product_id' => $value,
                            'total_cost_with_vat' => $total_cost_with_vat[$key] ?? $total_cost_with_vat,
                            'total_cost_without_vat' => $total_cost_without_vat[$key] ?? $total_cost_without_vat,
                            'quantity' => $quantity[$key] ?? $quantity,
                            'per_cost_price' => $per_cost_price[$key] ?? $per_cost_price,
                            'price' => $per_cost_price[$key] ?? $per_cost_price,
                            'expiry_date' => $expiry_date[$key] ?? $expiry_date ?: '',
                            'sku_id' => $sku_id[$key] ?? $sku_id,
                            'supplier_id' => $supplier_id[$key] ?? $supplier_id ?: '',
                            'vat'=>$vat[$key]??$vat?:'',
                            'depreciation_date' => $depreciation_date[$key] ?? $depreciation_date ?: '',
                            'shelf' => $shelf[$key] ?? $shelf ?: '',
                            'status' => 'equipment',
                        ]);
                        $expense = Expense::create([
                            'name' => $productInventory->products->title,
                            'product_inventory_id' => $productInventory->id,
                            'saloon_id' => Auth::user()->salon_id??"",
                            'vendor' => $productInventory->getSupplier->name??"",
                            'expense_category_id' => '5',
                            'amount' => $productInventory->total_cost_without_vat??"",
                            'total_amount' => $productInventory->total_cost_with_vat??"",
                            'tax' => $productInventory->vat,
                            'date' => date('Y-m-d')??"",
                        ]);
                        $data = [
                            'productInventory' => $productInventory,
                            'expense' => $expense,
                        ];
//                        \Log::info('Depreciatopmn Data: ' . $data);
                        $custom = CustomNotification::create(
                            [
                                'notifiable_id'   => $expense->saloon_id,
                                'notifiable_type' => 'App\User',
                                'type'            => 'addEntriesDepreciation',
                                'data'            => $data
                            ]
                        );
                    }else{
                        $productInventory = ProductInventory::create([
                            'product_id' => $value,
                            'total_cost_with_vat' => $total_cost_with_vat[$key] ?? $total_cost_with_vat,
                            'total_cost_without_vat' => $total_cost_without_vat[$key] ?? $total_cost_without_vat,
                            'quantity' => $quantity[$key] ?? $quantity,
                            'per_cost_price' => $per_cost_price[$key] ?? $per_cost_price,
                            'price' => $sale_price[$key] ?? $sale_price,
                            'expiry_date' => $expiry_date[$key] ?? $expiry_date ?: '',
                            'sku_id' => $sku_id[$key] ?? $sku_id,
                            'supplier_id' => $supplier_id[$key] ?? $supplier_id ?: '',
                            'vat'=>$vat[$key]??$vat?:'',
                            'shelf' => $shelf[$key] ?? $shelf ?: '',
                        ]);
                        $expense = Expense::create([
                            'name' => $productInventory->products->title,
                            'product_inventory_id' => $productInventory->id,
                            'saloon_id' => Auth::user()->salon_id??"",
                            'vendor' => $productInventory->getSupplier->name??"",
                            'expense_category_id' => '5',
                            'amount' => $productInventory->total_cost_without_vat??"",
                            'total_amount' => $productInventory->total_cost_with_vat??"",
                            'tax' => $productInventory->vat,
                            'date' => date('Y-m-d')??"",
                        ]);
                    }
                    $data = [
                        'productTitle'    => $expense->name??'', //Product title name
                        'vendor'          => $expense->vendor??'', //Vendor name
                        'totalAmount'     => $expense->total_amount??'', //Total amount
                        'productQuantity' => $productInventory->quantity??'', //Product quantity
                        'productSku'      => $productInventory->sku_id??'', //Product sku
                        'type'            => 'products',
                        'template'            => 'addEntries', //Type of notification
                    ];
                    $custom = CustomNotification::create(
                        [
                            'notifiable_id'   => $expense->saloon_id,
                            'notifiable_type' => 'App\User',
                            'type'            => 'InventoryProduct',
                            'data'            => $data
                        ]
                    );
                }
            }
        }
        return response()->json([
            'title'=>'Done',
            'message'=> 'Inventories created successfully',
            'type'=>'success'
        ]);
    }
    public function deleteStockEntries(Request $request){
        $productInventory = ProductInventory::where('id', $request->id)->first();
        if (!$productInventory) {
            return response()->json(['error' => 'Product inventory not found'], 404);
        }
        if ($productInventory->consumed_quantity == 0){
            if ($productInventory->productInventoryExpense) {
                $productInventory->productInventoryExpense->delete();
            }
            $productInventory->delete();
        }else{
//            $quantity = $productInventory->quantity;
//            $consumedQuantity = $productInventory->consumed_quantity;
//            $overAll = $quantity + $consumedQuantity;
            if ($productInventory->productInventoryExpense) {
                $productInventory->productInventoryExpense->update(['is_deleted' => '1']);
            }
            $productInventory->update(['is_deleted' => '1']);
        }
    }

    public function stockOut(){
        return view('dashboard.businessDashboard.stock_out');
    }
    public function stackOutDetails(){
        return view('dashboard.businessDashboard.stack_out_details');
    }
    public function stackOutSubmission(){
        return view('dashboard.businessDashboard.stack_out_submission');
    }
    //end stock-ins function.

    public function statictics(){
        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
        $branchIds = $allBranches->pluck('id');
        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
            ->orderBy('user_id', 'ASC')
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy('user_id');
        $currentDate = now();
        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
            $dueDate = $subs->first()->fatoraPdf->due_date;
            return $dueDate->isPast();
        })->keys();
        $customerAppointments = CustomerAppointment::where('salon_id',Auth::user()->id)
            ->whereNotIn('salon_id', $expiredUserIds)
            ->where('status', 'Complete')
            ->orderBy('id', 'DESC')
            ->get();
        $revenue = Revenue::where('saloon_id',Auth::user()->id)->get();
        $customerAppointmentsItems = $customerAppointments;
        $revenueItems = $revenue;
        $allRevenue = collect(array_merge($customerAppointmentsItems->all(), $revenueItems->all()));
        $onlineClient = $allRevenue->filter(function ($item) {
            return $item->client_type === 'Online' || (isset($item->customer->customerType) && $item->customer->customerType->name === 'Online');
        })->count();
        $walkinClient = $allRevenue->filter(function ($item) {
            return $item->client_type === 'Walk In' || (isset($item->customer->customerType) && $item->customer->customerType->name === 'Walk In');
        })->count();
        $onlineClientRevenue = $allRevenue->filter(function ($item) {
            return $item->client_type === 'Online' || (isset($item->customer->customerType) && $item->customer->customerType->name === 'Online');
        })->sum(function ($item) {
            return $item->total_amount ?? $item->appointmentPrice ?? 0;
        });
        $walkinClientRevenue = $allRevenue->filter(function ($item) {
            return $item->client_type === 'Walk In' || (isset($item->customer->customerType) && $item->customer->customerType->name === 'Walk In');
        })->sum(function ($item) {
            return $item->total_amount ?? $item->appointmentPrice ?? 0;
        });
        $totalExpense = Expense::where('saloon_id',Auth::user()->id)->where('is_deleted','0')->sum('total_amount');
        $totalRevenue = $allRevenue->map(function ($item) {
            return $item->total_amount ?? $item->appointmentPrice ?? 0;
        })->sum();
        $netProfit = $totalRevenue - $totalExpense;
        $netMargin = ($totalRevenue > 0) ? ($netProfit / $totalRevenue) * 100 : 0;

        $startOfYear = Carbon::now()->startOfYear();
        $endOfYear = Carbon::now()->endOfYear();
        $monthlyRevenue = $allRevenue->filter(function ($item) use ($startOfYear, $endOfYear) {
            $date = Carbon::parse($item->created_at);
            return $date->between($startOfYear, $endOfYear);
        })->map(function ($item) {
            return $item->total_amount ?? $item->appointmentPrice ?? 0;
        })->sum();

        $numberOfMonths = 12;
        $averageMonthlyEarning = ($numberOfMonths > 0) ? $monthlyRevenue / $numberOfMonths : 0;
        $employee = User::whereHas(
            'roles', function($q){
            $q->where('name', 'employee');
        })->where('salon_id',Auth::user()->id)->get();
        $top3employees = $employee->sortByDesc('cutomerPayments');
        $months = [];
        for ($date = $startOfYear->copy(); $date->lte($endOfYear); $date->addMonth()) {
            $months[$date->format('F Y')] = 0;
        }
        $monthlyRevenues = $allRevenue->filter(function ($item) use ($startOfYear, $endOfYear) {
            return Carbon::parse($item->date ?? $item->customer_appointment_date)->between($startOfYear, $endOfYear);
        })->groupBy(function ($item) {
            return Carbon::parse($item->date ?? $item->customer_appointment_date)->format('Y-m');
        })->map(function ($items) {
            return $items->sum(function ($item) {
                return $item->total_amount ?? $item->appointmentPrice ?? 0;
            });
        });
        $monthlyRevenuesArray = $monthlyRevenues->mapWithKeys(function ($sum, $month) {
            return [Carbon::createFromFormat('Y-m', $month)->format('F Y') => $sum];
        })->toArray();
        $monthlyRevenueFormatted = array_merge($months, $monthlyRevenuesArray);
        $monthlyExpenses = Expense::where('saloon_id', Auth::user()->id)
            ->where('is_deleted','0')
            ->whereBetween('date', [$startOfYear, $endOfYear])
            ->selectRaw('MONTH(date) as month, YEAR(date) as year, SUM(total_amount) as total')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->mapWithKeys(function ($item) {
                $date = Carbon::createFromFormat('!m', $item->month)->year($item->year)->format('F Y');
                return [$date => $item->total];
            })
            ->toArray();
        $monthlyExpensesFormatted = array_merge($months, $monthlyExpenses);
        $completedCustomerAppointmentsIds = $customerAppointments
            ->where('status', 'Complete')
            ->pluck('id')
            ->toArray();
        $services = CustomerService::whereIn('appointment_id', $completedCustomerAppointmentsIds)
            ->with('salonService')
            ->get();
        $groupedData = $services->map(function ($item) {
            $item->service_price = (int) $item->service_price;
            return $item;
        })->groupBy('salon_service_id');
        $result = $groupedData->map(function ($group) {
            $serviceName = $group->first()->salonService->name ?? 'Unknown Service';
            return [
                'count' => $group->count(),
                'sum' => $group->sum('service_price'),
                'name' => $serviceName,
            ];
        })->values();
        $servicesTypes = $result->toArray();
        $appointmentDates = $customerAppointments
            ->whereIn('id', $completedCustomerAppointmentsIds)
            ->pluck('customer_appointment_date', 'id')
            ->map(function ($date) {
                return $date ? Carbon::parse($date) : null;
            });
        $servicesWithDates = $services->map(function ($service) use ($appointmentDates) {
            $appointmentDate = $appointmentDates->get($service->appointment_id);
            $service->appointment_date = $appointmentDate;
            return $service;
        })->filter(function ($service) {
            return !is_null($service->appointment_date);
        });
        $monthlyData = $servicesWithDates->groupBy(function ($service) {
            return $service->appointment_date->format('F Y');
        })->map(function ($groupByMonth, $month) {
            $groupedByServiceType = $groupByMonth->groupBy('salon_service_id')->map(function ($group) {
                $serviceName = $group->first()->salonService->name ?? 'Unknown Service';
                return [
                    'name' => $serviceName,
                    'sum' => $group->sum('service_price'),
                    'count' => $group->count()
                ];
            });
            return [
                'month' => $month,
                'services' => $groupedByServiceType->values()->toArray()
            ];
        });
        $monthlyDataArray = $monthlyData->values()->toArray();
        $purchaseingProducts = StockOut::where('salon_id',Auth::user()->id)
            ->whereNotIn('salon_id', $expiredUserIds)
            ->whereNull('status')
            ->get();
        $groupedDataProduct = $purchaseingProducts->map(function ($item) {
            $item->total_price_per_product = (int) $item->total_price_per_product;
            return $item;
        })->groupBy('product_id');
        $resultProduct = $groupedDataProduct->map(function ($group) {
            $productName = $group->first()->product->title ?? 'Unknown Product';
            return [
                'count' => $group->count(),
                'sum' => $group->sum('total_price_per_product'),
                'name' => $productName,
            ];
        })->values();
        $productTypes = $resultProduct->toArray();
        $monthlyDataProduct = $purchaseingProducts->groupBy(function ($product) {
            return $product->created_at->format('F Y');
        })->map(function ($groupByMonth, $month) {
            $groupedByProductType = $groupByMonth->groupBy('product_id')->map(function ($group) {
                $productName = $group->first()->product->title ?? 'Unknown Product';
                return [
                    'name' => $productName,
                    'sum' => $group->sum('total_price_per_product'),
                    'count' => $group->count()
                ];
            });
            return [
                'month' => $month,
                'products' => $groupedByProductType->values()->toArray()
            ];
        });
        $monthlyDataArrayProduct = $monthlyDataProduct->values()->toArray();
        return view('dashboard.businessDashboard.statictics', [
            'onlineClient' => $onlineClient,
            'walkinClient' => $walkinClient,
            'onlineClientRevenue' => $onlineClientRevenue,
            'walkinClientRevenue' => $walkinClientRevenue,
            'totalExpense' => $totalExpense,
            'totalRevenue' => $totalRevenue,
            'netProfit' => $netProfit,
            'netMargin' => $netMargin,
            'averageMonthlyEarning' => $averageMonthlyEarning,
            'top3employees' => $top3employees,
            'monthlyRevenueFormatted' => $monthlyRevenueFormatted,
            'monthlyExpensesFormatted' => $monthlyExpensesFormatted,
            'servicesTypes' => $servicesTypes,
            'monthlyDataArray' => $monthlyDataArray,
            'productTypes' => $productTypes,
            'monthlyDataArrayProduct' => $monthlyDataArrayProduct,
        ]);
    }//end statistics function.

    //start seviceschart data ajax
    public function getServicesChartData(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
        $branchIds = $allBranches->pluck('id');
        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
            ->orderBy('user_id', 'ASC')
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy('user_id');
        $currentDate = now();
        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
            $dueDate = $subs->first()->fatoraPdf->due_date;
            return $dueDate->isPast();
        })->keys();
        $customerAppointments = CustomerAppointment::where('salon_id', Auth::user()->id)
            ->whereNotIn('salon_id', $expiredUserIds)
            ->orderBy('id', 'DESC')
            ->get();
        $completedCustomerAppointmentsIds = $customerAppointments
            ->where('status', 'Complete')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->pluck('id')
            ->toArray();

        $services = CustomerService::whereIn('appointment_id', $completedCustomerAppointmentsIds)
            ->with('salonService')
            ->get();

        $groupedData = $services->map(function ($item) {
            $item->service_price = (int) $item->service_price;
            return $item;
        })->groupBy('salon_service_id');

        $result = $groupedData->map(function ($group) {
            $serviceName = $group->first()->salonService->name ?? 'Unknown Service';
            return [
                'count' => $group->count(),
                'sum' => $group->sum('service_price'),
                'name' => $serviceName,
            ];
        })->values();

        return response()->json([
            'servicesTypes' => $result
        ]);
    }
    public function getProductsChartData(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
        $branchIds = $allBranches->pluck('id');
        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
            ->orderBy('user_id', 'ASC')
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy('user_id');
        $currentDate = now();
        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
            $dueDate = $subs->first()->fatoraPdf->due_date;
            return $dueDate->isPast();
        })->keys();
        $purchaseingProducts = StockOut::where('salon_id',Auth::user()->id)
            ->whereNotIn('salon_id', $expiredUserIds)
            ->whereNull('status')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();
        $groupedDataProduct = $purchaseingProducts->map(function ($item) {
            $item->total_price_per_product = (int) $item->total_price_per_product;
            return $item;
        })->groupBy('product_id');
        $resultProduct = $groupedDataProduct->map(function ($group) {
            $productName = $group->first()->product->title ?? 'Unknown Product';
            return [
                'count' => $group->count(),
                'sum' => $group->sum('total_price_per_product'),
                'name' => $productName,
            ];
        })->values();
        return response()->json([
            'productsTypes' => $resultProduct
        ]);
    }
    //start client type chart data ajax
    public function getClientTypeChart(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();

        $branchIds = $allBranches->pluck('id');
        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
            ->orderBy('user_id', 'ASC')
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy('user_id');
        $currentDate = now();
        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
            $dueDate = $subs->first()->fatoraPdf->due_date;
            return $dueDate->isPast();
        })->keys();
        $customerAppointments = CustomerAppointment::where('salon_id', Auth::user()->id)
            ->whereNotIn('salon_id', $expiredUserIds)
            ->orderBy('id', 'DESC')
            ->get();

        $customerAppointmentsItems = $customerAppointments
            ->filter(function ($item) use ($startDate, $endDate) {
                $appointmentDate = \Carbon\Carbon::parse($item->customer_appointment_date);
                return $appointmentDate->between($startDate, $endDate);
            });
        $revenue = Revenue::where('saloon_id', Auth::user()->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->get();
        $revenueItems = $revenue;
        $allRevenue = collect(array_merge($customerAppointmentsItems->all(), $revenueItems->all()));
        $onlineClientCount = $allRevenue->filter(function ($item) {
            return $item->client_type === 'Online' || (isset($item->customer->customerType) && $item->customer->customerType->name === 'Online');
        })->count();
        $walkinClientCount = $allRevenue->filter(function ($item) {
            return $item->client_type === 'Walk In' || (isset($item->customer->customerType) && $item->customer->customerType->name === 'Walk In');
        })->count();
        $onlineClientRevenue = $allRevenue->filter(function ($item) {
            return $item->client_type === 'Online' || (isset($item->customer->customerType) && $item->customer->customerType->name === 'Online');
        })->sum(function ($item) {
            return $item->total_amount ?? $item->appointmentPrice ?? 0;
        });
        $walkinClientRevenue = $allRevenue->filter(function ($item) {
            return $item->client_type === 'Walk In' || (isset($item->customer->customerType) && $item->customer->customerType->name === 'Walk In');
        })->sum(function ($item) {
            return $item->total_amount ?? $item->appointmentPrice ?? 0;
        });
        return response()->json([
            'clientTypes' => [
                ['name' => 'Online', 'sum' => $onlineClientCount,'revenue'=>$onlineClientRevenue],
                ['name' => 'Walk-In', 'sum' => $walkinClientCount,'revenue'=>$walkinClientRevenue]
            ]
        ]);
    }//end client type chart data ajax
    //start total revenue stats ajax
    public function getTotalRevenue(Request $request){
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
        $branchIds = $allBranches->pluck('id');
        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
            ->orderBy('user_id', 'ASC')
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy('user_id');
        $currentDate = now();
        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
            $dueDate = $subs->first()->fatoraPdf->due_date;
            return $dueDate->isPast();
        })->keys();
        $customerAppointments = CustomerAppointment::where('salon_id', Auth::user()->id)
            ->whereNotIn('salon_id', $expiredUserIds)
            ->where('status', 'Complete')
            ->orderBy('id', 'DESC')
            ->get();
        $customerAppointmentsItems = $customerAppointments
            ->filter(function ($item) use ($startDate, $endDate) {
                $appointmentDate = \Carbon\Carbon::parse($item->customer_appointment_date);
                return $appointmentDate->between($startDate, $endDate);
            });
        $revenue = Revenue::where('saloon_id', Auth::user()->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->get();
        $revenueItems = $revenue;
        $allRevenue = collect(array_merge($customerAppointmentsItems->all(), $revenueItems->all()));
        $totalExpense = Expense::where('saloon_id',Auth::user()->id)->where('is_deleted','0')->whereBetween('date', [$startDate, $endDate])
            ->sum('total_amount');
        $totalRevenue = $allRevenue->map(function ($item) {
            return $item->total_amount ?? $item->appointmentPrice ?? 0;
        })->sum();
        $netProfit = $totalRevenue - $totalExpense;
        $netMargin = ($totalRevenue > 0) ? ($netProfit / $totalRevenue) * 100 : 0;
        $startOfYear = Carbon::now()->startOfYear();
        $endOfYear = Carbon::now()->endOfYear();
        $monthlyRevenue = $allRevenue->filter(function ($item) use ($startOfYear, $endOfYear) {
            $date = Carbon::parse($item->created_at);
            return $date->between($startOfYear, $endOfYear);
        })->map(function ($item) {
            return $item->total_amount ?? $item->appointmentPrice ?? 0;
        })->sum();
        $numberOfMonths = 12;
        $averageMonthlyEarning = ($numberOfMonths > 0) ? $monthlyRevenue / $numberOfMonths : 0;
        return response()->json([
            'totalExpense' => $totalExpense,
            'totalRevenue'=> $totalRevenue,
            'netProfit'=>$netProfit,
            'netMargin'=>$netMargin,
            'averageMonthlyEarning'=>$averageMonthlyEarning
        ]);
    }//end total revenue stats ajax

    // employee leaderboard ajax
    public function getEmployeeLeaderboard(Request $request)
    {
        try {
            $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
            $branchIds = $allBranches->plcuck('id');
            $startDate = Carbon::parse($request->input('start_date'));
            $endDate = Carbon::parse($request->input('end_date'));
            $startDateFormatted = $startDate->format('m/d/Y');
            $endDateFormatted = $endDate->format('m/d/Y');
            $employees = User::whereHas('roles', function($q) {
                $q->where('name', 'employee');
            })->where('salon_id', Auth::user()->id)
                ->with('profile')
                ->get();
            $completedAppointmentsData = [];
            foreach ($employees as $employee) {
                $employeeId = $employee->id;
                $employeeName = $employee->name;
                $profilePic = $employee->profile->pic ?? null;
                $appointmentIds = AssignedCustomer::where('employee_id', $employeeId)
                    ->pluck('appointment_id')
                    ->toArray();
                $completedAppointments = CustomerAppointment::where('status', 'Complete')
                    ->whereIn('id', $appointmentIds)
                    ->get();
                $filteredAppointments = $completedAppointments->filter(function ($appointment) use ($startDateFormatted, $endDateFormatted) {
                    $appointmentDate = Carbon::createFromFormat('m/d/Y', $appointment->customer_appointment_date);
                    return $appointmentDate->between($startDateFormatted, $endDateFormatted);
                });
                $filteredAppointmentIds = $filteredAppointments->pluck('id')->toArray();
                $completedCount = count($filteredAppointmentIds);
                $customerServiceIds = CustomerService::whereIn('appointment_id', $filteredAppointmentIds)
                    ->pluck('salon_service_id')
                    ->toArray();
                $servicePrices = SalonService::whereIn('id', $customerServiceIds)
                    ->pluck('price', 'id')
                    ->toArray();
                $totalPrice = 0;
                foreach ($customerServiceIds as $serviceId) {
                    if (isset($servicePrices[$serviceId])) {
                        $totalPrice += $servicePrices[$serviceId];
                    }
                }
                $completedAppointmentsData[$employeeId] = [
                    'name' => $employeeName,
                    'profile_pic' => $profilePic,
                    'count' => $completedCount,
                    'total_price' => $totalPrice
                ];
            }
            usort($completedAppointmentsData, function ($a, $b) {
                return $b['total_price'] <=> $a['total_price'];
            });
            return response()->json($completedAppointmentsData);
        } catch (\Exception $e) {
            Log::error('Error fetching employee leaderboard: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred while fetching the leaderboard.'], 500);
        }
    }//end

    // start Revenue vs Expense ajax
    public function getRevenueVsExpense(Request $request){

        $year = $request->input('year');
        $year = $year ?: now()->year;
        $startOfYear = Carbon::create($year, 1, 1)->startOfYear();
        $endOfYear = Carbon::create($year, 12, 31)->endOfYear();

        $allBranches = User::whereHas('roles', function ($query) {
            $query->where('name', 'spa_salon');
        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
        $branchIds = $allBranches->pluck('id');
        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
            ->orderBy('user_id', 'ASC')
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy('user_id');
        $currentDate = now();
        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
            $dueDate = $subs->first()->fatoraPdf->due_date;
            return $dueDate->isPast();
        })->keys();
        $customerAppointments = CustomerAppointment::where('salon_id', Auth::user()->id)
            ->whereNotIn('salon_id', $expiredUserIds)
            ->where('status', 'Complete')
            ->orderBy('id', 'DESC')
            ->get();
        $customerAppointmentsItems = $customerAppointments;

        $revenue = Revenue::where('saloon_id', Auth::user()->id)->get();
        $revenueItems = $revenue;
        $allRevenue = collect(array_merge($customerAppointmentsItems->all(), $revenueItems->all()));
//        $monthlyRevenue = $allRevenue->filter(function ($item) use ($startOfYear, $endOfYear) {
//            $date = Carbon::parse($item->date);
//            $customerAppointmentDate = Carbon::parse($item->customer_appointment_date);
//            return $date->between($startOfYear, $endOfYear) || $customerAppointmentDate->between($startOfYear, $endOfYear);
//        })->map(function ($item) {
//            return $item->total_amount ?? $item->appointmentPrice ?? 0;
//        })->sum();
//        $numberOfMonths = 12;
//        $averageMonthlyEarning = ($numberOfMonths > 0) ? $monthlyRevenue / $numberOfMonths : 0;
        $months = [];
        for ($date = $startOfYear->copy(); $date->lte($endOfYear); $date->addMonth()) {
            $months[$date->format('F Y')] = 0;
        }
        $monthlyRevenues = $allRevenue->filter(function ($item) use ($startOfYear, $endOfYear) {
//            return Carbon::parse($item->date)->between($startOfYear, $endOfYear);
            return Carbon::parse($item->date ?? $item->customer_appointment_date)->between($startOfYear, $endOfYear);
        })->groupBy(function ($item) {
//            return Carbon::parse($item->date)->format('Y-m');
            return Carbon::parse($item->date ?? $item->customer_appointment_date)->format('Y-m');
        })->map(function ($items) {
            return $items->sum(function ($item) {
                return $item->total_amount ?? $item->appointmentPrice ?? 0;
            });
        });
        $monthlyRevenuesArray = $monthlyRevenues->mapWithKeys(function ($sum, $month) {
            return [Carbon::createFromFormat('Y-m', $month)->format('F Y') => $sum];
        })->toArray();
        $monthlyRevenueFormatted = array_merge($months, $monthlyRevenuesArray);
        $monthlyExpenses = Expense::where('saloon_id', Auth::user()->id)
            ->where('is_deleted','0')
            ->whereBetween('date', [$startOfYear, $endOfYear])
            ->selectRaw('MONTH(date) as month, YEAR(date) as year, SUM(total_amount) as total')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->mapWithKeys(function ($item) {
                $date = Carbon::createFromFormat('!m', $item->month)->year($item->year)->format('F Y');
                return [$date => $item->total];
            })
            ->toArray();
        $monthlyExpensesFormatted = array_merge($months, $monthlyExpenses);
        return response()->json([
            'monthlyRevenueFormatted' => $monthlyRevenueFormatted,
//            'averageMonthlyEarning' => $averageMonthlyEarning,
            'monthlyExpensesFormatted' => $monthlyExpensesFormatted
        ]);
    }//end
    //export Excel//
    public function exportExcel(Request $request)
    {
        try {
            $startDate = Carbon::parse($request->input('start_date'));
            $endDate = Carbon::parse($request->input('end_date'));
            $saloonOwner = User::findOrFail(Auth::user()->id);
            $saloonName = $saloonOwner->name;
            $saloonPicture = $saloonOwner->profile ? $saloonOwner->profile->pic:"not available";
            $saloonAddress = $saloonOwner->profile ? $saloonOwner->profile->address : 'Address not available';
            $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id', 'ASC')->get();
            $branchIds = $allBranches->pluck('id');
            $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                ->orderBy('user_id', 'ASC')
                ->orderBy('id', 'DESC')
                ->get()
                ->groupBy('user_id');
            $currentDate = now();
            $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                $dueDate = $subs->first()->fatoraPdf->due_date;
                return $dueDate->isPast();
            })->keys();
            $customerAppointments = CustomerAppointment::where('salon_id', Auth::user()->id)
                ->whereNotIn('salon_id', $expiredUserIds)
                ->where('status', 'Complete')
                ->get();
            $customerAppointmentsItems = $customerAppointments->filter(function ($item) use ($startDate, $endDate) {
                $appointmentDate = Carbon::parse($item->customer_appointment_date);
                return $appointmentDate->between($startDate, $endDate);
            });
            $revenue = Revenue::where('saloon_id', Auth::user()->id)
                ->whereBetween('date', [$startDate, $endDate])
                ->get();
            $allRevenue = $customerAppointmentsItems->merge($revenue);
            $totalRevenue = $allRevenue->map(function ($item) {
                return $item->total_amount ?? $item->appointmentPrice ?? 0;
            })->sum();
            $onlineClientRevenue = $allRevenue->filter(function ($item) {
                return $item->client_type === 'Online' || (isset($item->customer_appointment_type) && $item->customer_appointment_type === 'Online');
            })->sum(function ($item) {
                return $item->total_amount ?? $item->appointmentPrice ?? 0;
            });
            $walkinClientRevenue = $allRevenue->filter(function ($item) {
                return $item->client_type === 'Walk In' || (isset($item->customer_appointment_type) && $item->customer_appointment_type === 'Walk-In');
            })->sum(function ($item) {
                return $item->total_amount ?? $item->appointmentPrice ?? 0;
            });
            $otherIncomes = $allRevenue->sum(function ($item) {
                return $item->total_amount;
            });
            $completedCustomerAppointmentsIds = $customerAppointmentsItems
                ->where('status', 'Complete')
                ->pluck('id')
                ->toArray();
            $services = CustomerService::whereIn('appointment_id', $completedCustomerAppointmentsIds)
                ->with('salonService.getServiceCategory') // Eager load the category relationship
                ->get();
            $groupedData = $services->map(function ($item) {
                $item->service_price = (int)$item->service_price;
                return $item;
            })->groupBy(function ($item) {
                return $item->salonService->getServiceCategory->name ?? 'Unknown Category'; // Group by category name
            });
            $result = $groupedData->map(function ($group, $category) {
                return [
                    'name' => $category, // Return the category name
                    'count' => $group->count(), // Count of services in the category
                    'sum' => $group->sum('service_price'), // Sum of prices by category
                ];
            })->values();
            $totalAmountOfServices = $result->sum('sum');
            $servicesTypes = $result->toArray();
            $expenseCategoryTotal = Expense::where('saloon_id', Auth::user()->id)
                ->where('is_deleted','0')
                ->whereBetween('date', [$startDate, $endDate])
                ->select('expense_category_id', DB::raw('SUM(total_amount) as total_amount'))
                ->groupBy('expense_category_id')
                ->with('expenseCategory')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [
                        $item->expenseCategory->name => $item->total_amount
                    ];
                })
                ->toArray();
            $totalExpense = array_sum($expenseCategoryTotal);
            $netProfit = $totalRevenue - $totalExpense;
            $netMargin = ($totalRevenue > 0) ? ($netProfit / $totalRevenue) * 100 : 0;
            $admin = User::findOrFail(2);
            $excelData = [
                'saloonName' => $saloonName,
                'saloonPicture' => $saloonPicture,
                'saloonAddress' => $saloonAddress,
                'dateCreated' => now()->format('d M, Y'),
                'dateIssued' => now()->format('d M, Y'),
                'period' => $startDate->format('d M, Y') . ' - ' . $endDate->format('d M, Y'),
                'servicesTypes' => $servicesTypes,
                'expenses' => $expenseCategoryTotal,
                'netProfit' => $netProfit,
                'netMargin' => $netMargin,
                'totalRevenue' => $totalRevenue,
                'onlineClientRevenue' => $onlineClientRevenue,
                'walkInClientRevenue' => $walkinClientRevenue,
                'otherIncomes' => $otherIncomes,
                'totalExpense' => $totalExpense,
                'totalAmountOfServices'=>$totalAmountOfServices,
                'logo' => $admin->profile->invoice_logo ??'assets/images/liink-logo-print.png',
                'poweredBy' => $admin->last_name ??'LIINK',
            ];
            if($request->export == "excel") {
                return Excel::download(new FinancialReportExport($excelData), 'Income Statement - ' . $startDate->format('d M, Y') . ' till ' . $endDate->format('d M, Y') . '.xlsx');
            }else{
                $htmlContent = view('website.pdf.export_revenue', $excelData)->render();
                $apiKey = 'qx8zb9S9oKr1P61wV23x';
                $curl = curl_init();
                curl_setopt_array($curl, [
                    CURLOPT_URL => "https://docraptor.com/docs",
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_POST => true,
                    CURLOPT_POSTFIELDS => http_build_query([
                        'user_credentials' => $apiKey,
                        'test' => true,
                        'document_type' => 'pdf',
                        'name' => 'Income Statement - ' . $startDate->format('d M, Y') . ' till ' . $endDate->format('d M, Y') . '.pdf',
                        'doc' => [
                            'document_content' => $htmlContent,
                        ],
                        'js' => true,
                    ]),
                    CURLOPT_HTTPHEADER => [
                        'Content-Type: application/x-www-form-urlencoded',
                    ],
                ]);
                $response = curl_exec($curl);
                if (curl_errno($curl)) {
                    return response()->json(['error' => 'cURL Error: ' . curl_error($curl)], 500);
                }
                curl_close($curl);
                if ($response) {
                    $fileName = 'Income Statement - ' . $startDate->format('d M, Y') . ' till ' . $endDate->format('d M, Y') . '.pdf';
                    file_put_contents(public_path($fileName), $response);
                    return response()->download(public_path($fileName));
                } else {
                    return response()->json(['error' => 'Failed to generate PDF'], 500);
                }

//                $fontPath = storage_path('fonts/Cairo/Cairo-Regular.ttf');
//
//// Register the Cairo font (ensure the font is available)
//                PDF::setOptions([
//                    'defaultFont' => 'Cairo', // فونٹ کا نام ملاپ کریں
//                    'isFontSubsettingEnabled' => true,
//                    'isHtml5ParserEnabled' => true,
//                    'isRemoteEnabled' => true, // اگر تصاویر استعمال ہو رہی ہیں
//                    'fontDir' => storage_path('fonts/Cairo/'), // فونٹ ڈائریکٹری
//                    'fontCache' => storage_path('fonts/Cairo/'), // فونٹ کیشے
//                ]);
//
//// Load your data and PDF view
//                $pdf = PDF::loadView('website.pdf.export_revenue', $excelData);
//
//// Set paper size (A4, portrait)
//                $pdf->setPaper('a4', 'portrait');
//
//// Download the generated PDF
//                return $pdf->download('Income Statement - ' . $startDate->format('d M, Y') . ' till ' . $endDate->format('d M, Y') . '.pdf');

            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }//end
    public function employeeExportExcel(Request $request)
    {
        try {
            $startDate = Carbon::parse($request->input('start_date'));
            $endDate = Carbon::parse($request->input('end_date'));
            $selectedEmployeeIds = $request->input('employee_id', []); // Get selected employee IDs
            $saloonOwner = User::findOrFail(Auth::user()->id);
            $saloonName = $saloonOwner->name;
            $saloonPicture = $saloonOwner->profile ? $saloonOwner->profile->pic : "not available";
            $saloonAddress = $saloonOwner->profile ? $saloonOwner->profile->address : 'Address not available';
            $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('id', Auth::user()->id)->get();
            $branchIds = $allBranches->pluck('id');
            $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                ->orderBy('user_id', 'ASC')
                ->orderBy('id', 'DESC')
                ->get()
                ->groupBy('user_id');
            $currentDate = now();
            $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                $dueDate = $subs->first()->fatoraPdf->due_date;
                return $dueDate->isPast();
            })->keys();
            $purchaseOrdersQuery = PurchaseOrder::where('salon_id', Auth::user()->id)
                ->whereNotIn('salon_id', $expiredUserIds)
                ->where('status', 'services')
                ->whereNotNull('employee_id')
                ->with(['employee', 'stockOuts']);
            if (!empty($selectedEmployeeIds)) {
                $purchaseOrdersQuery->whereIn('employee_id', $selectedEmployeeIds);
            }
            $purchaseOrders = $purchaseOrdersQuery->get();
            $purchaseOrderItems = $purchaseOrders->filter(function ($item) use ($startDate, $endDate) {
                $date = Carbon::parse($item->date);
                return $date->between($startDate, $endDate);
            });
            $groupedData = $purchaseOrderItems->map(function ($item) {
                $item->total_amount_with_vat = (int)$item->total_amount_with_vat;
                return $item;
            })->groupBy(function ($item) {
                return $item->employee->name ?? 'Unknown Employee'; // Group by Employee name
            })->map(function ($orders, $employeeName) {
                $products = [];
                foreach ($orders as $order) {
                    foreach ($order->stockOuts as $stockOut) {
                        $productId = $stockOut->product_id;
                        if (!isset($products[$productId])) {
                            $products[$productId] = [
                                'quantity' => 0,
                                'total_price_per_product' => 0,
                                'title' => $stockOut->product->title,
                            ];
                        }
                        $products[$productId]['quantity'] += $stockOut->quantity;
                        $products[$productId]['total_price_per_product'] += $stockOut->total_price_per_product;
                    }
                }
                return [
                    'products' => collect($products)->map(function ($data, $productId) {
                        return [
                            'product_id' => $productId,
                            'title' => $data['title'],
                            'quantity' => $data['quantity'],
                            'amount' => $data['total_price_per_product'],
                        ];
                    })->values(),
                ];
            });
            $finalData = $groupedData->mapWithKeys(function ($data, $employeeName) {
                return [
                    $employeeName => $data['products']
                ];
            });
            $employeesQuery = User::whereHas('roles', function ($q) {
                $q->where('name', 'employee');
            })->where('salon_id', Auth::user()->id)
                ->whereNotIn('salon_id', $expiredUserIds)
                ->orderBy('id', 'DESC');
            if (!empty($selectedEmployeeIds)) {
                $employeesQuery->whereIn('id', $selectedEmployeeIds);
            }
            $employees = $employeesQuery->get();
            $admin = User::findOrFail(2);
            $excelData = [
                'logo' => $admin->profile->invoice_logo ?? 'assets/images/liink-logo-print.png',
                'poweredBy' => $admin->last_name ?? 'LIINK',
                'saloonName' => $saloonName,
                'saloonPicture' => $saloonPicture,
                'saloonAddress' => $saloonAddress,
                'dateCreated' => now()->format('d M, Y'),
                'dateIssued' => now()->format('d M, Y'),
                'period' => $startDate->format('d M, Y') . ' - ' . $endDate->format('d M, Y'),
                'finalData' => $finalData,
                'employees' => $employees,
            ];
            if($request->export == "excel") {
                return Excel::download(new EmployeeReportExport($excelData), 'Performance Report - ' . $startDate->format('d M, Y') . ' till ' . $endDate->format('d M, Y') . '.xlsx');
            }else{
                $htmlContent = view('website.pdf.export_employee_statement', $excelData)->render();
                $apiKey = 'oVkxj4QCAzDf1ShaIZbC';
                $curl = curl_init();
                curl_setopt_array($curl, [
                    CURLOPT_URL => "https://docraptor.com/docs",
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_POST => true,
                    CURLOPT_POSTFIELDS => http_build_query([
                        'user_credentials' => $apiKey,
                        'test' => true,  // Enable Test Mode
                        'document_type' => 'pdf',
                        'name' => 'Employee Statement - ' . $startDate->format('d M, Y') . ' till ' . $endDate->format('d M, Y') . '.pdf',
                        'doc' => [
                            'document_content' => $htmlContent,
                        ],
                        'js' => true,
                    ]),
                    CURLOPT_HTTPHEADER => [
                        'Content-Type: application/x-www-form-urlencoded',
                    ],
                ]);
                $response = curl_exec($curl);
                if (curl_errno($curl)) {
                    return response()->json(['error' => 'cURL Error: ' . curl_error($curl)], 500);
                }
                curl_close($curl);
                if ($response) {
                    $fileName = 'Employee Statement - ' . $startDate->format('d M, Y') . ' till ' . $endDate->format('d M, Y') . '.pdf';
                    file_put_contents(public_path($fileName), $response);
                    return response()->download(public_path($fileName));
                } else {
                    return response()->json(['error' => 'Failed to generate PDF'], 500);
                }
            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }//end
    public function premiumAddonsSubscribe(){
        $premiumAddonSalonCashiers = PremiumAddonSalonCashier::get();
        $premiumAddonSalonEmployees = PremiumAddonSalonEmployee::get();
        return view('dashboard.adminDashboard.premium_addon_subscribers', compact('premiumAddonSalonCashiers','premiumAddonSalonEmployees'));
    }
    public function testing(Request $request)
    {
        if (!$request->hasFile('file') || !$request->file('file')->isValid()) {
            return response()->json(['error' => 'No valid file uploaded.'], 400);
        }
        $file = $request->file('file');
        $referenceDiameter = $request->input('reference_diameter_mm');
//        $baseUrl = "https://669f-182-176-110-198.ngrok-free.app/detect-nails/";
        $baseUrl = "https://nails-analysis.devcustomprojects.online/analyze";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        $postFields = [
            'file' => new \CURLFile($file->getRealPath(), $file->getMimeType(), $file->getClientOriginalName()),
            'reference_diameter_mm' => $referenceDiameter,
        ];
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $headers = [
            'Accept: application/json',
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        $response = curl_exec($ch);
        if ($response === false) {
            curl_close($ch);
            return response()->json(['error' => 'Unable to process your request at this moment.'], 500);
        }
        curl_close($ch);
        $responseData = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return response()->json(['error' => 'Invalid response from external API.'], 500);
        }
        if (isset($responseData['output_image'])) {
            return redirect()->to('https://nails-analysis.devcustomprojects.online/static/output.jpg');  // Dynamic redirect based on API response
        } else {
            return response()->json(['error' => 'Unable to process the image or request.'], 500);
        }
    }
}
