<?php

namespace App\Http\Controllers\Expense;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\Expense;
use App\User;
use Illuminate\Http\Request;
use App\ExpenseCategory;
use Storage;
use Auth;

class ExpenseController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('expense','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 2500000;

            if (!empty($keyword)) {
                $expense = Expense::where('name', 'LIKE', "%$keyword%")
                ->orWhere('date', 'LIKE', "%$keyword%")
                ->orWhere('expense_category_id', 'LIKE', "%$keyword%")
                ->orWhere('amount', 'LIKE', "%$keyword%")
                ->orWhere('vendor', 'LIKE', "%$keyword%")
                ->orWhere('total_amount', 'LIKE', "%$keyword%")
                ->orWhere('invoice_no', 'LIKE', "%$keyword%")
                ->orWhere('tax', 'LIKE', "%$keyword%")
                ->orWhere('attachments', 'LIKE', "%$keyword%")
                ->orWhere('status', 'LIKE', "%$keyword%")
                ->orWhere('time_cycle', 'LIKE', "%$keyword%")
                ->orWhere('start_date', 'LIKE', "%$keyword%")
                ->orWhere('end_date', 'LIKE', "%$keyword%")
                ->orWhere('description', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $expense = Expense::where('saloon_id',Auth::user()->salon_id)->where('is_deleted','0')->orderBy('id','DESC')->paginate($perPage);
                $expenseCategories = ExpenseCategory::get();
            }
            return view('expense.expense.index', compact('expense','expenseCategories'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('expense','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            $expenseCategories=ExpenseCategory::get();
           $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id',Auth::user()->salon_id)->orderBy('id','ASC')->get();

            return view('expense.expense.create',compact('expenseCategories','allBranches'));
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('expense','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
//        return Auth::user()->id;
            extract($request->all());
            if ($request->hasFile('attachment')) {
                $attachment = Storage::disk('website')->put('expense_attachments', $request->attachment);
            } else {
                $attachment = (NULL);
            }
            $expense = Expense::create([
                'saloon_id'=> Auth::user()->id??'',
                'branch_id'=>$branch,
                'name' => $expense_name??'',
                'date' => $expense_date??'',
                'expense_category_id' => $expense_category??'',
                'amount' => $amount??'',
                'vendor' => $vendor??"",
                'total_amount' => $total_amount??'',
                'invoice_no' => rand('1111','8888')??'',
                'tax' => $expense_tax??'',
                'is_recurring' => $recuring ?? '0',
                'time_cycle' => $time_cycle??null,
                'start_date' => $start_date??null,
                'end_date' => $end_date??null,
                'description' => $expense_description??'',
                'attachments'=>$attachment??'',
                'amortization_date'=>$amortization_date??'',
            ]);

            if ($expense) {
                return redirect(url('expense/expense'))->with(['title'=>'Done','message'=>'expense details saved successfully','type'=>'success']);
            }
            else{
                return redirect(url('expense/expense'))->with(['title'=>'Fail','message'=>'Unable to save expense details','type'=>'error']);
            }
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('expense','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $expense = Expense::where('id',$id)->whereIn('saloon_id', $this->getBranchIds())->firstOrFail();
            return view('expense.expense.show', compact('expense'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('expense','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $expense = Expense::where('id',$id)->whereIn('saloon_id', $this->getBranchIds())->firstOrFail();
            $expenseCategories=ExpenseCategory::get();
            $allBranches = User::whereHas('roles', function ($query) {
                $query->where('name', 'spa_salon');
            })->where('salon_id', Auth::user()->salon_id)->orWhere('id',Auth::user()->salon_id)->orderBy('id','ASC')->get();
            return view('expense.expense.edit', compact('expense','expenseCategories','allBranches'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('expense','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
//            $requestData = $request->all();
            extract($request->all());
            $expense = Expense::findOrFail($id);

            if ($request->hasFile('attachment')) {
                $attachment = Storage::disk('website')->put('expense_attachments', $request->attachment);
                Storage::disk('website')->delete($expense->attachments);
            } else {
                $attachment = $expense->attachments;
            }
            $expense->update([
                'branch_id'=>$branch,
                'name' => $expense_name??'',
                'date' => $expense_date??'',
                'expense_category_id' => $expense_category??'',
                'amount' => $amount??'',
                'vendor' => $vendor??"",
                'total_amount' => $total_amount??'',
//                'invoice_no' => $invoice_no??'',
                'tax' => $expense_tax??'',
                'is_recurring' => $recuring ?? '0',
                'time_cycle' => $time_cycle??null,
                'start_date' => $start_date??null,
                'end_date' => $end_date??null,
                'description' => $expense_description??'',
                'attachments'=>$attachment??'',
            ]);

            if ($expense) {
                return redirect(url('expense/expense'))->with(['title'=>'Done','message'=>'expense details updated successfully','type'=>'success']);
            }
            else{
                return redirect(url('expense/expense'))->with(['title'=>'Fail','message'=>'Unable to update expense details','type'=>'error']);
            }

        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('expense','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            Expense::destroy($id);

            return redirect('expense/expense')->with('flash_message', 'Expense deleted!');
        }
        return response(view('403'), 403);

    }
}
