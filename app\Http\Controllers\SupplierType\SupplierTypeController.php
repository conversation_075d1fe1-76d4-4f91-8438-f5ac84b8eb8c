<?php

namespace App\Http\Controllers\SupplierType;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\SupplierType;
use Illuminate\Http\Request;

class SupplierTypeController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('suppliertype','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $suppliertype = SupplierType::where('name', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $suppliertype = SupplierType::paginate($perPage);
            }

            return view('supplierType.supplier-type.index', compact('suppliertype'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('suppliertype','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('supplierType.supplier-type.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('suppliertype','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            SupplierType::create($requestData);
            return redirect('supplierType/supplier-type')->with('flash_message', 'SupplierType added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('suppliertype','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $suppliertype = SupplierType::findOrFail($id);
            return view('supplierType.supplier-type.show', compact('suppliertype'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('suppliertype','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $suppliertype = SupplierType::findOrFail($id);
            return view('supplierType.supplier-type.edit', compact('suppliertype'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('suppliertype','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $suppliertype = SupplierType::findOrFail($id);
             $suppliertype->update($requestData);

             return redirect('supplierType/supplier-type')->with('flash_message', 'SupplierType updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('suppliertype','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            SupplierType::destroy($id);

            return redirect('supplierType/supplier-type')->with('flash_message', 'SupplierType deleted!');
        }
        return response(view('403'), 403);

    }
}
