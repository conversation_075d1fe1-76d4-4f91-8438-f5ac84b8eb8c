<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use App\User;
use App\Profile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/dashboard';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function authenticated(Request $request, $user)
    {
        activity($user->name)
            ->performedOn($user)
            ->causedBy($user)
            ->log('LoggedIn');
          return redirect('dashboard');
    }

    public function logout(Request $request)
    {
        try{
            $user = auth()->user();
            $user->update(['remember_token' => null]);
            activity($user->name)
                ->performedOn($user)
                ->causedBy($user)
                ->log('LoggedOut');
            $this->guard()->logout();
            $request->session()->invalidate();
            return redirect('/');
        }catch(\Exception $e){
            return redirect('/');
        }
    }
    public function login(Request $request){
//        return $request->all();
        $credentials = $request->only('email_or_phone', 'password');
        $user = null;
        $user = User::where('email', $credentials['email_or_phone'])->orWhere('phone',$credentials['email_or_phone'])->first();
        if (!$user) {
            $profile = Profile::where('phone', $credentials['email_or_phone'])->first();
            if ($profile) {
                $user = $profile->user;
            }
        }
        if ($user && Hash::check($credentials['password'], $user->password)) {
            if($user->roles[0]->name=="spa_salon"){
                if($user->register_status=="Accepted"){
                    Auth::login($user);
                    $token = Str::random(60);
                    $user = Auth::user();
                    $user->update(['remember_token' => $token]);
                    return redirect('/dashboard');
                }elseif($user->register_status=="Hold"){
                    return redirect()->back()->with(['title'=>'Fail','message'=>'Please Login With Primary Branch','type'=>'error']);
                }elseif($user->register_status=="Pending"){
                    return redirect()->back()->with(['title'=>'Fail','message'=>trans('messages.Pending_Account_Message'),'type'=>'error']);
                }else{
                    return redirect()->back()->with(['title'=>'Fail','message'=>trans('messages.Rejected_Account_Message'),'type'=>'error']);
                }
            }else if($user->roles[0]->name=="customer"){
                if ($user->register_status != "Banned"){
                    Auth::login($user);
                    $token = Str::random(60);
                    $user = Auth::user();
                    $user->update(['remember_token' => $token]);
                    if($request->salon_id!=null){
                        $firstName = Auth::user()->first_name;
                        $lastName = Auth::user()->last_name;
                        $email = Auth::user()->email;
                        $phone = Auth::user()->profile->phone;
                        $dob = Auth::user()->profile->dob;
                        $address = Auth::user()->profile->address;
                        return response()->json(['login' => 'true','first_name' => $firstName,'last_name' => $lastName, 'email' => $email,'phone' => $phone,'dob' => $dob,'address' => $address]);
                    }else{
                        return redirect('/dashboard');
                    }
                }else{
                    return redirect()->back()->with(['title'=>'Fail','message'=>'Your account has been banned by ADMIN. Please contact support for further assistance.','type'=>'error']);
                }
            }else if($user->roles[0]->name=="employee"){
                if ($user->salon->register_status == "Accepted"){
                    Auth::login($user);
                    $token = Str::random(60);
                    $user = Auth::user();
                    $user->update(['remember_token' => $token]);
                    return redirect('/dashboard');
                }else{
                    return redirect()->back()->with(['title'=>'Fail','message'=>'Your Shop Account is Deactivate','type'=>'error']);
                }
            }else if($user->roles[0]->name=="cashier"){
                if ($user->salon->register_status == "Accepted"){
                    Auth::login($user);
                    $token = Str::random(60);
                    $user = Auth::user();
                    $user->update(['remember_token' => $token]);
                    return redirect('/dashboard');
                }else{
                    return redirect()->back()->with(['title'=>'Fail','message'=>'Your Shop Account is Deactivate','type'=>'error']);
                }
            }else{
                Auth::login($user);
                $token = Str::random(60);
                $user = Auth::user();
                $user->update(['remember_token' => $token]);
                return redirect('/dashboard');
            }
        }
        return redirect()->back()->withErrors(['loginError' => 'Invalid email/phone or password.']);
    }
}
