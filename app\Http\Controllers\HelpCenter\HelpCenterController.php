<?php

namespace App\Http\Controllers\HelpCenter;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\HelpCenter;
use Illuminate\Http\Request;

class HelpCenterController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('helpcenter','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $helpcenter = HelpCenter::where('title', 'LIKE', "%$keyword%")
                ->orWhere('video_link', 'LIKE', "%$keyword%")
                ->orWhere('status', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                $helpcenter = HelpCenter::paginate($perPage);
            }

            return view('helpCenter.help-center.index', compact('helpcenter'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('helpcenter','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('helpCenter.help-center.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('helpcenter','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            HelpCenter::create($requestData);
            return redirect('helpCenter/help-center')->with('flash_message', 'HelpCenter added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('helpcenter','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $helpcenter = HelpCenter::findOrFail($id);
            return view('helpCenter.help-center.show', compact('helpcenter'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('helpcenter','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $helpcenter = HelpCenter::findOrFail($id);
            return view('helpCenter.help-center.edit', compact('helpcenter'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('helpcenter','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            $helpcenter = HelpCenter::findOrFail($id);
             $helpcenter->update($requestData);

             return redirect('helpCenter/help-center')->with('flash_message', 'HelpCenter updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('helpcenter','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            HelpCenter::destroy($id);

            return redirect('helpCenter/help-center')->with('flash_message', 'HelpCenter deleted!');
        }
        return response(view('403'), 403);

    }
}
