<?php

use Faker\Factory;
use App\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Role;
use App\Permission;
use App\CommonSetting;
class AdminSeeder extends DatabaseSeeder
{

    public function run()
    {
        
        $user = User::firstOrCreate(array(
            'email' => '<EMAIL>',
            'name' => 'User'
        ));
        $user->password = bcrypt("123456");
        $user->save();

        if($user->profile == null){
            $profile = new \App\Profile();
            $profile->user_id = $user->id;
            $profile->save();
        }

        $admin_role = Role::firstOrcreate(['name' => 'admin']);
        $user_role = Role::firstOrcreate(['name' => 'user']);
        
        $permission = Permission::create(['name' => 'All Permission']);
        $admin_role->givePermissionTo($permission);
        $user->assignRole('user');
        $permission = Permission::create(['name' => 'add-commonsetting']);
        $permission = Permission::create(['name' => 'edit-commonsetting']);
        $permission = Permission::create(['name' => 'view-commonsetting']);
        $permission = Permission::create(['name' => 'delete-commonsetting']);

        CommonSetting::create(['title'=>'Update Title from admin','description'=>'Update Title from admin use for meta description tag', 'favicon'=>'AdminDashboard/default_logo.png', 'dashboard_logo'=>'AdminDashboard/default_logo.png', 'dashboard_logo_text'=>'AdminDashboard/default_logo_text.png', 'footer_text'=>date('Y').' © Update footer form admin.']);

        $this->command->info('Admin User created <NAME_EMAIL> and password admin');
    }

}