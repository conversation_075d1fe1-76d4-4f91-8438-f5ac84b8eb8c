<?php

namespace App\Http\Controllers\CustomerAppointment;

use App\Http\Controllers\Controller;
use App\Http\Requests;

use App\CustomerAppointment;
use App\SalonService;
use App\UserSubscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Auth;
use Mail;
use App\User;
class CustomerAppointmentController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('customerappointment','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $keyword = $request->get('search');
            $perPage = 25000000;
            if (!empty($keyword)) {
                $customerappointment = CustomerAppointment::where('customer_id', 'LIKE', "%$keyword%")
                ->orWhere('customer_appointment_date', 'LIKE', "%$keyword%")
                ->paginate($perPage);
            } else {
                if(Auth::user()->hasRole('spa_salon')) {
                    $currentDateApp = Carbon::now()->format('m/d/Y');
                    if ($request->status == "Upcoming"){
                        $title = "Upcoming";
                        $allBranches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                        $branchIds = $allBranches->pluck('id');
                        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                            ->orderBy('user_id', 'ASC')
                            ->orderBy('id', 'DESC')
                            ->get()
                            ->groupBy('user_id');
                        $currentDate = now();
                        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                            $dueDate = $subs->first()->fatoraPdf->due_date;
                            return $dueDate->isPast();
                        })->keys();
                        $customerappointment = CustomerAppointment::whereIn('salon_id', $branchIds)->whereNotIn('salon_id',$expiredUserIds)
                            ->where('customer_appointment_date', '>', $currentDateApp)
                            ->whereIn('status', ['Pending', 'Approved'])
                            ->orderByRaw("STR_TO_DATE(customer_appointment_date, '%m/%d/%Y') DESC")
                            ->paginate($perPage);
                        $employee = $employee = User::whereHas(
                            'roles', function ($q) {
                            $q->where('name', 'employee');
                        }
                        )->where('salon_id', Auth::id())->orderBy('id', 'DESC')->get();
                    }else if($request->status == "Current"){
                        $title = "Current";
                        $allBranches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                        $branchIds = $allBranches->pluck('id');
                        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                            ->orderBy('user_id', 'ASC')
                            ->orderBy('id', 'DESC')
                            ->get()
                            ->groupBy('user_id');
                        $currentDate = now();
                        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                            $dueDate = $subs->first()->fatoraPdf->due_date;
                            return $dueDate->isPast();
                        })->keys();
                        $customerappointment = CustomerAppointment::whereIn('salon_id', $branchIds)->whereNotIn('salon_id',$expiredUserIds)
                            ->where('customer_appointment_date', '<=', $currentDateApp)
                            ->whereIn('status', ['Pending', 'Approved'])
                            ->orderByRaw("STR_TO_DATE(customer_appointment_date, '%m/%d/%Y') DESC")
                            ->paginate($perPage);
                        $employee = $employee = User::whereHas(
                            'roles', function ($q) {
                            $q->where('name', 'employee');
                        }
                        )->where('salon_id', Auth::id())->orderBy('id', 'DESC')->get();
                    }else if($request->status == "Completed"){
                        $title = "Completed";
                        $allBranches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                        $branchIds = $allBranches->pluck('id');
                        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                            ->orderBy('user_id', 'ASC')
                            ->orderBy('id', 'DESC')
                            ->get()
                            ->groupBy('user_id');
                        $currentDate = now();
                        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                            $dueDate = $subs->first()->fatoraPdf->due_date;
                            return $dueDate->isPast();
                        })->keys();
                        $customerappointment = CustomerAppointment::whereIn('salon_id', $branchIds)
                            ->whereNotIn('salon_id', $expiredUserIds)
                            ->where('status', 'Complete')
                            ->orderByRaw("STR_TO_DATE(customer_appointment_date, '%m/%d/%Y') DESC")
                            ->paginate($perPage);
                        $employee = User::whereHas(
                            'roles', function ($q) {
                            $q->where('name', 'employee');
                        }
                        )->where('salon_id', Auth::id())->orderBy('id', 'DESC')->get();
                    }else if($request->status == "Cancel"){
                        $title = "Cancel";
                        $allBranches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                        $branchIds = $allBranches->pluck('id');
                        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                            ->orderBy('user_id', 'ASC')
                            ->orderBy('id', 'DESC')
                            ->get()
                            ->groupBy('user_id');
                        $currentDate = now();
                        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                            $dueDate = $subs->first()->fatoraPdf->due_date;
                            return $dueDate->isPast();
                        })->keys();
                        $customerappointment = CustomerAppointment::whereIn('salon_id', $branchIds)->whereNotIn('salon_id',$expiredUserIds)
                            ->where('status', 'Cancel')
                            ->orderByRaw("STR_TO_DATE(customer_appointment_date, '%m/%d/%Y') DESC")
                            ->paginate($perPage);
                        $employee = $employee = User::whereHas(
                            'roles', function ($q) {
                            $q->where('name', 'employee');
                        }
                        )->where('salon_id', Auth::id())->orderBy('id', 'DESC')->get();
                    }else{
                        $title = "All";
                        $allBranches = User::whereHas('roles', function ($query) {
                            $query->where('name', 'spa_salon');
                        })->where('salon_id', Auth::user()->salon_id)->orWhere('id', Auth::user()->salon_id)->orderBy('id','ASC')->get();
                        $branchIds = $allBranches->pluck('id');
                        $userSubscriptionsDueDate = UserSubscription::whereIn('user_id', $branchIds)
                            ->orderBy('user_id', 'ASC')
                            ->orderBy('id', 'DESC')
                            ->get()
                            ->groupBy('user_id');
                        $currentDate = now();
                        $expiredUserIds = $userSubscriptionsDueDate->filter(function ($subs) use ($currentDate) {
                            $dueDate = $subs->first()->fatoraPdf->due_date;
                            return $dueDate->isPast();
                        })->keys();
                        $customerappointment = CustomerAppointment::whereIn('salon_id', $branchIds)->whereNotIn('salon_id',$expiredUserIds)
                            ->orderBy('id','DESC')->paginate($perPage);
                        $employee = $employee = User::whereHas(
                            'roles', function ($q) {
                            $q->where('name', 'employee');
                        }
                        )->where('salon_id', Auth::id())->orderBy('id', 'DESC')->get();
                    }
                    $services = SalonService::where('salon_id', Auth::id())->get();
                }elseif (Auth::user()->hasRole('cashier')){
                    $customerappointment = CustomerAppointment::where('salon_id', Auth::user()->salon_id)->with(['startSlot','endSlot'])->orderBy('id','DESC')->paginate($perPage);
                    $employee = $employee = User::whereHas(
                        'roles', function ($q) {
                        $q->where('name', 'employee');
                    })->where('salon_id', Auth::user()->salon_id)->orderBy('id', 'DESC')->get();
                }
            }
            return view('customerAppointment.customer-appointment.index', compact('customerappointment','employee','allBranches','title','services'));
        }
        return response(view('403'), 403);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('customerappointment','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            return view('customerAppointment.customer-appointment.create');
        }
        return response(view('403'), 403);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {   
        $model = str_slug('customerappointment','-');
        if(auth()->user()->permissions()->where('name','=','add-'.$model)->first()!= null) {
            
            $requestData = $request->all();
            
            CustomerAppointment::create($requestData);
            return redirect('customerAppointment/customer-appointment')->with('flash_message', 'CustomerAppointment added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('customerappointment','-');
        if(auth()->user()->permissions()->where('name','=','view-'.$model)->first()!= null) {
            $customerappointment = CustomerAppointment::findOrFail($id);
            return view('customerAppointment.customer-appointment.show', compact('customerappointment'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('customerappointment','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $customerappointment = CustomerAppointment::findOrFail($id);
            return view('customerAppointment.customer-appointment.edit', compact('customerappointment'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('customerappointment','-');
        if(auth()->user()->permissions()->where('name','=','edit-'.$model)->first()!= null) {
            $requestData = $request->all();
            $customerAppointment = CustomerAppointment::findOrFail($id);
            $customerAppointment->update($requestData);
//            try{
                $status = $customerAppointment->status;
                $appointmentId = $customerAppointment->id;
                $customerName = $customerAppointment->customer->name;
                $customerEmail = $customerAppointment->customer->email;
                $salonId = $customerAppointment->salon->id;
                $salonName = $customerAppointment->salon->name;
                $salonEmail = $customerAppointment->salon->email;
                $salonPhone = $customerAppointment->salon->phone;
                $description = $customerAppointment->cancelation_description;
                $salonPic = $customerAppointment->salon->profile->pic;
                $date = $customerAppointment->customer_appointment_date ? date('d M Y', strtotime($customerAppointment->customer_appointment_date)) : '';
                $time = ($customerAppointment->startSlot->start_time ?? '') . ' - ' . ($customerAppointment->endSlot->end_time ?? '');
                $servicesIds = $customerAppointment->getCustomerServices ? $customerAppointment->getCustomerServices->pluck('salon_service_id')->toArray() : [];
                $services = SalonService::whereIn('id', $servicesIds)->pluck('name')->toArray();
                if($status == "Cancel") {
                    $data = array(
                        'status' => $status,
                        'appointmentId' => $appointmentId,
                        'customerName' => $customerName,
                        'customerEmail' => $customerEmail,
                        'salonId' => $salonId,
                        'salonName' => $salonName,
                        'salonEmail' => $salonEmail,
                        'salonPic' => $salonPic,
                        'salonPhone' => $salonPhone,
                        'description' => $description,
                        'date' => $date,
                        'time' => $time,
                        'site_url' => env('APP_URL'),
                        'site_contact' => env('APP_URL_CONTACT'),
                        'services' => implode(', ', $services),
                    );
                    $result = Mail::send('website.email_templates.appointment_cancel', ['data' => $data], function ($message) use ($data) {
                        $message->to($data['customerEmail'], $data['customerName'])->cc('<EMAIL>', 'Dev')->subject('Appointment Cancellation Notice.');;
                    });
                }else{
                    $result = Mail::raw("Hello $customerName, Your Appointment in $salonName Is $status!
                    ", function ($message) use ($customerEmail) {
                        $message->to($customerEmail)->cc('<EMAIL>', 'Dev')->subject('Appointment Request.');
                    });
                }
                $result = Mail::raw("Hello $salonName, Your Salon Customer Is $status Appointment!
                    Customer Name: $customerName
                    Customer Email: $customerEmail
                    Thanks.
                ", function ($message) use ($salonEmail) {
                    $message->to($salonEmail)->cc('<EMAIL>', 'Dev')->subject('Appointment Request.');
                });
                return back()->with('flash_message', 'CustomerAppointment has been cancelled!');
//            }catch(\Exception $e){
//                return back()->with(['message'=>'Your Creation successfully, but unable to send email.','type'=>'error','title'=>'Fail']);
//            }
            return back()->with('flash_message', 'CustomerAppointment updated!');
        }
        return response(view('403'), 403);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('customerappointment','-');
        if(auth()->user()->permissions()->where('name','=','delete-'.$model)->first()!= null) {
            CustomerAppointment::destroy($id);

            return redirect('customerAppointment/customer-appointment')->with('flash_message', 'CustomerAppointment deleted!');
        }
        return response(view('403'), 403);

    }
}
